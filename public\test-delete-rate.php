<?php
/**
 * Test Delete Service Rate Functionality
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

echo "<h1>Test Delete Service Rate</h1>";

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    echo "<p style='color: red;'>❌ You need to be logged in to test this functionality.</p>";
    echo "<p><a href='" . url('/login') . "'>Login here</a></p>";
    exit;
}

echo "<p>✅ User is logged in: " . htmlspecialchars($_SESSION['user']['username']) . "</p>";

// Test creating a temporary rate first
if (isset($_POST['create_test_rate'])) {
    try {
        $db = new \App\Core\Database();
        
        // Create a test rate
        $testRateData = [
            'service_code' => 'test.delete.rate',
            'service_name' => 'Test Delete Rate',
            'service_description' => 'This is a test rate for deletion testing',
            'rate' => 99.99,
            'unit' => 'each',
            'category' => 'processing',
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $rateId = $db->insert('billing_rates', $testRateData);
        echo "<p style='color: green;'>✅ Test rate created with ID: {$rateId}</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error creating test rate: " . $e->getMessage() . "</p>";
    }
}

// Get existing test rates
try {
    $db = new \App\Core\Database();
    $testRates = $db->fetchAll(
        "SELECT * FROM billing_rates WHERE service_code LIKE 'test.%' ORDER BY id DESC LIMIT 5"
    );
    
    echo "<h3>Available Test Rates for Deletion:</h3>";
    
    if (empty($testRates)) {
        echo "<p>No test rates found. Create one first.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Service Code</th><th>Service Name</th><th>Rate</th><th>Status</th><th>Actions</th></tr>";
        
        foreach ($testRates as $rate) {
            $status = $rate['is_active'] ? 'Active' : 'Inactive';
            $statusColor = $rate['is_active'] ? 'green' : 'gray';
            
            echo "<tr>";
            echo "<td>{$rate['id']}</td>";
            echo "<td><code>{$rate['service_code']}</code></td>";
            echo "<td>{$rate['service_name']}</td>";
            echo "<td>\${$rate['rate']}</td>";
            echo "<td style='color: {$statusColor};'>{$status}</td>";
            echo "<td>";
            
            // Delete form
            echo "<form method='POST' action='" . url('/app/billing/rates/delete/' . $rate['id']) . "' style='display: inline;' onsubmit='return confirm(\"Are you sure you want to delete this test rate?\");'>";
            echo "<button type='submit' style='background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>Delete</button>";
            echo "</form>";
            
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error fetching test rates: " . $e->getMessage() . "</p>";
}

?>

<hr>

<h3>Create Test Rate for Deletion</h3>
<form method="POST" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
    <p>Create a test service rate that you can safely delete:</p>
    
    <button type="submit" name="create_test_rate" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        Create Test Rate
    </button>
</form>

<h3>Test Delete Functionality</h3>
<ol>
    <li><strong>Create a test rate</strong> using the form above</li>
    <li><strong>Delete the test rate</strong> using the delete button in the table</li>
    <li><strong>Verify deletion</strong> by refreshing this page</li>
</ol>

<h3>Safety Features</h3>
<ul>
    <li>✅ <strong>Confirmation dialog</strong> - Asks for confirmation before deletion</li>
    <li>✅ <strong>Usage check</strong> - Prevents deletion if rate is used in billing events</li>
    <li>✅ <strong>Error handling</strong> - Shows appropriate error messages</li>
    <li>✅ <strong>Flash messages</strong> - Success/error notifications after deletion</li>
</ul>

<hr>
<p><a href="<?= url('/app/billing/rates') ?>">← Back to Service Rates</a></p>

<script>
// Auto-refresh every 5 seconds to show updates
setTimeout(function() {
    location.reload();
}, 5000);
</script>
