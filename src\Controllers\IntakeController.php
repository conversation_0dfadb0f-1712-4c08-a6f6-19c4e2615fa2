<?php

namespace App\Controllers;

use App\Services\BillingEventLogger;

/**
 * Intake Controller - Document Intake Management
 * 
 * Handles the initial document reception and processing workflow
 * Documents flow: Intake → Bundle → Box → Final Storage
 */
class IntakeController extends BaseController
{
    /**
     * Display intake queue/dashboard
     */
    public function index()
    {
        $this->requireAuth();
        
        try {
            // Get intake statistics
            $stats = $this->getIntakeStats();
            
            // Get pending intake items
            $pendingIntakes = $this->getPendingIntakes();
            
            // Get recent intake activity
            $recentActivity = $this->getRecentIntakeActivity();

            $this->view('intake/index', [
                'title' => 'Document Intake',
                'stats' => $stats,
                'pendingIntakes' => $pendingIntakes,
                'recentActivity' => $recentActivity
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading intake dashboard: ' . $e->getMessage(), 'error');
            $this->redirect('/dashboard');
        }
    }

    /**
     * Create new intake entry
     */
    public function create()
    {
        $this->requireAuth();
        
        // Get available sources and types
        $sources = $this->getIntakeSources();
        $documentTypes = $this->getDocumentTypes();
        
        $this->view('intake/create', [
            'title' => 'New Intake Entry',
            'sources' => $sources,
            'documentTypes' => $documentTypes
        ]);
    }

    /**
     * Store new intake entry
     */
    public function store()
    {
        $this->requireAuth();

        try {
            // Validate input according to documentation requirements
            $data = $this->validate($_POST, [
                'client_name' => 'required|max:255', // As per documentation
                'client_id' => 'max:50', // As per documentation
                'source' => 'required|max:100', // Updated to match VARCHAR(100)
                'document_type' => 'required|max:100', // Updated to match VARCHAR(100)
                'description' => 'required|max:500',
                'priority' => 'required|in:low,medium,high,urgent',
                'expected_count' => 'required|integer|min:1',
                'actual_count' => 'integer|min:0',
                'sensitivity_level' => 'in:public,internal,confidential,restricted', // As per documentation
                'department' => 'max:100', // As per documentation
                'notes' => 'max:1000'
            ]);

            // Create intake entry first without reference number
            $intakeId = $this->db->execute(
                "INSERT INTO document_intake (
                    company_id, client_name, client_id, source, document_type,
                    description, priority, expected_count, actual_count,
                    sensitivity_level, department, notes, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $data['client_name'],
                    $data['client_id'] ?? null,
                    $data['source'],
                    $data['document_type'],
                    $data['description'],
                    $data['priority'],
                    $data['expected_count'],
                    $data['actual_count'] ?? 0,
                    $data['sensitivity_level'] ?? 'internal',
                    $data['department'] ?? null,
                    $data['notes'] ?? null,
                    $this->user['id']
                ]
            );

            // Generate intake reference number
            $reference = 'INT-' . date('Y') . '-' . str_pad($intakeId, 6, '0', STR_PAD_LEFT);
            $this->db->execute(
                "UPDATE document_intake SET reference_number = ? WHERE id = ?",
                [$reference, $intakeId]
            );

            // Save custom source and type if they're new
            $this->saveCustomSource($data['source']);
            $this->saveCustomDocumentType($data['document_type']);

            // Log activity
            $this->logActivity('create', 'intake', $intakeId, "Created intake entry: {$reference}");

            // Log billing event for new intake
            if (!empty($data['client_id'])) {
                BillingEventLogger::logIntakeCreated(
                    $data['client_id'],
                    $intakeId,
                    $reference
                );
            }

            $this->setFlashMessage('Intake entry created successfully', 'success');
            $this->redirect('/app/intake');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create intake entry: ' . $e->getMessage(), 'error');
            $this->redirect('/app/intake/create');
        }
    }

    /**
     * Show intake details
     */
    public function show($id)
    {
        $this->requireAuth();
        
        try {
            $intake = $this->getIntakeById($id);
            if (!$intake) {
                $this->setFlashMessage('Intake entry not found', 'error');
                $this->redirect('/app/intake');
                return;
            }

            // Get associated documents
            $documents = $this->getIntakeDocuments($id);
            
            // Get available bundles for assignment
            $bundles = $this->getAvailableBundles();

            // Get categories for dropdown
            $categories = $this->getCategories();

            // Get document types for dropdown
            $documentTypes = $this->getDocumentTypes();

            // Get sorting categories for dropdown
            $sortingCategories = $this->getSortingCategories();

            $this->view('intake/show', [
                'title' => 'Intake Details - ' . $intake['reference_number'],
                'intake' => $intake,
                'documents' => $documents,
                'bundles' => $bundles,
                'categories' => $categories,
                'documentTypes' => $documentTypes,
                'sortingCategories' => $sortingCategories
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading intake details: ' . $e->getMessage(), 'error');
            $this->redirect('/app/intake');
        }
    }

    /**
     * Process intake - assign to bundle
     */
    public function process($id)
    {
        $this->requireAuth();

        try {
            $intake = $this->getIntakeById($id);
            if (!$intake) {
                throw new \Exception('Intake entry not found');
            }

            if ($intake['status'] !== 'pending') {
                throw new \Exception('Intake entry is not in pending status');
            }

            // Determine if creating new bundle or using existing
            $bundleOption = $_POST['bundle_option'] ?? 'existing';
            $bundleId = null;

            if ($bundleOption === 'new') {
                // Validate new bundle data with all fields
                $bundleData = $this->validate($_POST, [
                    'new_bundle_name' => 'required|max:255',
                    'new_bundle_description' => 'max:1000',
                    'new_bundle_category' => 'max:100',
                    'new_bundle_priority' => 'in:low,medium,high,urgent',
                    'new_bundle_document_type' => 'max:100',
                    'new_bundle_year' => 'integer|min:1900|max:2100',
                    'new_bundle_department' => 'max:100',
                    'new_bundle_pages_volume' => 'integer|min:1',
                    'new_bundle_confidentiality_flag' => 'boolean',
                    'new_bundle_retention_period' => 'integer|min:1|max:100',
                    'new_bundle_access_level' => 'in:public,private,restricted',
                    'new_bundle_contents_summary' => 'max:2000',
                    'processing_notes' => 'max:1000'
                ]);

                // Create new bundle with comprehensive data
                $bundleId = $this->createBundle([
                    'name' => $bundleData['new_bundle_name'],
                    'description' => $bundleData['new_bundle_description'] ?? null,
                    'category' => $bundleData['new_bundle_category'] ?? 'general',
                    'priority' => $bundleData['new_bundle_priority'] ?? 'medium',
                    'document_type' => $bundleData['new_bundle_document_type'] ?? null,
                    'year' => $bundleData['new_bundle_year'] ?? date('Y'),
                    'department' => $bundleData['new_bundle_department'] ?? null,
                    'pages_volume' => $bundleData['new_bundle_pages_volume'] ?? null,
                    'confidentiality_flag' => isset($bundleData['new_bundle_confidentiality_flag']) ? 1 : 0,
                    'retention_period' => $bundleData['new_bundle_retention_period'] ?? 7,
                    'access_level' => $bundleData['new_bundle_access_level'] ?? 'private',
                    'contents_summary' => $bundleData['new_bundle_contents_summary'] ?? null,
                    'intake_id' => $id
                ]);

                $processingNotes = $bundleData['processing_notes'] ?? null;
            } else {
                // Validate existing bundle selection
                $validatedData = $this->validate($_POST, [
                    'bundle_id' => 'required|integer',
                    'processing_notes' => 'max:1000'
                ]);

                $bundleId = $validatedData['bundle_id'];
                $processingNotes = $validatedData['processing_notes'] ?? null;
            }

            // Generate unique intake code for physical documents
            $intakeCode = $this->generatePhysicalIntakeCode($intake);

            // Handle digitization option
            $digitizationRequired = isset($_POST['digitization_required']) ? (bool)$_POST['digitization_required'] : false;

            // Handle box creation if requested
            $boxId = null;
            if (isset($_POST['assign_box']) && $_POST['assign_box'] === 'yes') {
                $boxOption = $_POST['box_option'] ?? 'existing';

                if ($boxOption === 'new') {
                    // Validate new box data
                    $boxData = $this->validate($_POST, [
                        'box_name' => 'required|max:255',
                        'box_identifier' => 'required|max:100',
                        'warehouse_id' => 'required|integer',
                        'storage_type' => 'required|in:physical,online',
                        'capacity' => 'integer|min:1|max:10000',
                        'box_barcode' => 'max:255',
                        'box_description' => 'max:1000'
                    ]);

                    // Create the new box
                    $boxId = $this->createBox($boxData, $bundleId);
                } elseif ($boxOption === 'existing') {
                    // Validate existing box selection
                    $boxSelection = $this->validate($_POST, [
                        'box_id' => 'required|integer'
                    ]);
                    $boxId = $boxSelection['box_id'];
                }
            }

            // Following INTAKE → BUNDLE → BOX → STORAGE workflow
            // Intake links to bundles, bundles link to boxes via junction table

            // Update intake status with enhanced tracking
            $this->db->execute(
                "UPDATE document_intake SET
                 status = 'processing',
                 bundle_id = ?,
                 physical_intake_code = ?,
                 digitization_required = ?,
                 processing_notes = ?,
                 processed_by = ?,
                 processed_at = NOW(),
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [
                    $bundleId,
                    $intakeCode,
                    $digitizationRequired ? 1 : 0,
                    $processingNotes,
                    $this->user['id'],
                    $id,
                    $this->user['company_id']
                ]
            );

            // Update associated documents
            $this->db->execute(
                "UPDATE documents SET bundle_id = ?, updated_at = NOW()
                 WHERE intake_id = ? AND company_id = ?",
                [$bundleId, $id, $this->user['company_id']]
            );

            // CRITICAL: Link bundle to box using proper junction table
            if ($boxId) {
                $this->linkBundleToBox($bundleId, $boxId);

                // Handle storage location assignment
                $row = $_POST['storage_row'] ?? '';
                $shelf = $_POST['storage_shelf'] ?? '';
                $position = $_POST['storage_position'] ?? '';

                if (!empty($row) && !empty($shelf) && !empty($position)) {
                    $warehouseId = $_POST['warehouse_id'] ?? 1;
                    $storageLocationCode = $this->generateStorageLocationCode($warehouseId, $row, $shelf, $position);

                    // Update box with storage location
                    $this->db->execute(
                        "UPDATE boxes SET storage_location_code = ?, updated_at = NOW() WHERE id = ?",
                        [$storageLocationCode, $boxId]
                    );

                    // Log storage assignment
                    $this->logActivity('assign_storage', 'box', $boxId, "Assigned storage location: {$storageLocationCode}");
                }
            }

            // Generate barcode for intake if requested
            if (isset($_POST['generate_barcode']) && $_POST['generate_barcode'] === 'yes') {
                $this->generateIntakeBarcode($id, $intakeCode);
            }

            // Log enhanced activity
            $activityMessage = $bundleOption === 'new'
                ? "Processed intake: {$intake['reference_number']} with code: {$intakeCode} and created new bundle"
                : "Processed intake: {$intake['reference_number']} with code: {$intakeCode}";
            $this->logIntakeActivity($id, 'process', $activityMessage);

            $this->setFlashMessage('Intake processed successfully with code: ' . $intakeCode, 'success');
            $this->redirect("/app/intake/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to process intake: ' . $e->getMessage(), 'error');
            $this->redirect("/app/intake/{$id}");
        }
    }

    /**
     * Update intake status (simple status change)
     */
    public function updateStatus($id)
    {
        $this->requireAuth();

        try {
            $intake = $this->getIntakeById($id);
            if (!$intake) {
                throw new \Exception('Intake entry not found');
            }

            if ($intake['status'] !== 'pending') {
                throw new \Exception('Intake entry is not in pending status');
            }

            // Update intake status to processing
            $this->db->execute(
                "UPDATE document_intake SET
                 status = 'processing',
                 processed_by = ?,
                 processed_at = NOW(),
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [
                    $this->user['id'],
                    $id,
                    $this->user['company_id']
                ]
            );

            // Log activity
            $this->logActivity('status_update', 'intake', $id, "Changed status to processing: {$intake['reference_number']}");

            $this->setFlashMessage('Intake status updated to processing', 'success');
            $this->redirect('/app/intake');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to update intake status: ' . $e->getMessage(), 'error');
            $this->redirect('/app/intake');
        }
    }

    /**
     * Complete intake processing
     */
    public function complete($id)
    {
        $this->requireAuth();

        try {
            $intake = $this->getIntakeById($id);
            if (!$intake) {
                throw new \Exception('Intake entry not found');
            }

            if ($intake['status'] !== 'processing') {
                throw new \Exception('Intake entry is not in processing status');
            }

            // Validate input
            $validatedData = $this->validate($_POST, [
                'completion_notes' => 'max:1000'
            ]);

            // Update intake status
            $this->db->execute(
                "UPDATE document_intake SET
                 status = 'completed',
                 completion_notes = ?,
                 completed_by = ?,
                 completed_at = NOW(),
                 updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [
                    $validatedData['completion_notes'] ?? null,
                    $this->user['id'],
                    $id,
                    $this->user['company_id']
                ]
            );

            // Log activity
            $this->logActivity('complete', 'intake', $id, "Completed intake: {$intake['reference_number']}");

            $this->setFlashMessage('Intake completed successfully', 'success');
            $this->redirect('/app/intake');

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to complete intake: ' . $e->getMessage(), 'error');
            $this->redirect("/app/intake/{$id}");
        }
    }

    /**
     * Get intake statistics
     */
    private function getIntakeStats()
    {
        $stats = [
            'pending' => 0,
            'processing' => 0,
            'completed_today' => 0,
            'total_this_month' => 0
        ];

        try {
            // Pending intakes
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'",
                [$this->user['company_id']]
            );
            $stats['pending'] = $result['count'] ?? 0;

            // Processing intakes
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'processing'",
                [$this->user['company_id']]
            );
            $stats['processing'] = $result['count'] ?? 0;

            // Completed today
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'completed' 
                 AND DATE(completed_at) = CURDATE()",
                [$this->user['company_id']]
            );
            $stats['completed_today'] = $result['count'] ?? 0;

            // Total this month
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND MONTH(created_at) = MONTH(CURDATE()) 
                 AND YEAR(created_at) = YEAR(CURDATE())",
                [$this->user['company_id']]
            );
            $stats['total_this_month'] = $result['count'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get pending intake items
     */
    private function getPendingIntakes()
    {
        return $this->db->fetchAll(
            "SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20",
            [$this->user['company_id']]
        );
    }

    /**
     * Get recent intake activity
     */
    private function getRecentIntakeActivity()
    {
        return $this->db->fetchAll(
            "SELECT di.*, u.first_name, u.last_name
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             WHERE di.company_id = ? 
             ORDER BY di.updated_at DESC
             LIMIT 10",
            [$this->user['company_id']]
        );
    }

    /**
     * Get intake by ID
     */
    private function getIntakeById($id)
    {
        return $this->db->fetch(
            "SELECT di.*, 
                    u1.first_name as creator_first_name, u1.last_name as creator_last_name,
                    u2.first_name as processor_first_name, u2.last_name as processor_last_name,
                    u3.first_name as completer_first_name, u3.last_name as completer_last_name,
                    b.name as bundle_name
             FROM document_intake di
             LEFT JOIN users u1 ON di.created_by = u1.id
             LEFT JOIN users u2 ON di.processed_by = u2.id
             LEFT JOIN users u3 ON di.completed_by = u3.id
             LEFT JOIN bundles b ON di.bundle_id = b.id
             WHERE di.id = ? AND di.company_id = ?",
            [$id, $this->user['company_id']]
        );
    }

    /**
     * Get documents associated with intake
     */
    private function getIntakeDocuments($intakeId)
    {
        return $this->db->fetchAll(
            "SELECT d.*, u.first_name, u.last_name
             FROM documents d
             LEFT JOIN users u ON d.created_by = u.id
             WHERE d.intake_id = ? AND d.company_id = ?
             ORDER BY d.created_at DESC",
            [$intakeId, $this->user['company_id']]
        );
    }

    /**
     * Get available bundles for assignment
     */
    private function getAvailableBundles()
    {
        return $this->db->fetchAll(
            "SELECT id, name, description, status
             FROM bundles
             WHERE company_id = ? AND status = 'open'
             ORDER BY name",
            [$this->user['company_id']]
        );
    }

    /**
     * Create a new bundle
     */
    private function createBundle($data)
    {
        try {
            // Validate that name is not empty
            if (empty($data['name']) || trim($data['name']) === '') {
                throw new \Exception('Bundle name cannot be empty');
            }

            // Ensure name is properly trimmed
            $data['name'] = trim($data['name']);

            // Generate reference number
            $reference = $this->generateBundleReference($data['name']);

            // Check if the extended columns exist in the bundles table
            $hasExtendedColumns = $this->checkBundleTableColumns();

            if ($hasExtendedColumns) {
                // Create bundle with extended columns including new fields
                $bundleId = $this->db->execute(
                    "INSERT INTO bundles (
                        company_id, name, description, reference_number, category, priority,
                        retention_period, access_level, document_type, year, department,
                        pages_volume, confidentiality_flag, contents_summary,
                        status, created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'open', ?, NOW(), NOW())",
                    [
                        $this->user['company_id'],
                        $data['name'],
                        $data['description'],
                        $reference,
                        $data['category'] ?? 'general',
                        $data['priority'] ?? 'medium',
                        $data['retention_period'] ?? 7,
                        $data['access_level'] ?? 'private',
                        $data['document_type'],
                        $data['year'] ?? date('Y'),
                        $data['department'],
                        $data['pages_volume'],
                        $data['confidentiality_flag'] ?? 0,
                        $data['contents_summary'],
                        $this->user['id']
                    ]
                );
            } else {
                // Create bundle with basic columns only
                $bundleId = $this->db->execute(
                    "INSERT INTO bundles (
                        company_id, name, description, reference_number, category, priority,
                        retention_period, access_level, status, created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', ?, NOW(), NOW())",
                    [
                        $this->user['company_id'],
                        $data['name'],
                        $data['description'],
                        $reference,
                        $data['category'] ?? 'general',
                        $data['priority'] ?? 'medium',
                        7, // Default retention period
                        'private', // Default access level
                        $this->user['id']
                    ]
                );
            }

            // Log activity
            $this->logActivity('create', 'bundle', $bundleId, "Created bundle: {$data['name']}");

            return $bundleId;

        } catch (\Exception $e) {
            throw new \Exception('Failed to create bundle: ' . $e->getMessage());
        }
    }

    /**
     * Check if bundles table has extended columns
     */
    private function checkBundleTableColumns()
    {
        try {
            $result = $this->db->fetch("SHOW COLUMNS FROM bundles LIKE 'document_type'");
            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Create a new box (REWRITTEN - No direct bundle_id)
     */
    private function createBox($data, $bundleId = null)
    {
        try {
            // Validate required fields
            if (empty($data['box_name']) || empty($data['box_identifier'])) {
                throw new \Exception('Box name and identifier are required');
            }

            // Check if box identifier already exists
            $existing = $this->db->fetch(
                "SELECT id FROM boxes WHERE box_id = ? AND company_id = ?",
                [$data['box_identifier'], $this->user['company_id']]
            );

            if ($existing) {
                throw new \Exception('Box identifier already exists. Please choose a different identifier.');
            }

            // Extract client prefix from box identifier or generate one
            $boxIdentifier = $data['box_identifier'];
            if (preg_match('/^([A-Z0-9]+)-/', $boxIdentifier, $matches)) {
                $clientPrefix = $matches[1];
            } else {
                // Generate client prefix from box name or use default
                $clientPrefix = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $data['box_name']), 0, 6));
                if (strlen($clientPrefix) < 3) {
                    $clientPrefix = 'CLIENT01'; // Default prefix
                }
            }

            // Get next box number for this client prefix
            $result = $this->db->fetch(
                "SELECT COALESCE(MAX(box_number), 0) + 1 as next_number FROM boxes WHERE client_prefix = ? AND company_id = ?",
                [$clientPrefix, $this->user['company_id']]
            );
            $boxNumber = $result['next_number'] ?? 1;

            // Generate barcode if not provided
            $barcode = !empty($data['box_barcode']) ? $data['box_barcode'] : $this->generateBoxBarcode($data['box_identifier']);

            // Create the box WITHOUT bundle_id (using junction table only)
            $boxId = $this->db->execute(
                "INSERT INTO boxes (
                    company_id, warehouse_id, box_id, client_prefix, box_number, name, description,
                    storage_type, capacity, current_count, barcode_value,
                    status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 'empty', ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $data['warehouse_id'],
                    $data['box_identifier'],
                    $clientPrefix,
                    $boxNumber,
                    $data['box_name'],
                    $data['box_description'] ?? null,
                    $data['storage_type'],
                    $data['capacity'] ?? 100,
                    $barcode,
                    $this->user['id']
                ]
            );

            // Log activity
            $this->logActivity('create', 'box', $boxId, "Created box: {$data['box_name']} ({$data['box_identifier']})");

            return $boxId;

        } catch (\Exception $e) {
            throw new \Exception('Failed to create box: ' . $e->getMessage());
        }
    }

    /**
     * Link bundle to box using proper junction table (REWRITTEN)
     */
    private function linkBundleToBox($bundleId, $boxId)
    {
        try {
            // Check if relationship already exists
            $existingRelation = $this->db->fetch(
                "SELECT id FROM box_bundles WHERE box_id = ? AND bundle_id = ?",
                [$boxId, $bundleId]
            );

            if (!$existingRelation) {
                // Get next position in box
                $position = $this->db->fetchColumn(
                    "SELECT COALESCE(MAX(position_in_box), 0) + 1 FROM box_bundles WHERE box_id = ?",
                    [$boxId]
                ) ?? 1;

                // Create bundle-box relationship in junction table
                $this->db->execute(
                    "INSERT INTO box_bundles (box_id, bundle_id, position_in_box, added_by, added_at) VALUES (?, ?, ?, ?, NOW())",
                    [$boxId, $bundleId, $position, $this->user['id']]
                );

                // Update box status and count
                $this->updateBoxStatusAndCount($boxId);

                // Log the relationship creation
                $this->logActivity('link', 'bundle_box', $boxId, "Linked bundle {$bundleId} to box {$boxId}");
            }
        } catch (\Exception $e) {
            throw new \Exception('Failed to link bundle to box: ' . $e->getMessage());
        }
    }

    /**
     * Update box status and document count based on actual content
     */
    private function updateBoxStatusAndCount($boxId)
    {
        try {
            // Count documents in all bundles in this box
            $documentCount = $this->db->fetchColumn(
                "SELECT COUNT(d.id)
                 FROM box_bundles bb
                 JOIN bundles b ON bb.bundle_id = b.id
                 LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
                 WHERE bb.box_id = ?",
                [$boxId]
            ) ?? 0;

            // Get box capacity
            $box = $this->db->fetch(
                "SELECT capacity FROM boxes WHERE id = ?",
                [$boxId]
            );
            $capacity = $box['capacity'] ?? 100;

            // Determine status based on content
            $status = 'empty';
            if ($documentCount > 0) {
                if ($documentCount >= $capacity) {
                    $status = 'full';
                } else {
                    $status = 'partial';
                }
            }

            // Update box
            $this->db->execute(
                "UPDATE boxes SET current_count = ?, status = ?, updated_at = NOW() WHERE id = ?",
                [$documentCount, $status, $boxId]
            );

        } catch (\Exception $e) {
            // Log error but don't fail the operation
            error_log("Failed to update box status and count: " . $e->getMessage());
        }
    }

    /**
     * Generate box barcode
     */
    private function generateBoxBarcode($boxIdentifier)
    {
        // Create a barcode based on box identifier and timestamp
        $timestamp = time();
        $hash = substr(md5($boxIdentifier . $timestamp), 0, 8);
        return strtoupper($boxIdentifier . '-' . $hash);
    }



    /**
     * Generate bundle reference number
     */
    private function generateBundleReference($name)
    {
        try {
            // Create prefix from bundle name
            $prefix = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 3));
            if (strlen($prefix) < 3) {
                $prefix = str_pad($prefix, 3, 'B', STR_PAD_RIGHT);
            }

            // Get next bundle number
            $bundleCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM bundles WHERE company_id = ?",
                [$this->user['company_id']]
            );

            $bundleNumber = str_pad($bundleCount + 1, 4, '0', STR_PAD_LEFT);
            $year = date('Y');

            return $prefix . '-' . $year . '-' . $bundleNumber;

        } catch (\Exception $e) {
            // Fallback reference
            return 'BUN-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        }
    }

    /**
     * Get intake sources (including custom ones)
     */
    private function getIntakeSources()
    {
        // Default sources
        $defaultSources = [
            'Email',
            'Physical Mail',
            'Scanned Documents',
            'Direct Upload',
            'Fax',
            'Courier Delivery',
            'Walk-in Delivery',
            'Other'
        ];

        // Get custom sources from database
        $customSources = [];
        try {
            $customSourcesData = $this->db->fetchAll(
                "SELECT source_name FROM custom_document_sources
                 WHERE company_id = ? AND is_active = 1
                 ORDER BY usage_count DESC, source_name ASC",
                [$this->user['company_id']]
            );

            foreach ($customSourcesData as $source) {
                $customSources[] = $source['source_name'];
            }
        } catch (\Exception $e) {
            // Continue with default sources if custom sources fail
        }

        // Combine and return unique sources
        $allSources = array_unique(array_merge($defaultSources, $customSources));

        // Convert to associative array for backward compatibility
        $sources = [];
        foreach ($allSources as $source) {
            $key = strtolower(str_replace(' ', '_', $source));
            $sources[$key] = $source;
        }

        return $sources;
    }

    /**
     * Get categories for dropdowns
     */
    private function getCategories()
    {
        // Default categories
        $defaultCategories = [
            'general' => 'General',
            'financial' => 'Financial',
            'legal' => 'Legal',
            'hr' => 'Human Resources',
            'marketing' => 'Marketing',
            'technical' => 'Technical',
            'correspondence' => 'Correspondence',
            'reports' => 'Reports',
            'contracts' => 'Contracts',
            'invoices' => 'Invoices'
        ];

        // Get custom categories from database
        $customCategories = [];
        try {
            $customCategoriesData = $this->db->fetchAll(
                "SELECT name, description FROM categories
                 WHERE company_id = ? AND (status = 'active' OR is_active = 1)
                 ORDER BY name ASC",
                [$this->user['company_id']]
            );

            foreach ($customCategoriesData as $category) {
                $key = strtolower(str_replace(' ', '_', $category['name']));
                $customCategories[$key] = $category['name'];
            }
        } catch (\Exception $e) {
            // Continue with default categories if custom categories fail
        }

        // Merge default and custom categories
        return array_merge($defaultCategories, $customCategories);
    }

    /**
     * Get document types (including custom ones)
     */
    private function getDocumentTypes()
    {
        // Default types
        $defaultTypes = [
            'Contract',
            'Invoice',
            'Receipt',
            'Report',
            'Correspondence',
            'Legal Document',
            'Financial Document',
            'HR Document',
            'Technical Document',
            'Other'
        ];

        // Get custom types from database
        $customTypes = [];
        try {
            $customTypesData = $this->db->fetchAll(
                "SELECT type_name FROM custom_document_types
                 WHERE company_id = ? AND is_active = 1
                 ORDER BY usage_count DESC, type_name ASC",
                [$this->user['company_id']]
            );

            foreach ($customTypesData as $type) {
                $customTypes[] = $type['type_name'];
            }
        } catch (\Exception $e) {
            // Continue with default types if custom types fail
        }

        // Combine and return unique types
        $allTypes = array_unique(array_merge($defaultTypes, $customTypes));

        // Convert to associative array for backward compatibility
        $types = [];
        foreach ($allTypes as $type) {
            $key = strtolower(str_replace(' ', '_', $type));
            $types[$key] = $type;
        }

        return $types;
    }

    /**
     * Get sorting categories
     */
    private function getSortingCategories()
    {
        try {
            return $this->db->fetchAll(
                "SELECT category_code, category_name, emoji, color_code
                 FROM document_sorting_categories
                 WHERE company_id = ? AND is_active = 1
                 ORDER BY sort_order, category_name",
                [$this->user['company_id']]
            );
        } catch (\Exception $e) {
            // Return default categories if database query fails
            return [
                ['category_code' => 'HR', 'category_name' => 'Human Resources', 'emoji' => '🏢', 'color_code' => '#10B981'],
                ['category_code' => 'FIN', 'category_name' => 'Finance', 'emoji' => '💰', 'color_code' => '#F59E0B'],
                ['category_code' => 'LEG', 'category_name' => 'Legal', 'emoji' => '⚖️', 'color_code' => '#EF4444'],
                ['category_code' => 'OPS', 'category_name' => 'Operations', 'emoji' => '🔧', 'color_code' => '#8B5CF6'],
                ['category_code' => 'MKT', 'category_name' => 'Marketing', 'emoji' => '📢', 'color_code' => '#EC4899'],
                ['category_code' => 'GEN', 'category_name' => 'General', 'emoji' => '📁', 'color_code' => '#6B7280']
            ];
        }
    }

    /**
     * Generate intake reference number according to documentation
     * Format: CLIENT01-INT-001 (includes client ID, intake date, and document type)
     */
    private function generateIntakeReference($clientName)
    {
        try {
            // Create client prefix from name
            $clientPrefix = $this->createClientPrefix($clientName);

            // Get next intake number for this client
            $intakeCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM document_intake WHERE client_name = ? AND company_id = ?",
                [$clientName, $this->user['company_id']]
            );

            $intakeNumber = str_pad($intakeCount + 1, 3, '0', STR_PAD_LEFT);
            $dateCode = date('Ymd');

            return $clientPrefix . '-INT-' . $intakeNumber;

        } catch (\Exception $e) {
            // Fallback reference
            return 'INT-' . date('Y') . '-' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
        }
    }

    /**
     * Create client prefix from client name
     */
    private function createClientPrefix($clientName)
    {
        // Remove special characters and convert to uppercase
        $cleaned = preg_replace('/[^A-Za-z0-9]/', '', $clientName);
        $cleaned = strtoupper($cleaned);

        // Take first 6 characters or pad with zeros
        if (strlen($cleaned) >= 6) {
            return substr($cleaned, 0, 6);
        } else {
            return str_pad($cleaned, 6, '0', STR_PAD_RIGHT);
        }
    }

    /**
     * Save custom document source if it's new
     */
    private function saveCustomSource($sourceName)
    {
        try {
            // Check if this is a default source
            $defaultSources = ['Email', 'Physical Mail', 'Scanned Documents', 'Direct Upload', 'Fax', 'Courier Delivery', 'Walk-in Delivery', 'Other'];
            if (in_array($sourceName, $defaultSources)) {
                return; // Don't save default sources
            }

            // Check if custom source already exists
            $existing = $this->db->fetch(
                "SELECT id, usage_count FROM custom_document_sources
                 WHERE company_id = ? AND source_name = ?",
                [$this->user['company_id'], $sourceName]
            );

            if ($existing) {
                // Increment usage count
                $this->db->execute(
                    "UPDATE custom_document_sources SET usage_count = usage_count + 1, updated_at = NOW()
                     WHERE id = ?",
                    [$existing['id']]
                );
            } else {
                // Create new custom source
                $this->db->execute(
                    "INSERT INTO custom_document_sources (company_id, source_name, created_by, created_at, updated_at)
                     VALUES (?, ?, ?, NOW(), NOW())",
                    [$this->user['company_id'], $sourceName, $this->user['id']]
                );
            }
        } catch (\Exception $e) {
            // Silently fail - don't break the intake process
        }
    }

    /**
     * Save custom document type if it's new
     */
    private function saveCustomDocumentType($typeName)
    {
        try {
            // Check if this is a default type
            $defaultTypes = ['Contract', 'Invoice', 'Receipt', 'Report', 'Correspondence', 'Legal Document', 'Financial Document', 'HR Document', 'Technical Document', 'Other'];
            if (in_array($typeName, $defaultTypes)) {
                return; // Don't save default types
            }

            // Check if custom type already exists
            $existing = $this->db->fetch(
                "SELECT id, usage_count FROM custom_document_types
                 WHERE company_id = ? AND type_name = ?",
                [$this->user['company_id'], $typeName]
            );

            if ($existing) {
                // Increment usage count
                $this->db->execute(
                    "UPDATE custom_document_types SET usage_count = usage_count + 1, updated_at = NOW()
                     WHERE id = ?",
                    [$existing['id']]
                );
            } else {
                // Create new custom type
                $this->db->execute(
                    "INSERT INTO custom_document_types (company_id, type_name, created_by, created_at, updated_at)
                     VALUES (?, ?, ?, NOW(), NOW())",
                    [$this->user['company_id'], $typeName, $this->user['id']]
                );
            }
        } catch (\Exception $e) {
            // Silently fail - don't break the intake process
        }
    }

    /**
     * API endpoint to get dynamic suggestions for sources and types
     */
    public function getSuggestions()
    {
        $this->requireAuth();

        header('Content-Type: application/json');

        try {
            $sources = $this->getIntakeSources();
            $documentTypes = $this->getDocumentTypes();

            echo json_encode([
                'success' => true,
                'sources' => array_values($sources),
                'documentTypes' => array_values($documentTypes)
            ]);
        } catch (\Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
        exit;
    }

    /**
     * Create a new custom document type via AJAX
     */
    public function createDocumentType()
    {
        $this->requireAuth();

        if (!$this->isAjaxRequest()) {
            $this->redirect('/app/intake');
            return;
        }

        // For AJAX requests, ensure clean output
        while (ob_get_level()) {
            ob_end_clean(); // Clear all output buffers
        }
        ob_start(); // Start fresh output buffering

        try {
            // Validate input
            $validatedData = $this->validate($_POST, [
                'type_name' => 'required|max:100',
                'description' => 'max:500'
            ]);

            // Check for duplicate name
            $existing = $this->db->fetch(
                "SELECT id FROM custom_document_types
                 WHERE type_name = ? AND company_id = ? AND is_active = 1",
                [$validatedData['type_name'], $this->user['company_id']]
            );

            if ($existing) {
                throw new \Exception('Document type name already exists');
            }

            // Create document type
            $documentTypeId = $this->db->execute(
                "INSERT INTO custom_document_types (company_id, type_name, description, created_by, created_at, updated_at, is_active)
                 VALUES (?, ?, ?, ?, NOW(), NOW(), 1)",
                [
                    $this->user['company_id'],
                    $validatedData['type_name'],
                    $validatedData['description'] ?? null,
                    $this->user['id']
                ]
            );

            // Verify the document type was created
            if (!$documentTypeId) {
                throw new \Exception('Failed to create document type - no ID returned');
            }

            // Log activity (but don't let it fail the request)
            try {
                $this->logActivity('create', 'document_type', $documentTypeId, "Created document type: {$validatedData['type_name']}");
            } catch (\Exception $logError) {
                // Log the error but continue
                error_log("Failed to log activity: " . $logError->getMessage());
            }

            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Document type created successfully',
                'document_type' => [
                    'id' => $documentTypeId,
                    'type_name' => $validatedData['type_name'],
                    'description' => $validatedData['description'] ?? null
                ]
            ]);
            exit;

        } catch (\Exception $e) {
            // Log the full error for debugging
            error_log("Document type creation error: " . $e->getMessage() . " | Trace: " . $e->getTraceAsString());

            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * Get document types for API/AJAX requests
     */
    public function getDocumentTypesApi()
    {
        $this->requireAuth();

        // For AJAX requests, ensure clean output
        while (ob_get_level()) {
            ob_end_clean(); // Clear all output buffers
        }
        ob_start(); // Start fresh output buffering

        try {
            $documentTypes = $this->getDocumentTypes();

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'document_types' => $documentTypes
            ]);
            exit;

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * Create sorting category for AJAX requests
     */
    public function createSortingCategory()
    {
        $this->requireAuth();

        // For AJAX requests, ensure clean output
        while (ob_get_level()) {
            ob_end_clean();
        }
        ob_start();

        try {
            // Validate input
            $validatedData = $this->validate($_POST, [
                'category_name' => 'required|max:100',
                'category_code' => 'required|max:10',
                'description' => 'max:500',
                'color_code' => 'max:7',
                'emoji' => 'max:2'
            ]);

            // Check if category code already exists
            $existing = $this->db->fetch(
                "SELECT id FROM document_sorting_categories WHERE company_id = ? AND category_code = ?",
                [$this->user['company_id'], strtoupper($validatedData['category_code'])]
            );

            if ($existing) {
                throw new \Exception('Category code already exists. Please choose a different code.');
            }

            // Create sorting category
            $categoryId = $this->db->execute(
                "INSERT INTO document_sorting_categories (
                    company_id, category_name, category_code, description,
                    color_code, emoji, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
                [
                    $this->user['company_id'],
                    $validatedData['category_name'],
                    strtoupper($validatedData['category_code']),
                    $validatedData['description'] ?? null,
                    $validatedData['color_code'] ?? '#3B82F6',
                    $validatedData['emoji'] ?? null,
                    $this->user['id']
                ]
            );

            // Get the created category
            $category = $this->db->fetch(
                "SELECT * FROM document_sorting_categories WHERE id = ?",
                [$categoryId]
            );

            // Log activity
            $this->logActivity('create', 'sorting_category', $categoryId, "Created sorting category: {$validatedData['category_name']}");

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'category' => $category,
                'message' => 'Sorting category created successfully'
            ]);
            exit;

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * Check if request is AJAX
     */
    private function isAjaxRequest()
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Generate unique physical intake code for documents
     */
    private function generatePhysicalIntakeCode($intake)
    {
        $clientPrefix = !empty($intake['client_id']) ? strtoupper($intake['client_id']) : 'CLIENT';
        $date = date('Ymd');
        $sequence = str_pad($intake['id'], 4, '0', STR_PAD_LEFT);

        return "{$clientPrefix}-{$date}-{$sequence}";
    }

    // Box assignment methods removed - following INTAKE → BUNDLE → BOX → STORAGE workflow
    // Boxes are now assigned at the bundle level, not directly from intake

    /**
     * Generate intake barcode
     */
    private function generateIntakeBarcode($intakeId, $intakeCode)
    {
        try {
            $barcodeValue = 'INTAKE-' . $intakeCode;
            $qrCodeValue = json_encode([
                'type' => 'intake',
                'id' => $intakeId,
                'code' => $intakeCode,
                'timestamp' => time()
            ]);

            // Update intake with barcode information
            $this->db->execute(
                "UPDATE document_intake SET
                 barcode_value = ?,
                 qr_code_value = ?,
                 barcode_generated_at = NOW(),
                 updated_at = NOW()
                 WHERE id = ?",
                [$barcodeValue, $qrCodeValue, $intakeId]
            );

        } catch (\Exception $e) {
            // Log error but don't fail the process
            error_log("Failed to generate intake barcode: " . $e->getMessage());
        }
    }

    /**
     * Enhanced activity logging for intake
     */
    private function logIntakeActivity($intakeId, $action, $description)
    {
        try {
            // Log to general activity log
            $this->logActivity($action, 'intake', $intakeId, $description);

            // Log to specific intake activity log if table exists
            $this->db->execute(
                "INSERT INTO intake_activity_log (intake_id, action, description, user_id, created_at)
                 VALUES (?, ?, ?, ?, NOW())",
                [$intakeId, $action, $description, $this->user['id']]
            );

        } catch (\Exception $e) {
            // Continue if specific logging fails
            error_log("Failed to log intake activity: " . $e->getMessage());
        }
    }

    /**
     * Get next box number for client
     */
    private function getNextBoxNumber($clientPrefix)
    {
        try {
            $result = $this->db->fetch(
                "SELECT MAX(box_number) as max_number FROM boxes
                 WHERE client_prefix = ? AND company_id = ?",
                [$clientPrefix, $this->user['company_id']]
            );

            return ($result['max_number'] ?? 0) + 1;
        } catch (\Exception $e) {
            return 1;
        }
    }

    /**
     * Get default warehouse ID
     */
    private function getDefaultWarehouseId()
    {
        try {
            $result = $this->db->fetch(
                "SELECT id FROM warehouses WHERE company_id = ? ORDER BY id LIMIT 1",
                [$this->user['company_id']]
            );

            return $result['id'] ?? 1;
        } catch (\Exception $e) {
            return 1;
        }
    }

    /**
     * Generate storage location code
     */
    private function generateStorageLocationCode($warehouseId, $row, $shelf, $position)
    {
        return "WH{$warehouseId}-{$row}-{$shelf}-{$position}";
    }

    /**
     * Delete intake entry
     */
    public function delete($id)
    {
        $this->requireAuth();

        try {
            // Get intake details for validation and logging
            $intake = $this->getIntakeById($id);
            if (!$intake) {
                throw new \Exception('Intake entry not found');
            }

            // Only allow deletion of pending intakes
            if ($intake['status'] !== 'pending') {
                throw new \Exception('Only pending intake entries can be deleted. This intake is currently ' . $intake['status'] . '.');
            }

            // Check if there are any associated documents
            $documentCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM documents WHERE intake_id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Start transaction for safe deletion
            $this->db->beginTransaction();

            try {
                // Delete associated documents first (if any)
                if ($documentCount > 0) {
                    $this->db->execute(
                        "DELETE FROM documents WHERE intake_id = ? AND company_id = ?",
                        [$id, $this->user['company_id']]
                    );
                }

                // Delete the intake entry
                $this->db->execute(
                    "DELETE FROM document_intake WHERE id = ? AND company_id = ?",
                    [$id, $this->user['company_id']]
                );

                // Log the deletion activity
                $this->logActivity('delete', 'intake', $id, "Deleted intake: {$intake['reference_number']} (with {$documentCount} documents)");

                // Commit transaction
                $this->db->commit();

                $this->setFlashMessage("Intake entry '{$intake['reference_number']}' has been successfully deleted.", 'success');
                $this->redirect('/app/intake');

            } catch (\Exception $e) {
                // Rollback transaction on error
                $this->db->rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to delete intake: ' . $e->getMessage(), 'error');
            $this->redirect('/app/intake');
        }
    }
}
