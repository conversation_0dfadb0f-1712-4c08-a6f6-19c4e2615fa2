<?php
/**
 * Service Rates Management - Completely Rewritten
 */

// Ensure CSRF token is available
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = generateToken(32);
}

$title = 'Service Rates';
$content = ob_start();
?>

<!-- CSRF Token Meta Tag -->
<meta name="csrf-token" content="<?= $_SESSION['csrf_token'] ?? '' ?>">

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
                <div class="flex items-center gap-3 mb-2">
                    <a href="<?= url('/app/billing') ?>" class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Back to Billing
                    </a>
                </div>
                <h1 class="text-3xl font-bold text-gray-900">Service Rates</h1>
                <p class="text-gray-600 mt-1">Manage billing rates for all services</p>
            </div>
            <div>
                <button type="button" onclick="RatesManager.openAddModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add New Rate
                </button>
            </div>
        </div>
    </div>

    <!-- Service Rates Table -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Current Service Rates</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (!empty($rates_by_category)): ?>
                        <?php foreach ($rates_by_category as $category => $rates): ?>
                            <?php foreach ($rates as $rate): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($rate['service_name']) ?></div>
                                        <div class="text-sm text-gray-500"><?= htmlspecialchars($rate['service_code']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?= htmlspecialchars($category) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        $<?= number_format($rate['rate'], 2) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= htmlspecialchars($rate['unit'] ?? 'per item') ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($rate['is_active']): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Inactive
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <!-- Edit Button -->
                                            <button onclick="RatesManager.editRate(<?= $rate['id'] ?>, '<?= htmlspecialchars($rate['service_name'], ENT_QUOTES) ?>', '<?= htmlspecialchars($rate['service_code'], ENT_QUOTES) ?>', <?= $rate['rate'] ?>, '<?= htmlspecialchars($rate['category'], ENT_QUOTES) ?>', '<?= htmlspecialchars($rate['unit'] ?? '', ENT_QUOTES) ?>')" 
                                                    class="text-blue-600 hover:text-blue-900 transition-colors">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </button>
                                            
                                            <!-- Toggle Status Button -->
                                            <a href="<?= url('/app/billing/rates/toggle/' . $rate['id']) ?>" 
                                               class="<?= $rate['is_active'] ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900' ?> transition-colors"
                                               title="<?= $rate['is_active'] ? 'Deactivate' : 'Activate' ?>">
                                                <?php if ($rate['is_active']): ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                    </svg>
                                                <?php else: ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                <?php endif; ?>
                                            </a>
                                            
                                            <!-- Delete Button -->
                                            <button onclick="RatesManager.deleteRate(<?= $rate['id'] ?>, '<?= htmlspecialchars($rate['service_name'], ENT_QUOTES) ?>')" 
                                                    class="text-red-600 hover:text-red-900 transition-colors">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No service rates</h3>
                                <p class="mt-1 text-sm text-gray-500">Get started by creating a new service rate.</p>
                                <div class="mt-6">
                                    <button onclick="RatesManager.openAddModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Add Service Rate
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>

                <div class="text-center">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete Service Rate</h3>
                    <p class="text-sm text-gray-600 mb-1">Are you sure you want to delete:</p>
                    <p class="text-base font-medium text-gray-900 mb-4" id="deleteServiceName"></p>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div class="text-left">
                                <p class="text-sm font-medium text-yellow-800">Warning</p>
                                <p class="text-sm text-yellow-700">This action cannot be undone. If this rate is being used in billing events, the deletion will be prevented.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="RatesManager.closeDeleteModal()" class="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        Cancel
                    </button>
                    <button type="button" onclick="RatesManager.confirmDelete()" class="flex-1 px-4 py-2.5 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete Rate
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Rate Modal -->
<div id="addRateModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4" id="modalTitle">Add New Service Rate</h3>

                <form id="rateForm" method="POST" action="<?= url('/app/billing/rates/save') ?>">
                    <input type="hidden" name="_token" value="<?= $_SESSION['csrf_token'] ?? '' ?>">
                    <input type="hidden" name="rate_id" id="rateId" value="">

                    <div class="space-y-4">
                        <div>
                            <label for="serviceName" class="block text-sm font-medium text-gray-700 mb-1">Service Name</label>
                            <input type="text" id="serviceName" name="service_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="serviceCode" class="block text-sm font-medium text-gray-700 mb-1">Service Code</label>
                            <input type="text" id="serviceCode" name="service_code" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="rate" class="block text-sm font-medium text-gray-700 mb-1">Rate ($)</label>
                            <input type="number" id="rate" name="rate" step="0.01" min="0" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <select id="category" name="category" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Category</option>
                                <option value="Document Processing">Document Processing</option>
                                <option value="Storage">Storage</option>
                                <option value="Handling">Handling</option>
                                <option value="Administrative">Administrative</option>
                            </select>
                        </div>

                        <div>
                            <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                            <input type="text" id="unit" name="unit" placeholder="per item, per box, per hour, etc."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button type="button" onclick="RatesManager.closeAddModal()" class="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="flex-1 px-4 py-2.5 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            Save Rate
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Service Rates Management System
 */
class ServiceRatesManager {
    constructor() {
        this.currentRateId = null;
        this.currentServiceName = null;
        this.isProcessing = false;
        this.init();
    }

    init() {
        // Close modals when clicking outside
        document.addEventListener('click', (event) => {
            const deleteModal = document.getElementById('deleteModal');
            const addModal = document.getElementById('addRateModal');

            if (event.target === deleteModal) {
                this.closeDeleteModal();
            }
            if (event.target === addModal) {
                this.closeAddModal();
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.closeDeleteModal();
                this.closeAddModal();
            }
        });

        console.log('✅ Service Rates Manager initialized');
    }

    // Delete functionality
    deleteRate(rateId, serviceName) {
        if (this.isProcessing) {
            console.log('Operation already in progress');
            return;
        }

        try {
            // Validate inputs
            if (!rateId || !serviceName) {
                this.showError('Invalid rate information provided');
                return;
            }

            // Store rate information
            this.currentRateId = parseInt(rateId);
            this.currentServiceName = serviceName;

            // Update modal content
            const nameElement = document.getElementById('deleteServiceName');
            if (nameElement) {
                nameElement.textContent = serviceName;
            }

            // Show confirmation modal
            this.showDeleteModal();

            console.log('Delete initialized for rate:', this.currentRateId, this.currentServiceName);
        } catch (error) {
            console.error('Error initializing delete:', error);
            this.showError('Failed to initialize delete process');
        }
    }

    showDeleteModal() {
        const modal = document.getElementById('deleteModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    closeDeleteModal() {
        const modal = document.getElementById('deleteModal');
        if (modal) {
            modal.classList.add('hidden');
        }
        this.resetDeleteState();
    }

    resetDeleteState() {
        this.currentRateId = null;
        this.currentServiceName = null;
        this.isProcessing = false;
    }

    async confirmDelete() {
        if (this.isProcessing) {
            console.log('Delete already in progress');
            return;
        }

        if (!this.currentRateId) {
            this.showError('No rate selected for deletion');
            this.closeDeleteModal();
            return;
        }

        try {
            this.isProcessing = true;
            this.setDeleteLoadingState(true);

            await this.executeDelete();

        } catch (error) {
            console.error('Delete confirmation error:', error);
            this.showError('Failed to delete service rate: ' + error.message);
        } finally {
            this.isProcessing = false;
            this.setDeleteLoadingState(false);
            this.closeDeleteModal();
        }
    }

    async executeDelete() {
        return new Promise((resolve, reject) => {
            try {
                // Generate delete URL
                const deleteUrl = `<?= url('/app/billing/rates/delete/') ?>${this.currentRateId}`;
                console.log('Executing delete to URL:', deleteUrl);

                // Create and configure form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = deleteUrl;
                form.style.display = 'none';

                // Add CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (csrfToken && csrfToken.getAttribute('content')) {
                    const tokenInput = document.createElement('input');
                    tokenInput.type = 'hidden';
                    tokenInput.name = '_token';
                    tokenInput.value = csrfToken.getAttribute('content');
                    form.appendChild(tokenInput);
                    console.log('✅ CSRF token added');
                } else {
                    console.warn('⚠️ CSRF token not found');
                }

                // Add rate ID as hidden field for extra validation
                const rateIdInput = document.createElement('input');
                rateIdInput.type = 'hidden';
                rateIdInput.name = 'rate_id';
                rateIdInput.value = this.currentRateId;
                form.appendChild(rateIdInput);

                // Submit form
                document.body.appendChild(form);
                console.log('✅ Submitting delete form');
                form.submit();

                resolve();
            } catch (error) {
                reject(error);
            }
        });
    }

    setDeleteLoadingState(loading) {
        const confirmBtn = document.querySelector('#deleteModal button[onclick="RatesManager.confirmDelete()"]');
        const cancelBtn = document.querySelector('#deleteModal button[onclick="RatesManager.closeDeleteModal()"]');

        if (confirmBtn) {
            if (loading) {
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-2 inline animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Deleting...
                `;
            } else {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete Rate
                `;
            }
        }

        if (cancelBtn) {
            cancelBtn.disabled = loading;
        }
    }

    // Add/Edit functionality
    openAddModal() {
        document.getElementById('modalTitle').textContent = 'Add New Service Rate';
        document.getElementById('rateForm').reset();
        document.getElementById('rateId').value = '';
        document.getElementById('addRateModal').classList.remove('hidden');
    }

    editRate(id, name, code, rate, category, unit) {
        document.getElementById('modalTitle').textContent = 'Edit Service Rate';
        document.getElementById('rateId').value = id;
        document.getElementById('serviceName').value = name;
        document.getElementById('serviceCode').value = code;
        document.getElementById('rate').value = rate;
        document.getElementById('category').value = category;
        document.getElementById('unit').value = unit || '';
        document.getElementById('addRateModal').classList.remove('hidden');
    }

    closeAddModal() {
        document.getElementById('addRateModal').classList.add('hidden');
    }

    // Error handling
    showError(message) {
        console.error('Rates Manager Error:', message);

        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-20 right-4 z-[9999] max-w-sm';
        errorDiv.innerHTML = `
            <div class="bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg border border-red-600">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>${this.escapeHtml(message)}</span>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200 focus:outline-none">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(errorDiv);

        // Auto-remove after 7 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 7000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the rates manager
const RatesManager = new ServiceRatesManager();

// Legacy function support
function openAddRateModal() {
    RatesManager.openAddModal();
}
</script>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/../layouts/app.php';
?>
