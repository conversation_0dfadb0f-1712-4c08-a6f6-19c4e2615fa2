<?php
/**
 * Simple Log Viewer
 */

echo "<h1>📋 Error Log Viewer</h1>";

// Common log file locations
$logPaths = [
    'PHP Error Log' => ini_get('error_log'),
    'Apache Error Log' => 'C:/xampp/apache/logs/error.log',
    'PHP Error Log (XAMPP)' => 'C:/xampp/php/logs/php_error_log',
    'Custom Error Log' => 'C:/xampp/htdocs/dms/error.log'
];

foreach ($logPaths as $name => $path) {
    echo "<h2>{$name}</h2>";
    
    if ($path && file_exists($path)) {
        echo "<p><strong>Path:</strong> {$path}</p>";
        
        $lines = file($path);
        if ($lines) {
            $recentLines = array_slice($lines, -50); // Last 50 lines
            
            echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;'>";
            echo "<strong>Last 50 lines:</strong><br><br>";
            
            foreach ($recentLines as $line) {
                $line = htmlspecialchars($line);
                
                // Highlight our debug messages
                if (strpos($line, 'DELETE SERVICE RATE') !== false) {
                    echo "<span style='background: yellow; font-weight: bold;'>{$line}</span>";
                } elseif (strpos($line, '✅') !== false) {
                    echo "<span style='color: green;'>{$line}</span>";
                } elseif (strpos($line, '❌') !== false) {
                    echo "<span style='color: red; font-weight: bold;'>{$line}</span>";
                } elseif (strpos($line, '🔍') !== false || strpos($line, '🔄') !== false) {
                    echo "<span style='color: blue;'>{$line}</span>";
                } else {
                    echo $line;
                }
                echo "<br>";
            }
            echo "</div>";
        } else {
            echo "<p>❌ Could not read log file</p>";
        }
    } else {
        echo "<p>❌ Log file not found: {$path}</p>";
    }
    
    echo "<hr>";
}

echo "<h2>🔄 Refresh Logs</h2>";
echo "<button onclick='window.location.reload()' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🔄 Refresh Page</button>";

echo "<hr>";
echo "<p><a href='debug-delete-complete.php'>← Back to Debug Page</a></p>";
?>
