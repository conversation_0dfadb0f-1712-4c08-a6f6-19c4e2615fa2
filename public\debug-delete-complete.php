<?php
/**
 * Complete Delete Debug - Step by Step Analysis
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = generateToken(32);
}

// Create a mock user session for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['company_id'] = 1;
    $_SESSION['user_role'] = 'super_admin';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['logged_in'] = true;
}

echo "<h1>🔍 Complete Delete Debug Analysis</h1>";

// Step 1: Environment Check
echo "<h2>Step 1: Environment Check</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Check</th><th>Value</th><th>Status</th></tr>";
echo "<tr><td>Base Path</td><td>" . BASE_PATH . "</td><td>✅</td></tr>";
echo "<tr><td>Session Active</td><td>" . (session_status() === PHP_SESSION_ACTIVE ? 'Yes' : 'No') . "</td><td>✅</td></tr>";
echo "<tr><td>User Logged In</td><td>" . (isset($_SESSION['logged_in']) ? 'Yes' : 'No') . "</td><td>" . (isset($_SESSION['logged_in']) ? '✅' : '❌') . "</td></tr>";
echo "<tr><td>CSRF Token</td><td>" . (isset($_SESSION['csrf_token']) ? substr($_SESSION['csrf_token'], 0, 10) . '...' : 'Missing') . "</td><td>" . (isset($_SESSION['csrf_token']) ? '✅' : '❌') . "</td></tr>";
echo "</table>";

// Step 2: Route Testing
echo "<h2>Step 2: Route Testing</h2>";
try {
    $router = new \App\Core\Router();
    require_once APP_ROOT . '/src/routes.php';
    
    $testUrl = '/app/billing/rates/delete/1';
    echo "<p><strong>Testing Route:</strong> POST {$testUrl}</p>";
    echo "<p>✅ Router initialized successfully</p>";
    echo "<p>✅ Routes loaded successfully</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Router Error: " . $e->getMessage() . "</p>";
}

// Step 3: Controller Testing
echo "<h2>Step 3: Controller Testing</h2>";
try {
    require_once APP_ROOT . '/src/Controllers/BillingController.php';
    $controller = new App\Controllers\BillingController();
    echo "<p>✅ BillingController instantiated successfully</p>";
    
    // Check if method exists
    if (method_exists($controller, 'deleteServiceRate')) {
        echo "<p>✅ deleteServiceRate method exists</p>";
    } else {
        echo "<p>❌ deleteServiceRate method not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Controller Error: " . $e->getMessage() . "</p>";
}

// Step 4: Database Testing
echo "<h2>Step 4: Database Testing</h2>";
try {
    $db = App\Core\Database::getInstance();
    $rates = $db->fetchAll("SELECT * FROM billing_rates LIMIT 3");
    
    if (!empty($rates)) {
        echo "<p>✅ Database connection successful</p>";
        echo "<p>✅ Found " . count($rates) . " test rates</p>";
        
        $testRate = $rates[0];
        echo "<p><strong>Test Rate:</strong> {$testRate['service_name']} (ID: {$testRate['id']})</p>";
    } else {
        echo "<p>❌ No rates found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database Error: " . $e->getMessage() . "</p>";
}

// Step 5: Actual Delete Test
echo "<h2>Step 5: Live Delete Test</h2>";

if (isset($testRate)) {
    $deleteUrl = url('/app/billing/rates/delete/' . $testRate['id']);
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⚠️ Live Delete Test</h3>";
    echo "<p><strong>Target:</strong> {$testRate['service_name']} (ID: {$testRate['id']})</p>";
    echo "<p><strong>URL:</strong> {$deleteUrl}</p>";
    echo "<p><strong>Method:</strong> POST</p>";
    echo "<p><strong>CSRF Token:</strong> " . substr($_SESSION['csrf_token'], 0, 16) . "...</p>";
    
    echo "<form method='POST' action='{$deleteUrl}' style='margin: 10px 0;'>";
    echo "<input type='hidden' name='_token' value='{$_SESSION['csrf_token']}'>";
    echo "<input type='hidden' name='rate_id' value='{$testRate['id']}'>";
    echo "<button type='submit' style='background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"This will attempt to delete the rate. Continue?\")'>🚨 Execute Live Delete Test</button>";
    echo "</form>";
    echo "</div>";
}

// Step 6: JavaScript Debug Console
echo "<h2>Step 6: JavaScript Debug Console</h2>";
echo "<div id='js-debug-output' style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; font-family: monospace; min-height: 100px;'>";
echo "JavaScript debug output will appear here...";
echo "</div>";

echo "<button onclick='runJavaScriptDebug()' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px 0;'>🧪 Run JavaScript Debug</button>";

// Step 7: Flash Message Check
echo "<h2>Step 7: Flash Message Check</h2>";
if (isset($_SESSION['flash_message'])) {
    $type = $_SESSION['flash_type'] ?? 'info';
    $message = $_SESSION['flash_message'];
    
    echo "<div style='background: " . ($type === 'error' ? '#f8d7da' : '#d4edda') . "; border: 1px solid " . ($type === 'error' ? '#f5c6cb' : '#c3e6cb') . "; color: " . ($type === 'error' ? '#721c24' : '#155724') . "; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Flash Message ({$type}):</strong> " . htmlspecialchars($message);
    echo "</div>";
    
    // Clear flash message
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
} else {
    echo "<p>✅ No flash messages in session</p>";
}

?>

<meta name="csrf-token" content="<?= $_SESSION['csrf_token'] ?? '' ?>">

<script>
function runJavaScriptDebug() {
    const output = document.getElementById('js-debug-output');
    let debugInfo = '';
    
    try {
        // Test 1: CSRF Token Detection
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken && csrfToken.getAttribute('content')) {
            debugInfo += '✅ CSRF Token Found: ' + csrfToken.getAttribute('content').substring(0, 16) + '...\n';
        } else {
            debugInfo += '❌ CSRF Token Not Found\n';
        }
        
        // Test 2: URL Generation
        const testUrl = '<?= url('/app/billing/rates/delete/1') ?>';
        debugInfo += '✅ URL Generation: ' + testUrl + '\n';
        
        // Test 3: Form Creation Test
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = testUrl;
        debugInfo += '✅ Form Creation: Success\n';
        
        // Test 4: Token Addition Test
        if (csrfToken) {
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = csrfToken.getAttribute('content');
            form.appendChild(tokenInput);
            debugInfo += '✅ Token Addition: Success\n';
        } else {
            debugInfo += '❌ Token Addition: Failed (no token)\n';
        }
        
        // Test 5: Simulate Delete Function
        debugInfo += '\n--- Simulating Delete Function ---\n';
        debugInfo += 'Rate ID: 1\n';
        debugInfo += 'Service Name: Test Rate\n';
        debugInfo += 'Form Action: ' + form.action + '\n';
        debugInfo += 'Form Method: ' + form.method + '\n';
        debugInfo += 'Token Present: ' + (form.querySelector('input[name="_token"]') ? 'Yes' : 'No') + '\n';
        
        debugInfo += '\n✅ All JavaScript tests completed successfully';
        
    } catch (error) {
        debugInfo += '\n❌ JavaScript Error: ' + error.message;
        console.error('JavaScript Debug Error:', error);
    }
    
    output.textContent = debugInfo;
    console.log('JavaScript Debug Results:', debugInfo);
}

// Auto-run debug on page load
document.addEventListener('DOMContentLoaded', function() {
    runJavaScriptDebug();
});
</script>

<h2>Step 8: Next Steps</h2>
<div style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;">
    <h3>🎯 What to do:</h3>
    <ol>
        <li><strong>Check all steps above</strong> - Look for any ❌ errors</li>
        <li><strong>Try the "Execute Live Delete Test"</strong> - This will show the exact error</li>
        <li><strong>Check the JavaScript Debug Console</strong> - Look for any issues</li>
        <li><strong>Look at Flash Messages</strong> - See if there are specific error messages</li>
        <li><strong>Report back</strong> - Tell me exactly what you see in each step</li>
    </ol>
</div>

<hr>
<p><a href="<?= url('/app/billing/rates') ?>">← Back to Service Rates</a></p>
