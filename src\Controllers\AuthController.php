<?php

namespace App\Controllers;

/**
 * Authentication Controller
 * 
 * Handles user authentication, registration, and password management
 */
class AuthController extends BaseController
{
    /**
     * Show login form
     */
    public function showLogin()
    {
        // Redirect if already logged in
        if ($this->user) {
            redirect('/dashboard');
        }
        
        $this->view('auth/login');
    }
    
    /**
     * <PERSON>le login (ULTRA SIMPLIFIED - Direct and Reliable)
     */
    public function login()
    {
        // Get form data
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';

        // Log attempt
        error_log("LOGIN ATTEMPT: Email = " . $email);

        // Basic validation
        if (empty($email) || empty($password)) {
            $this->view('auth/login', ['error' => 'Email and password are required', 'email' => $email]);
            return;
        }

        try {
            // Find user
            $user = $this->db->fetch(
                "SELECT u.*, c.status as company_status
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 WHERE u.email = ?",
                [$email]
            );

            // Check user exists
            if (!$user) {
                $this->view('auth/login', ['error' => 'Invalid email or password', 'email' => $email]);
                return;
            }

            // Check password
            if (!password_verify($password, $user['password_hash'])) {
                $this->view('auth/login', ['error' => 'Invalid email or password', 'email' => $email]);
                return;
            }

            // Check user is active
            if ($user['status'] !== 'active') {
                $this->view('auth/login', ['error' => 'Account is not active', 'email' => $email]);
                return;
            }

            // For super admin, skip company check
            if ($user['role'] !== 'super_admin' && $user['company_status'] !== 'active') {
                $this->view('auth/login', ['error' => 'Company account is not active', 'email' => $email]);
                return;
            }

            // Start session
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // Set session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['company_id'] = $user['company_id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['logged_in'] = true;

            // Generate CSRF token for the session
            if (!isset($_SESSION['csrf_token'])) {
                $_SESSION['csrf_token'] = generateToken(32);
            }

            // Update last login
            $this->db->execute("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);

            // Log success
            error_log("LOGIN SUCCESS: User {$user['id']} logged in");

            // Redirect based on role
            if ($user['role'] === 'super_admin') {
                header('Location: ' . url('/super-admin/dashboard'));
            } else {
                header('Location: ' . url('/app/dashboard'));
            }
            exit;

        } catch (Exception $e) {
            error_log("LOGIN ERROR: " . $e->getMessage());
            $this->view('auth/login', ['error' => 'Login failed. Please try again.', 'email' => $email]);
        }
    }
    
    /**
     * Show registration form
     */
    public function showRegister()
    {
        // Check if registration is enabled
        if (!config('features.user_registration', true)) {
            $this->view('errors/404');
            return;
        }
        
        // Redirect if already logged in
        if ($this->user) {
            redirect('/dashboard');
        }
        
        // Get companies for selection
        $companies = $this->db->fetchAll(
            "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name"
        );
        
        $this->view('auth/register', ['companies' => $companies]);
    }
    
    /**
     * Handle registration
     */
    public function register()
    {
        // Check if registration is enabled
        if (!config('features.user_registration', true)) {
            $this->jsonResponse(['error' => 'Registration is disabled'], 403);
        }
        
        $data = [
            'first_name' => $this->input('first_name'),
            'last_name' => $this->input('last_name'),
            'email' => $this->input('email'),
            'username' => $this->input('username'),
            'password' => $this->input('password'),
            'password_confirmation' => $this->input('password_confirmation'),
            'company_id' => $this->input('company_id'),
            'phone' => $this->input('phone')
        ];
        
        // Validate input
        $errors = $this->validate($data, [
            'first_name' => 'required|max:100',
            'last_name' => 'required|max:100',
            'email' => 'required|email|unique:users,email',
            'username' => 'required|min:3|max:100|unique:users,username',
            'password' => 'required|min:' . config('password_min_length', 8),
            'company_id' => 'required'
        ]);
        
        // Check password confirmation
        if ($data['password'] !== $data['password_confirmation']) {
            $errors['password_confirmation'][] = 'Password confirmation does not match';
        }
        
        if (!empty($errors)) {
            if (isAjax()) {
                $this->jsonResponse(['errors' => $errors], 422);
            } else {
                $companies = $this->db->fetchAll(
                    "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name"
                );
                $this->view('auth/register', ['errors' => $errors, 'data' => $data, 'companies' => $companies]);
                return;
            }
        }
        
        // Verify company exists and is active
        $company = $this->db->fetch(
            "SELECT * FROM companies WHERE id = ? AND status = 'active'",
            [$data['company_id']]
        );
        
        if (!$company) {
            $error = 'Selected company is not valid';
            if (isAjax()) {
                $this->jsonResponse(['error' => $error], 422);
            } else {
                $companies = $this->db->fetchAll(
                    "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name"
                );
                $this->view('auth/register', ['error' => $error, 'data' => $data, 'companies' => $companies]);
                return;
            }
        }
        
        try {
            // Create user
            $userId = $this->db->insert('users', [
                'company_id' => $data['company_id'],
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'username' => $data['username'],
                'password_hash' => password_hash($data['password'], PASSWORD_DEFAULT),
                'phone' => $data['phone'],
                'role' => 'viewer', // Default role
                'status' => config('features.email_verification', false) ? 'pending' : 'active',
                'email_verification_token' => config('features.email_verification', false) ? generateToken() : null
            ]);
            
            // Log activity
            $this->logActivity('user', $userId, 'create');
            
            $message = 'Registration successful! ';
            if (config('features.email_verification', false)) {
                $message .= 'Please check your email to verify your account.';
            } else {
                $message .= 'You can now log in.';
                
                // Auto-login if email verification is disabled
                $_SESSION['user_id'] = $userId;
                $_SESSION['company_id'] = $data['company_id'];
                $_SESSION['user_role'] = 'viewer';
            }
            
            if (isAjax()) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $message,
                    'redirect' => config('features.email_verification', false) ? '/login' : '/dashboard'
                ]);
            } else {
                $this->redirectWithMessage(
                    config('features.email_verification', false) ? '/login' : '/dashboard',
                    $message
                );
            }
            
        } catch (\Exception $e) {
            logMessage("Registration failed: " . $e->getMessage(), 'error');
            
            $error = 'Registration failed. Please try again.';
            if (isAjax()) {
                $this->jsonResponse(['error' => $error], 500);
            } else {
                $companies = $this->db->fetchAll(
                    "SELECT id, name FROM companies WHERE status = 'active' ORDER BY name"
                );
                $this->view('auth/register', ['error' => $error, 'data' => $data, 'companies' => $companies]);
            }
        }
    }
    
    /**
     * Handle logout
     */
    public function logout()
    {
        // Log activity before destroying session
        if ($this->user) {
            $this->logActivity('user', $this->user['id'], 'logout');
        }
        
        // Destroy session
        session_destroy();
        
        if (isAjax()) {
            $this->jsonResponse(['success' => true, 'redirect' => '/login']);
        } else {
            redirect('/login');
        }
    }
    
    /**
     * Show forgot password form
     */
    public function showForgotPassword()
    {
        $this->view('auth/forgot-password');
    }
    
    /**
     * Handle forgot password
     */
    public function forgotPassword()
    {
        $email = $this->input('email');
        
        $errors = $this->validate(['email' => $email], ['email' => 'required|email']);
        
        if (!empty($errors)) {
            if (isAjax()) {
                $this->jsonResponse(['errors' => $errors], 422);
            } else {
                $this->view('auth/forgot-password', ['errors' => $errors, 'email' => $email]);
                return;
            }
        }
        
        // Find user
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE email = ? AND status = 'active'",
            [$email]
        );
        
        if ($user) {
            // Generate reset token
            $token = generateToken();
            $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
            
            $this->db->update('users', [
                'password_reset_token' => $token,
                'password_reset_expires' => $expires
            ], 'id = ?', [$user['id']]);
            
            // TODO: Send email with reset link
            // For now, just log the token
            logMessage("Password reset token for {$email}: {$token}");
        }
        
        // Always show success message for security
        $message = 'If an account with that email exists, a password reset link has been sent.';
        
        if (isAjax()) {
            $this->jsonResponse(['success' => true, 'message' => $message]);
        } else {
            $this->view('auth/forgot-password', ['success' => $message]);
        }
    }
    
    /**
     * Show reset password form
     */
    public function showResetPassword($token)
    {
        // Verify token
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE password_reset_token = ? AND password_reset_expires > NOW()",
            [$token]
        );
        
        if (!$user) {
            $this->view('auth/reset-password', ['error' => 'Invalid or expired reset token']);
            return;
        }
        
        $this->view('auth/reset-password', ['token' => $token]);
    }
    
    /**
     * Handle reset password
     */
    public function resetPassword()
    {
        $token = $this->input('token');
        $password = $this->input('password');
        $passwordConfirmation = $this->input('password_confirmation');
        
        // Validate input
        $errors = $this->validate([
            'password' => $password
        ], [
            'password' => 'required|min:' . config('password_min_length', 8)
        ]);
        
        if ($password !== $passwordConfirmation) {
            $errors['password_confirmation'][] = 'Password confirmation does not match';
        }
        
        if (!empty($errors)) {
            if (isAjax()) {
                $this->jsonResponse(['errors' => $errors], 422);
            } else {
                $this->view('auth/reset-password', ['errors' => $errors, 'token' => $token]);
                return;
            }
        }
        
        // Verify token
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE password_reset_token = ? AND password_reset_expires > NOW()",
            [$token]
        );
        
        if (!$user) {
            $error = 'Invalid or expired reset token';
            if (isAjax()) {
                $this->jsonResponse(['error' => $error], 422);
            } else {
                $this->view('auth/reset-password', ['error' => $error]);
                return;
            }
        }
        
        // Update password
        $this->db->update('users', [
            'password_hash' => password_hash($password, PASSWORD_DEFAULT),
            'password_reset_token' => null,
            'password_reset_expires' => null
        ], 'id = ?', [$user['id']]);
        
        // Log activity
        $this->logActivity('user', $user['id'], 'update', null, ['password_reset' => true]);
        
        $message = 'Password has been reset successfully. You can now log in.';
        
        if (isAjax()) {
            $this->jsonResponse(['success' => true, 'message' => $message, 'redirect' => '/login']);
        } else {
            $this->redirectWithMessage('/login', $message);
        }
    }
}
