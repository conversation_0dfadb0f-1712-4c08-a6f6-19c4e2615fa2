<?php

namespace App\Controllers;

use App\Services\BillingEventLogger;

/**
 * Advanced Barcode Management Controller
 * 
 * Enhanced barcode management features for super admin
 * Provides bulk operations, analytics, and comprehensive tracking
 */
class BarcodeManagementController extends BaseController
{
    /**
     * Barcode Management Dashboard
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get barcode overview statistics
            $barcodeStats = $this->getBarcodeStats();
            
            // Get recent barcode activities
            $recentActivities = $this->getRecentBarcodeActivities();
            
            // Get barcode usage analytics
            $usageAnalytics = $this->getBarcodeUsageAnalytics();
            
            // Get barcode generation trends
            $generationTrends = $this->getBarcodeGenerationTrends();

            $this->view('super-admin/barcode-management', [
                'title' => 'Advanced Barcode Management',
                'barcodeStats' => $barcodeStats,
                'recentActivities' => $recentActivities,
                'usageAnalytics' => $usageAnalytics,
                'generationTrends' => $generationTrends
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading barcode management: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/dashboard');
        }
    }

    /**
     * Bulk Barcode Generation Interface
     */
    public function bulkGenerate()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->processBulkGeneration();
        }

        try {
            // Get available entities for bulk generation
            $availableBoxes = $this->getBoxesWithoutBarcodes();
            $availableBundles = $this->getBundlesWithoutBarcodes();
            $availableDocuments = $this->getDocumentsWithoutBarcodes();

            $this->view('super-admin/barcode-bulk-generate', [
                'title' => 'Bulk Barcode Generation',
                'availableBoxes' => $availableBoxes,
                'availableBundles' => $availableBundles,
                'availableDocuments' => $availableDocuments
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading bulk generation: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/barcode-management');
        }
    }

    /**
     * Mobile Scanner Interface
     */
    public function mobileScanner()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin', 'admin', 'staff']);

        $this->view('super-admin/mobile-scanner', [
            'title' => 'Mobile Barcode Scanner'
        ]);
    }

    /**
     * Barcode Analytics Dashboard
     */
    public function analytics()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get comprehensive analytics
            $scanningAnalytics = $this->getScanningAnalytics();
            $usagePatterns = $this->getUsagePatterns();
            $performanceMetrics = $this->getPerformanceMetrics();
            $errorAnalytics = $this->getErrorAnalytics();

            $this->view('super-admin/barcode-analytics', [
                'title' => 'Barcode Analytics',
                'scanningAnalytics' => $scanningAnalytics,
                'usagePatterns' => $usagePatterns,
                'performanceMetrics' => $performanceMetrics,
                'errorAnalytics' => $errorAnalytics
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading barcode analytics: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/barcode-management');
        }
    }

    /**
     * Barcode Audit Trail
     */
    public function auditTrail()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        try {
            // Get audit trail with filters
            $filters = $this->getFiltersFromRequest();
            $auditTrail = $this->getBarcodeAuditTrail($filters);
            $auditStats = $this->getAuditStats($filters);

            $this->view('super-admin/barcode-audit-trail', [
                'title' => 'Barcode Audit Trail',
                'auditTrail' => $auditTrail,
                'auditStats' => $auditStats,
                'filters' => $filters
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading audit trail: ' . $e->getMessage(), 'error');
            $this->redirect('/super-admin/barcode-management');
        }
    }

    /**
     * Process bulk barcode generation
     */
    private function processBulkGeneration()
    {
        try {
            $entityType = $_POST['entity_type'] ?? '';
            $entityIds = $_POST['entity_ids'] ?? [];
            $barcodeType = $_POST['barcode_type'] ?? 'qr';
            $barcodeFormat = $_POST['barcode_format'] ?? 'standard';

            if (empty($entityType) || empty($entityIds)) {
                throw new \Exception('Entity type and IDs are required');
            }

            $results = [];
            $successCount = 0;
            $errorCount = 0;

            foreach ($entityIds as $entityId) {
                try {
                    $barcode = $this->generateBarcodeForEntity($entityType, $entityId, $barcodeType, $barcodeFormat);
                    $results[] = [
                        'entity_id' => $entityId,
                        'status' => 'success',
                        'barcode' => $barcode
                    ];
                    $successCount++;
                } catch (\Exception $e) {
                    $results[] = [
                        'entity_id' => $entityId,
                        'status' => 'error',
                        'error' => $e->getMessage()
                    ];
                    $errorCount++;
                }
            }

            // Log bulk generation activity
            $this->logBulkGenerationActivity($entityType, $successCount, $errorCount);

            $this->jsonResponse([
                'success' => true,
                'message' => "Generated {$successCount} barcodes successfully, {$errorCount} errors",
                'results' => $results,
                'summary' => [
                    'total' => count($entityIds),
                    'success' => $successCount,
                    'errors' => $errorCount
                ]
            ]);

        } catch (\Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get barcode overview statistics
     */
    private function getBarcodeStats()
    {
        return $this->db->fetch(
            "SELECT 
                COUNT(DISTINCT b.id) as total_barcodes,
                COUNT(DISTINCT CASE WHEN b.is_active = 1 THEN b.id END) as active_barcodes,
                COUNT(DISTINCT CASE WHEN b.last_scanned_at IS NOT NULL THEN b.id END) as scanned_barcodes,
                COUNT(DISTINCT CASE WHEN DATE(b.generated_at) = CURDATE() THEN b.id END) as generated_today,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'box' THEN b.id END) as box_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'bundle' THEN b.id END) as bundle_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'document' THEN b.id END) as document_barcodes,
                SUM(b.scan_count) as total_scans,
                AVG(b.scan_count) as avg_scans_per_barcode
             FROM barcodes b
             WHERE b.is_active = 1",
            []
        );
    }

    /**
     * Get recent barcode activities
     */
    private function getRecentBarcodeActivities()
    {
        return $this->db->fetchAll(
            "SELECT 
                bat.*,
                u.first_name,
                u.last_name,
                u.email,
                CASE 
                    WHEN bat.barcode_type = 'box' THEN (SELECT name FROM boxes WHERE id = bat.entity_id)
                    WHEN bat.barcode_type = 'bundle' THEN (SELECT name FROM bundles WHERE id = bat.entity_id)
                    WHEN bat.barcode_type = 'document' THEN (SELECT title FROM documents WHERE id = bat.entity_id)
                    ELSE 'Unknown'
                END as entity_name
             FROM barcode_audit_trail bat
             LEFT JOIN users u ON bat.scanned_by = u.id
             ORDER BY bat.created_at DESC
             LIMIT 20",
            []
        );
    }

    /**
     * Get barcode usage analytics
     */
    private function getBarcodeUsageAnalytics()
    {
        return $this->db->fetchAll(
            "SELECT 
                b.entity_type,
                COUNT(b.id) as total_barcodes,
                SUM(b.scan_count) as total_scans,
                AVG(b.scan_count) as avg_scans,
                COUNT(CASE WHEN b.last_scanned_at IS NOT NULL THEN 1 END) as used_barcodes,
                COUNT(CASE WHEN b.last_scanned_at IS NULL THEN 1 END) as unused_barcodes
             FROM barcodes b
             WHERE b.is_active = 1
             GROUP BY b.entity_type
             ORDER BY total_barcodes DESC",
            []
        );
    }

    /**
     * Get barcode generation trends
     */
    private function getBarcodeGenerationTrends()
    {
        return $this->db->fetchAll(
            "SELECT 
                DATE(b.generated_at) as generation_date,
                COUNT(b.id) as barcodes_generated,
                b.entity_type
             FROM barcodes b
             WHERE b.generated_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
             GROUP BY DATE(b.generated_at), b.entity_type
             ORDER BY generation_date DESC",
            []
        );
    }

    /**
     * Get boxes without barcodes
     */
    private function getBoxesWithoutBarcodes()
    {
        return $this->db->fetchAll(
            "SELECT b.id, b.box_id, b.name, w.name as warehouse_name
             FROM boxes b
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN barcodes bc ON bc.entity_type = 'box' AND bc.entity_id = b.id AND bc.is_active = 1
             WHERE bc.id IS NULL AND b.status = 'active'
             ORDER BY b.created_at DESC
             LIMIT 100",
            []
        );
    }

    /**
     * Get bundles without barcodes
     */
    private function getBundlesWithoutBarcodes()
    {
        return $this->db->fetchAll(
            "SELECT bun.id, bun.name, bun.reference_number
             FROM bundles bun
             LEFT JOIN barcodes bc ON bc.entity_type = 'bundle' AND bc.entity_id = bun.id AND bc.is_active = 1
             WHERE bc.id IS NULL AND bun.status = 'active'
             ORDER BY bun.created_at DESC
             LIMIT 100",
            []
        );
    }

    /**
     * Get documents without barcodes
     */
    private function getDocumentsWithoutBarcodes()
    {
        return $this->db->fetchAll(
            "SELECT d.id, d.title, d.reference_number
             FROM documents d
             LEFT JOIN barcodes bc ON bc.entity_type = 'document' AND bc.entity_id = d.id AND bc.is_active = 1
             WHERE bc.id IS NULL AND d.status != 'deleted'
             ORDER BY d.created_at DESC
             LIMIT 100",
            []
        );
    }

    /**
     * Generate barcode for entity
     */
    private function generateBarcodeForEntity($entityType, $entityId, $barcodeType, $format)
    {
        // Generate unique barcode value
        $barcodeValue = $this->generateUniqueBarcodeValue($entityType, $entityId, $format);
        
        // Insert barcode record
        $barcodeId = $this->db->execute(
            "INSERT INTO barcodes (entity_type, entity_id, barcode_value, barcode_type, generated_by, generated_at)
             VALUES (?, ?, ?, ?, ?, NOW())",
            [$entityType, $entityId, $barcodeValue, $barcodeType, $this->user['id']]
        );

        // Log generation in audit trail
        $this->db->execute(
            "INSERT INTO barcode_audit_trail (barcode_value, barcode_type, entity_id, action, scanned_by, created_at)
             VALUES (?, ?, ?, 'generated', ?, NOW())",
            [$barcodeValue, $entityType, $entityId, $this->user['id']]
        );

        // Log billing event for barcode generation
        // Note: client_id would need to be passed from the calling context
        // For now, we'll try to determine it from the entity
        $clientId = $this->getClientIdForEntity($entityType, $entityId);
        if ($clientId) {
            BillingEventLogger::logBarcodeGenerated(
                $clientId,
                $entityId,
                $entityType,
                $barcodeValue
            );
        }

        return $barcodeValue;
    }

    /**
     * Generate unique barcode value
     */
    private function generateUniqueBarcodeValue($entityType, $entityId, $format)
    {
        $prefix = strtoupper(substr($entityType, 0, 3));
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        
        switch ($format) {
            case 'sequential':
                $sequence = $this->getNextSequenceNumber($entityType);
                return $prefix . str_pad($sequence, 8, '0', STR_PAD_LEFT);
            case 'timestamp':
                return $prefix . $timestamp . $random;
            case 'uuid':
                return $prefix . '-' . $this->generateUUID();
            default: // standard
                return $prefix . str_pad($entityId, 6, '0', STR_PAD_LEFT) . $random;
        }
    }

    /**
     * Additional helper methods for analytics and audit trail
     */
    private function getScanningAnalytics()
    {
        // Implementation for scanning analytics
        return [];
    }

    private function getUsagePatterns()
    {
        // Implementation for usage patterns
        return [];
    }

    private function getPerformanceMetrics()
    {
        // Implementation for performance metrics
        return [];
    }

    private function getErrorAnalytics()
    {
        // Implementation for error analytics
        return [];
    }

    private function getFiltersFromRequest()
    {
        // Implementation for request filters
        return [];
    }

    private function getBarcodeAuditTrail($filters)
    {
        // Implementation for audit trail with filters
        return [];
    }

    private function getAuditStats($filters)
    {
        // Implementation for audit statistics
        return [];
    }

    private function logBulkGenerationActivity($entityType, $successCount, $errorCount)
    {
        // Implementation for logging bulk generation activity
    }

    private function getNextSequenceNumber($entityType)
    {
        $result = $this->db->fetch(
            "SELECT MAX(CAST(SUBSTRING(barcode_value, 4) AS UNSIGNED)) as max_seq 
             FROM barcodes 
             WHERE entity_type = ? AND barcode_value REGEXP '^[A-Z]{3}[0-9]+$'",
            [$entityType]
        );
        
        return ($result['max_seq'] ?? 0) + 1;
    }

    private function generateUUID()
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Get client ID for an entity (for billing purposes)
     */
    private function getClientIdForEntity($entityType, $entityId)
    {
        try {
            switch ($entityType) {
                case 'box':
                    // Get client from box record
                    $result = $this->db->fetch(
                        "SELECT client_id FROM boxes WHERE id = ?",
                        [$entityId]
                    );
                    return $result['client_id'] ?? null;

                case 'bundle':
                    // Get client from bundle record
                    $result = $this->db->fetch(
                        "SELECT client_id FROM bundles WHERE id = ?",
                        [$entityId]
                    );
                    return $result['client_id'] ?? null;

                case 'document':
                    // Get client from document record
                    $result = $this->db->fetch(
                        "SELECT client_id FROM documents WHERE id = ?",
                        [$entityId]
                    );
                    return $result['client_id'] ?? null;

                default:
                    return null;
            }
        } catch (\Exception $e) {
            // Return null if we can't determine client ID
            return null;
        }
    }
}
