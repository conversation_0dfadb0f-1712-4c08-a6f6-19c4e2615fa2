<?php
/**
 * Debug Billing Rates Connection to Invoice Generation
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

// Create a mock user session for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['company_id'] = 1;
    $_SESSION['user_role'] = 'super_admin';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['logged_in'] = true;
}

echo "<h1>🔗 Billing Rates Connection Analysis</h1>";

try {
    $db = App\Core\Database::getInstance();
    
    // Step 1: Check Billing Rates
    echo "<h2>Step 1: Billing Rates Analysis</h2>";
    $rates = $db->fetchAll("SELECT * FROM billing_rates ORDER BY category, service_name");
    
    if (empty($rates)) {
        echo "<p>❌ <strong>No billing rates found!</strong> This is the main issue.</p>";
        echo "<p>🔧 <strong>Solution:</strong> Run the billing setup to create default rates.</p>";
        echo "<p><a href='setup-billing.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Setup Billing Rates</a></p>";
    } else {
        echo "<p>✅ Found " . count($rates) . " billing rates</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>Service Code</th><th>Service Name</th><th>Rate</th><th>Unit</th><th>Category</th><th>Active</th></tr>";
        
        $activeCount = 0;
        foreach ($rates as $rate) {
            $isActive = $rate['is_active'];
            if ($isActive) $activeCount++;
            
            echo "<tr style='background: " . ($isActive ? '#f0f8ff' : '#ffe6e6') . ";'>";
            echo "<td>" . htmlspecialchars($rate['service_code']) . "</td>";
            echo "<td>" . htmlspecialchars($rate['service_name']) . "</td>";
            echo "<td>$" . number_format($rate['rate'], 2) . "</td>";
            echo "<td>" . htmlspecialchars($rate['unit']) . "</td>";
            echo "<td>" . htmlspecialchars($rate['category']) . "</td>";
            echo "<td>" . ($isActive ? '✅ Active' : '❌ Inactive') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Summary:</strong> {$activeCount} active rates, " . (count($rates) - $activeCount) . " inactive rates</p>";
    }
    
    // Step 2: Check Billing Events
    echo "<h2>Step 2: Billing Events Analysis</h2>";
    $events = $db->fetchAll("SELECT be.*, br.service_name FROM billing_events be LEFT JOIN billing_rates br ON be.service_code = br.service_code ORDER BY be.performed_at DESC LIMIT 10");
    
    if (empty($events)) {
        echo "<p>⚠️ <strong>No billing events found!</strong></p>";
        echo "<p>This means no services have been logged for billing yet.</p>";
        echo "<p>🔧 <strong>To test invoice generation, you need billing events.</strong></p>";
        
        // Create test billing events
        echo "<h3>Create Test Billing Events</h3>";
        if (!empty($rates)) {
            $clients = $db->fetchAll("SELECT id, name FROM companies WHERE type = 'client' LIMIT 3");
            
            if (!empty($clients)) {
                echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>Create Test Billing Event</h4>";
                
                echo "<div style='margin: 10px 0;'>";
                echo "<label>Client: <select name='client_id' required style='margin-left: 10px; padding: 5px;'>";
                foreach ($clients as $client) {
                    echo "<option value='{$client['id']}'>" . htmlspecialchars($client['name']) . "</option>";
                }
                echo "</select></label>";
                echo "</div>";
                
                echo "<div style='margin: 10px 0;'>";
                echo "<label>Service: <select name='service_code' required style='margin-left: 10px; padding: 5px;'>";
                foreach ($rates as $rate) {
                    if ($rate['is_active']) {
                        echo "<option value='{$rate['service_code']}'>{$rate['service_name']} (\${$rate['rate']})</option>";
                    }
                }
                echo "</select></label>";
                echo "</div>";
                
                echo "<div style='margin: 10px 0;'>";
                echo "<label>Quantity: <input type='number' name='quantity' value='1' min='1' step='0.01' style='margin-left: 10px; padding: 5px;'></label>";
                echo "</div>";
                
                echo "<button type='submit' name='create_test_event' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>Create Test Event</button>";
                echo "</form>";
            } else {
                echo "<p>❌ No clients found. You need clients to create billing events.</p>";
            }
        }
    } else {
        echo "<p>✅ Found " . count($events) . " recent billing events</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>Date</th><th>Client ID</th><th>Service</th><th>Quantity</th><th>Rate</th><th>Total</th><th>Status</th></tr>";
        
        foreach ($events as $event) {
            echo "<tr>";
            echo "<td>" . date('M j, Y', strtotime($event['performed_at'])) . "</td>";
            echo "<td>" . $event['client_id'] . "</td>";
            echo "<td>" . htmlspecialchars($event['service_name'] ?: $event['service_code']) . "</td>";
            echo "<td>" . $event['quantity'] . "</td>";
            echo "<td>$" . number_format($event['unit_rate'], 2) . "</td>";
            echo "<td>$" . number_format($event['total_amount'], 2) . "</td>";
            echo "<td>" . ucfirst($event['billing_status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 3: Check Connection Between Rates and Events
    echo "<h2>Step 3: Rate-Event Connection Analysis</h2>";
    
    $connectionQuery = "
        SELECT 
            br.service_code,
            br.service_name,
            br.rate as current_rate,
            br.is_active,
            COUNT(be.id) as event_count,
            SUM(be.total_amount) as total_revenue,
            COUNT(CASE WHEN be.billing_status = 'pending' THEN 1 END) as pending_events
        FROM billing_rates br
        LEFT JOIN billing_events be ON br.service_code = be.service_code
        GROUP BY br.service_code, br.service_name, br.rate, br.is_active
        ORDER BY event_count DESC, br.service_name
    ";
    
    $connections = $db->fetchAll($connectionQuery);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Service</th><th>Current Rate</th><th>Events</th><th>Pending Events</th><th>Total Revenue</th><th>Status</th></tr>";
    
    $totalPendingEvents = 0;
    $totalPendingRevenue = 0;
    
    foreach ($connections as $conn) {
        $pendingEvents = (int)$conn['pending_events'];
        $totalPendingEvents += $pendingEvents;
        
        if ($pendingEvents > 0) {
            $pendingRevenue = $pendingEvents * $conn['current_rate'];
            $totalPendingRevenue += $pendingRevenue;
        } else {
            $pendingRevenue = 0;
        }
        
        echo "<tr style='background: " . ($pendingEvents > 0 ? '#fff3cd' : '#f8f9fa') . ";'>";
        echo "<td>" . htmlspecialchars($conn['service_name']) . "</td>";
        echo "<td>$" . number_format($conn['current_rate'], 2) . "</td>";
        echo "<td>" . $conn['event_count'] . "</td>";
        echo "<td>" . ($pendingEvents > 0 ? "<strong>{$pendingEvents}</strong>" : '0') . "</td>";
        echo "<td>$" . number_format($conn['total_revenue'] ?: 0, 2) . "</td>";
        echo "<td>" . ($conn['is_active'] ? '✅ Active' : '❌ Inactive') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📊 Summary</h3>";
    echo "<p><strong>Total Pending Events:</strong> {$totalPendingEvents}</p>";
    echo "<p><strong>Potential Invoice Value:</strong> $" . number_format($totalPendingRevenue, 2) . "</p>";
    
    if ($totalPendingEvents > 0) {
        echo "<p>✅ <strong>Ready for invoice generation!</strong> You have unbilled events.</p>";
    } else {
        echo "<p>⚠️ <strong>No unbilled events found.</strong> Create some billing events first.</p>";
    }
    echo "</div>";
    
    // Step 4: Test Invoice Generation Process
    echo "<h2>Step 4: Invoice Generation Test</h2>";
    
    if ($totalPendingEvents > 0) {
        // Find a client with pending events
        $clientWithEvents = $db->fetch("
            SELECT client_id, COUNT(*) as event_count, SUM(total_amount) as total_amount
            FROM billing_events 
            WHERE billing_status = 'pending' 
            GROUP BY client_id 
            ORDER BY event_count DESC 
            LIMIT 1
        ");
        
        if ($clientWithEvents) {
            $client = $db->fetch("SELECT name FROM companies WHERE id = ?", [$clientWithEvents['client_id']]);
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>🧾 Ready to Generate Invoice</h3>";
            echo "<p><strong>Client:</strong> " . htmlspecialchars($client['name']) . "</p>";
            echo "<p><strong>Pending Events:</strong> {$clientWithEvents['event_count']}</p>";
            echo "<p><strong>Total Amount:</strong> $" . number_format($clientWithEvents['total_amount'], 2) . "</p>";
            
            echo "<form method='POST' action='" . url('/app/billing/generate-invoice') . "' style='margin: 10px 0;'>";
            echo "<input type='hidden' name='_token' value='" . ($_SESSION['csrf_token'] ?? generateToken(32)) . "'>";
            echo "<input type='hidden' name='client_id' value='{$clientWithEvents['client_id']}'>";
            echo "<input type='hidden' name='year' value='" . date('Y') . "'>";
            echo "<input type='hidden' name='month' value='" . date('n') . "'>";
            echo "<button type='submit' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🧾 Generate Invoice Now</button>";
            echo "</form>";
            echo "</div>";
        }
    }
    
    // Handle test event creation
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_event'])) {
        try {
            $clientId = (int)$_POST['client_id'];
            $serviceCode = $_POST['service_code'];
            $quantity = (float)$_POST['quantity'];
            
            // Get the rate
            $rate = $db->fetch("SELECT * FROM billing_rates WHERE service_code = ?", [$serviceCode]);
            
            if ($rate) {
                $totalAmount = $quantity * $rate['rate'];
                
                $eventData = [
                    'company_id' => 1,
                    'client_id' => $clientId,
                    'service_code' => $serviceCode,
                    'event_type' => 'manual_test',
                    'quantity' => $quantity,
                    'unit_rate' => $rate['rate'],
                    'total_amount' => $totalAmount,
                    'performed_by' => 1,
                    'notes' => 'Test event created for billing verification'
                ];
                
                $eventId = $db->insert('billing_events', $eventData);
                
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "✅ <strong>Test billing event created successfully!</strong> Event ID: {$eventId}";
                echo "</div>";
                
                // Refresh the page to show updated data
                echo "<script>setTimeout(() => window.location.reload(), 2000);</script>";
            }
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ <strong>Error creating test event:</strong> " . $e->getMessage();
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Database Error:</strong> " . $e->getMessage() . "</p>";
}

?>

<h2>🎯 Action Items</h2>
<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;">
    <h3>To Fix Invoice Generation:</h3>
    <ol>
        <li><strong>Ensure billing rates exist</strong> - Run setup if needed</li>
        <li><strong>Create billing events</strong> - Log some service usage</li>
        <li><strong>Test invoice generation</strong> - Use the form above</li>
        <li><strong>Verify the connection</strong> - Check that events use correct service codes</li>
    </ol>
</div>

<hr>
<p><a href="<?= url('/app/billing') ?>">← Back to Billing Dashboard</a></p>
<p><a href="<?= url('/app/billing/rates') ?>">🔧 Manage Billing Rates</a></p>
