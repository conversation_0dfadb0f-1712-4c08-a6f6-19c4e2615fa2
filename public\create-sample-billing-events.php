<?php
/**
 * Create Sample Billing Events for Testing Invoice Generation
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

// Create a mock user session for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['company_id'] = 1;
    $_SESSION['user_role'] = 'super_admin';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['logged_in'] = true;
}

echo "<h1>📊 Create Sample Billing Events</h1>";

try {
    $db = App\Core\Database::getInstance();
    
    // Check if we should create sample events
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_samples'])) {
        
        echo "<h2>Creating Sample Billing Events...</h2>";
        
        // Get available clients
        $clients = $db->fetchAll("SELECT id, name FROM companies WHERE type = 'client' LIMIT 5");
        
        if (empty($clients)) {
            echo "<p>❌ No clients found. Creating sample clients first...</p>";
            
            // Create sample clients
            $sampleClients = [
                ['ABC Corporation', '<EMAIL>'],
                ['XYZ Industries', '<EMAIL>'],
                ['Tech Solutions Ltd', '<EMAIL>']
            ];
            
            foreach ($sampleClients as $clientData) {
                $clientId = $db->insert('companies', [
                    'name' => $clientData[0],
                    'email' => $clientData[1],
                    'type' => 'client',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                $clients[] = ['id' => $clientId, 'name' => $clientData[0]];
                echo "<p>✅ Created client: {$clientData[0]} (ID: {$clientId})</p>";
            }
        }
        
        // Get available service rates
        $rates = $db->fetchAll("SELECT * FROM billing_rates WHERE is_active = 1");
        
        if (empty($rates)) {
            echo "<p>❌ No active billing rates found. Please run the billing setup first.</p>";
        } else {
            echo "<p>✅ Found " . count($rates) . " active billing rates</p>";
            
            // Create sample billing events
            $sampleEvents = [];
            $currentMonth = date('Y-m');
            
            foreach ($clients as $client) {
                // Create 3-8 random events per client for the current month
                $eventCount = rand(3, 8);
                
                for ($i = 0; $i < $eventCount; $i++) {
                    $rate = $rates[array_rand($rates)];
                    $quantity = rand(1, 5);
                    $daysAgo = rand(1, 28);
                    
                    $sampleEvents[] = [
                        'company_id' => 1,
                        'client_id' => $client['id'],
                        'service_code' => $rate['service_code'],
                        'event_type' => 'sample_' . strtolower(str_replace(' ', '_', $rate['service_name'])),
                        'quantity' => $quantity,
                        'unit_rate' => $rate['rate'],
                        'total_amount' => $quantity * $rate['rate'],
                        'entity_type' => 'document',
                        'entity_id' => rand(1000, 9999),
                        'reference_number' => 'REF-' . date('Ymd') . '-' . rand(1000, 9999),
                        'billing_status' => 'pending',
                        'performed_by' => 1,
                        'performed_at' => date('Y-m-d H:i:s', strtotime("-{$daysAgo} days")),
                        'notes' => "Sample billing event for testing invoice generation"
                    ];
                }
            }
            
            // Insert the sample events
            $insertedCount = 0;
            $totalAmount = 0;
            
            foreach ($sampleEvents as $event) {
                try {
                    $eventId = $db->insert('billing_events', $event);
                    $insertedCount++;
                    $totalAmount += $event['total_amount'];
                    
                    echo "<p>✅ Created event: {$event['service_code']} for client {$event['client_id']} - $" . number_format($event['total_amount'], 2) . "</p>";
                } catch (Exception $e) {
                    echo "<p>❌ Error creating event: " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>🎉 Sample Data Created Successfully!</h3>";
            echo "<p><strong>Events Created:</strong> {$insertedCount}</p>";
            echo "<p><strong>Total Value:</strong> $" . number_format($totalAmount, 2) . "</p>";
            echo "<p><strong>Clients:</strong> " . count($clients) . "</p>";
            echo "</div>";
            
            echo "<h3>🧾 Ready to Test Invoice Generation</h3>";
            echo "<p>You can now go to the billing dashboard and generate invoices for these clients.</p>";
            echo "<p><a href='" . url('/app/billing') . "' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Billing Dashboard</a></p>";
        }
        
    } else {
        // Show the form to create sample events
        echo "<h2>Generate Sample Billing Data</h2>";
        
        // Check current state
        $eventCount = $db->fetch("SELECT COUNT(*) as count FROM billing_events")['count'];
        $clientCount = $db->fetch("SELECT COUNT(*) as count FROM companies WHERE type = 'client'")['count'];
        $rateCount = $db->fetch("SELECT COUNT(*) as count FROM billing_rates WHERE is_active = 1")['count'];
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>📊 Current State</h3>";
        echo "<p><strong>Billing Events:</strong> {$eventCount}</p>";
        echo "<p><strong>Clients:</strong> {$clientCount}</p>";
        echo "<p><strong>Active Rates:</strong> {$rateCount}</p>";
        echo "</div>";
        
        if ($rateCount == 0) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>⚠️ Missing Billing Rates</h3>";
            echo "<p>You need to set up billing rates first before creating sample events.</p>";
            echo "<p><a href='setup-billing.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Setup Billing Rates</a></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>🚀 Create Sample Data</h3>";
            echo "<p>This will create:</p>";
            echo "<ul>";
            echo "<li><strong>3 sample clients</strong> (if none exist)</li>";
            echo "<li><strong>15-40 billing events</strong> for the current month</li>";
            echo "<li><strong>Random service usage</strong> using your existing rates</li>";
            echo "<li><strong>Pending billing status</strong> ready for invoice generation</li>";
            echo "</ul>";
            
            echo "<form method='POST' style='margin: 15px 0;'>";
            echo "<button type='submit' name='create_samples' style='background: #28a745; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;' onclick='return confirm(\"Create sample billing events for testing?\")'>🎯 Create Sample Billing Events</button>";
            echo "</form>";
            echo "</div>";
        }
        
        // Show existing events if any
        if ($eventCount > 0) {
            echo "<h3>📋 Recent Billing Events</h3>";
            $recentEvents = $db->fetchAll("
                SELECT be.*, br.service_name, c.name as client_name
                FROM billing_events be
                LEFT JOIN billing_rates br ON be.service_code = br.service_code
                LEFT JOIN companies c ON be.client_id = c.id
                ORDER BY be.performed_at DESC
                LIMIT 10
            ");
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>Date</th><th>Client</th><th>Service</th><th>Qty</th><th>Rate</th><th>Total</th><th>Status</th></tr>";
            
            foreach ($recentEvents as $event) {
                echo "<tr>";
                echo "<td>" . date('M j', strtotime($event['performed_at'])) . "</td>";
                echo "<td>" . htmlspecialchars($event['client_name'] ?: 'Client ' . $event['client_id']) . "</td>";
                echo "<td>" . htmlspecialchars($event['service_name'] ?: $event['service_code']) . "</td>";
                echo "<td>" . $event['quantity'] . "</td>";
                echo "<td>$" . number_format($event['unit_rate'], 2) . "</td>";
                echo "<td>$" . number_format($event['total_amount'], 2) . "</td>";
                echo "<td>" . ucfirst($event['billing_status']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}

?>

<hr>
<p><a href="<?= url('/app/billing') ?>">← Back to Billing Dashboard</a></p>
<p><a href="debug-billing-rates-connection.php">🔍 Analyze Billing Connection</a></p>
