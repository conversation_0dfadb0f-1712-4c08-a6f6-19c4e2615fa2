<?php
/**
 * Debug Generate Invoice Functionality
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = generateToken(32);
}

// Create a mock user session for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['company_id'] = 1;
    $_SESSION['user_role'] = 'super_admin';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['logged_in'] = true;
}

echo "<h1>🧾 Generate Invoice Debug</h1>";

// Step 1: Environment Check
echo "<h2>Step 1: Environment Check</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Check</th><th>Value</th><th>Status</th></tr>";
echo "<tr><td>Base Path</td><td>" . BASE_PATH . "</td><td>✅</td></tr>";
echo "<tr><td>Session Active</td><td>" . (session_status() === PHP_SESSION_ACTIVE ? 'Yes' : 'No') . "</td><td>✅</td></tr>";
echo "<tr><td>User Logged In</td><td>" . (isset($_SESSION['logged_in']) ? 'Yes' : 'No') . "</td><td>" . (isset($_SESSION['logged_in']) ? '✅' : '❌') . "</td></tr>";
echo "<tr><td>CSRF Token</td><td>" . (isset($_SESSION['csrf_token']) ? substr($_SESSION['csrf_token'], 0, 10) . '...' : 'Missing') . "</td><td>" . (isset($_SESSION['csrf_token']) ? '✅' : '❌') . "</td></tr>";
echo "</table>";

// Step 2: Route Testing
echo "<h2>Step 2: Route Testing</h2>";
try {
    $router = new \App\Core\Router();
    require_once APP_ROOT . '/src/routes.php';
    
    $testUrl = '/app/billing/generate-invoice';
    echo "<p><strong>Testing Route:</strong> POST {$testUrl}</p>";
    echo "<p>✅ Router initialized successfully</p>";
    echo "<p>✅ Routes loaded successfully</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Router Error: " . $e->getMessage() . "</p>";
}

// Step 3: Controller Testing
echo "<h2>Step 3: Controller Testing</h2>";
try {
    require_once APP_ROOT . '/src/Controllers/BillingController.php';
    $controller = new App\Controllers\BillingController();
    echo "<p>✅ BillingController instantiated successfully</p>";
    
    // Check if method exists
    if (method_exists($controller, 'generateInvoice')) {
        echo "<p>✅ generateInvoice method exists</p>";
    } else {
        echo "<p>❌ generateInvoice method not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Controller Error: " . $e->getMessage() . "</p>";
}

// Step 4: Database Testing
echo "<h2>Step 4: Database Testing</h2>";
try {
    $db = App\Core\Database::getInstance();
    
    // Check for clients
    $clients = $db->fetchAll("SELECT id, name FROM companies WHERE type = 'client' LIMIT 5");
    
    if (!empty($clients)) {
        echo "<p>✅ Database connection successful</p>";
        echo "<p>✅ Found " . count($clients) . " test clients</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>Client ID</th><th>Client Name</th><th>Test Invoice</th></tr>";
        
        foreach ($clients as $client) {
            echo "<tr>";
            echo "<td>" . $client['id'] . "</td>";
            echo "<td>" . htmlspecialchars($client['name']) . "</td>";
            echo "<td><button onclick=\"testInvoiceGeneration(" . $client['id'] . ", '" . htmlspecialchars($client['name']) . "')\" style='background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>Test</button></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $testClient = $clients[0];
    } else {
        echo "<p>❌ No clients found in database</p>";
        $testClient = null;
    }
    
    // Check for billing events
    if ($testClient) {
        $events = $db->fetchAll("SELECT COUNT(*) as count FROM billing_events WHERE client_id = ? AND invoice_id IS NULL", [$testClient['id']]);
        $eventCount = $events[0]['count'] ?? 0;
        echo "<p><strong>Unbilled events for {$testClient['name']}:</strong> {$eventCount}</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database Error: " . $e->getMessage() . "</p>";
}

// Step 5: Form Testing
echo "<h2>Step 5: Generate Invoice Form Test</h2>";

if ($testClient) {
    $generateUrl = url('/app/billing/generate-invoice');
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Test Invoice Generation</h3>";
    echo "<p><strong>Target Client:</strong> {$testClient['name']} (ID: {$testClient['id']})</p>";
    echo "<p><strong>Form Action:</strong> {$generateUrl}</p>";
    echo "<p><strong>Method:</strong> POST</p>";
    
    echo "<form method='POST' action='{$generateUrl}' style='margin: 10px 0;'>";
    echo "<input type='hidden' name='_token' value='{$_SESSION['csrf_token']}'>";
    echo "<input type='hidden' name='client_id' value='{$testClient['id']}'>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label>Year: <input type='number' name='year' value='" . date('Y') . "' min='2020' max='2030' style='margin-left: 10px; padding: 5px;'></label>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label>Month: <select name='month' style='margin-left: 10px; padding: 5px;'>";
    for ($i = 1; $i <= 12; $i++) {
        $selected = ($i == date('n')) ? 'selected' : '';
        echo "<option value='{$i}' {$selected}>" . date('F', mktime(0, 0, 0, $i, 1)) . "</option>";
    }
    echo "</select></label>";
    echo "</div>";
    
    echo "<button type='submit' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px 0;' onclick='return confirm(\"Generate invoice for {$testClient['name']}?\")'>🧾 Generate Test Invoice</button>";
    echo "</form>";
    echo "</div>";
}

// Step 6: JavaScript Testing
echo "<h2>Step 6: JavaScript Testing</h2>";
echo "<div id='js-test-results'></div>";
echo "<button onclick='testJavaScript()' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🧪 Test JavaScript</button>";

// Step 7: Flash Message Check
echo "<h2>Step 7: Flash Message Check</h2>";
if (isset($_SESSION['flash_message'])) {
    $type = $_SESSION['flash_type'] ?? 'info';
    $message = $_SESSION['flash_message'];
    
    echo "<div style='background: " . ($type === 'error' ? '#f8d7da' : '#d4edda') . "; border: 1px solid " . ($type === 'error' ? '#f5c6cb' : '#c3e6cb') . "; color: " . ($type === 'error' ? '#721c24' : '#155724') . "; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Flash Message ({$type}):</strong> " . htmlspecialchars($message);
    echo "</div>";
    
    // Clear flash message
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
} else {
    echo "<p>✅ No flash messages in session</p>";
}

?>

<script>
function testJavaScript() {
    const resultsDiv = document.getElementById('js-test-results');
    let results = '<div style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 10px; border-radius: 5px; margin: 10px 0;">';
    results += '<strong>JavaScript Test Results:</strong><br>';
    
    try {
        // Test URL generation
        const testUrl = '<?= url('/app/billing/generate-invoice') ?>';
        results += '✅ URL generation works: ' + testUrl + '<br>';
        
        // Test form creation
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = testUrl;
        results += '✅ Form creation works<br>';
        
        // Test modal functionality (if exists)
        const modal = document.getElementById('generateInvoiceModal');
        if (modal) {
            results += '✅ Generate Invoice Modal found<br>';
        } else {
            results += '⚠️ Generate Invoice Modal not found (normal for debug page)<br>';
        }
        
        results += '✅ All JavaScript tests passed<br>';
        
    } catch (error) {
        results += '❌ JavaScript error: ' + error.message + '<br>';
        console.error('JavaScript test error:', error);
    }
    
    results += '</div>';
    resultsDiv.innerHTML = results;
}

function testInvoiceGeneration(clientId, clientName) {
    const message = `Test invoice generation for client: ${clientName} (ID: ${clientId})\n\nThis will attempt to generate an invoice for the current month.`;
    
    if (confirm(message)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= url('/app/billing/generate-invoice') ?>';
        form.style.display = 'none';
        
        // Add CSRF token
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '<?= $_SESSION['csrf_token'] ?>';
        form.appendChild(tokenInput);
        
        // Add client ID
        const clientInput = document.createElement('input');
        clientInput.type = 'hidden';
        clientInput.name = 'client_id';
        clientInput.value = clientId;
        form.appendChild(clientInput);
        
        // Add current year
        const yearInput = document.createElement('input');
        yearInput.type = 'hidden';
        yearInput.name = 'year';
        yearInput.value = new Date().getFullYear();
        form.appendChild(yearInput);
        
        // Add current month
        const monthInput = document.createElement('input');
        monthInput.type = 'hidden';
        monthInput.name = 'month';
        monthInput.value = new Date().getMonth() + 1;
        form.appendChild(monthInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-run JavaScript test on page load
document.addEventListener('DOMContentLoaded', function() {
    testJavaScript();
});
</script>

<h2>Step 8: Instructions</h2>
<div style="background: #cce5ff; border: 1px solid #99ccff; padding: 15px; border-radius: 5px;">
    <h3>🎯 What to do:</h3>
    <ol>
        <li><strong>Check all steps above</strong> - Look for any ❌ errors</li>
        <li><strong>Try the "Generate Test Invoice" form</strong> - Fill in year/month and submit</li>
        <li><strong>Try the "Test" buttons</strong> for individual clients</li>
        <li><strong>Check Flash Messages</strong> - See what error/success messages appear</li>
        <li><strong>Report back</strong> - Tell me exactly what happens when you try to generate an invoice</li>
    </ol>
</div>

<hr>
<p><a href="<?= url('/app/billing') ?>">← Back to Billing Dashboard</a></p>
