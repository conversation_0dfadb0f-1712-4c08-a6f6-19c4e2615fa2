<?php
/**
 * Step-by-step Delete Debug
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';
$dbConfig = require_once CONFIG_PATH . '/database.php';

// Initialize database
$db = App\Core\Database::getInstance();

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

echo "<h1>Step-by-Step Delete Debug</h1>";

// Step 1: Test URL generation
echo "<h2>Step 1: URL Generation Test</h2>";
echo "<p><strong>Base Path:</strong> " . (BASE_PATH ?: '(empty)') . "</p>";
echo "<p><strong>Delete URL for rate ID 1:</strong> " . url('/app/billing/rates/delete/1') . "</p>";

// Step 2: Test database connection and get rates
echo "<h2>Step 2: Database Connection & Available Rates</h2>";
try {
    $rates = $db->fetchAll("SELECT * FROM billing_rates ORDER BY id LIMIT 3");
    
    if (empty($rates)) {
        echo "<p>❌ No service rates found in database.</p>";
    } else {
        echo "<p>✅ Found " . count($rates) . " rates</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Service Name</th><th>Rate</th><th>Active</th></tr>";
        
        foreach ($rates as $rate) {
            echo "<tr>";
            echo "<td>" . $rate['id'] . "</td>";
            echo "<td>" . htmlspecialchars($rate['service_name']) . "</td>";
            echo "<td>$" . number_format($rate['rate'], 2) . "</td>";
            echo "<td>" . ($rate['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}

// Step 3: Test direct delete URL access
echo "<h2>Step 3: Direct Delete URL Test</h2>";
if (!empty($rates)) {
    $testRate = $rates[0];
    $deleteUrl = url('/app/billing/rates/delete/' . $testRate['id']);
    echo "<p><strong>Test Rate:</strong> " . htmlspecialchars($testRate['service_name']) . " (ID: {$testRate['id']})</p>";
    echo "<p><strong>Delete URL:</strong> <code>{$deleteUrl}</code></p>";
    
    echo "<form method='POST' action='{$deleteUrl}' style='margin: 10px 0;'>";
    echo "<input type='hidden' name='rate_id' value='{$testRate['id']}'>";
    echo "<button type='submit' style='background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"Test delete for {$testRate['service_name']}?\")'>🧪 Test Direct Delete</button>";
    echo "</form>";
}

// Step 4: Check for flash messages
echo "<h2>Step 4: Flash Message Check</h2>";
if (isset($_SESSION['flash_message'])) {
    echo "<div style='background: " . ($_SESSION['flash_type'] === 'error' ? '#dc3545' : '#28a745') . "; color: white; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Flash Message:</strong> " . htmlspecialchars($_SESSION['flash_message']);
    echo "</div>";
    
    // Clear the flash message
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
} else {
    echo "<p>✅ No flash messages in session</p>";
}

// Step 5: Check server logs
echo "<h2>Step 5: Recent Error Logs</h2>";
$errorLogPath = ini_get('error_log');
if ($errorLogPath && file_exists($errorLogPath)) {
    $logLines = file($errorLogPath);
    $recentLines = array_slice($logLines, -10); // Last 10 lines
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    echo "<strong>Last 10 lines from error log:</strong><br>";
    foreach ($recentLines as $line) {
        echo htmlspecialchars($line) . "<br>";
    }
    echo "</div>";
} else {
    echo "<p>⚠️ Error log not found or not accessible</p>";
}

// Step 6: Test the controller directly
echo "<h2>Step 6: Controller Test</h2>";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_controller'])) {
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Controller Test Results:</strong><br>";
    
    try {
        // Simulate the controller call
        require_once APP_ROOT . '/src/Controllers/BillingController.php';
        
        // Create a mock user session
        $_SESSION['user'] = [
            'id' => 1,
            'role' => 'super_admin',
            'company_id' => 1
        ];
        
        $controller = new App\Controllers\BillingController();
        
        echo "✅ Controller instantiated successfully<br>";
        echo "✅ User session created<br>";
        echo "✅ Ready to test delete operation<br>";
        
    } catch (Exception $e) {
        echo "❌ Controller test failed: " . $e->getMessage() . "<br>";
    }
    
    echo "</div>";
}

echo "<form method='POST'>";
echo "<input type='hidden' name='test_controller' value='1'>";
echo "<button type='submit' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px 0;'>🧪 Test Controller</button>";
echo "</form>";

// Step 7: JavaScript test
echo "<h2>Step 7: JavaScript Test</h2>";
echo "<div id='js-test-results'></div>";
echo "<button onclick='testJavaScript()' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🧪 Test JavaScript</button>";

?>

<script>
function testJavaScript() {
    const resultsDiv = document.getElementById('js-test-results');
    let results = '<div style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 10px; border-radius: 5px; margin: 10px 0;">';
    results += '<strong>JavaScript Test Results:</strong><br>';
    
    try {
        // Test URL generation
        const testUrl = '<?= url('/app/billing/rates/delete/1') ?>';
        results += '✅ URL generation works: ' + testUrl + '<br>';
        
        // Test CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            results += '✅ CSRF token found: ' + csrfToken.getAttribute('content').substring(0, 10) + '...<br>';
        } else {
            results += '❌ CSRF token not found<br>';
        }
        
        // Test form creation
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = testUrl;
        results += '✅ Form creation works<br>';
        
        // Test console logging
        console.log('JavaScript test completed successfully');
        results += '✅ Console logging works (check browser console)<br>';
        
        results += '✅ All JavaScript tests passed<br>';
        
    } catch (error) {
        results += '❌ JavaScript error: ' + error.message + '<br>';
        console.error('JavaScript test error:', error);
    }
    
    results += '</div>';
    resultsDiv.innerHTML = results;
}
</script>

<hr>
<p><strong>Instructions:</strong></p>
<ol>
    <li>Check each step above for any errors</li>
    <li>Try the "Test Direct Delete" button to see what happens</li>
    <li>Check the browser console (F12) for any JavaScript errors</li>
    <li>Look at the flash message section to see if there are any error messages</li>
</ol>

<p><a href="<?= url('/app/billing/rates') ?>">← Back to Service Rates</a></p>
