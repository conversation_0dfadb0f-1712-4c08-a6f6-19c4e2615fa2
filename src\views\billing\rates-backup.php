<?php
$title = 'Service Rates';
$content = ob_start();
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
                <div class="flex items-center gap-3 mb-2">
                    <a href="<?= url('/app/billing') ?>" class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Back to Billing
                    </a>
                </div>
                <h1 class="text-3xl font-bold text-gray-900">Service Rates</h1>
                <p class="text-gray-600 mt-1">Manage billing rates for all services</p>
            </div>
            <div>
                <button type="button" onclick="openAddRateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add New Rate
                </button>
            </div>
        </div>
    </div>

    <!-- Service Rates by Category -->
    <?php if (empty($rates_by_category)): ?>
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-12 text-center">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Service Rates Found</h3>
                <p class="text-gray-500 mb-6">Add your first service rate to get started with billing.</p>
                <button type="button" onclick="openAddRateModal()" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Service Rate
                </button>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($rates_by_category as $category => $rates): ?>
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <?= ucfirst(str_replace('_', ' ', $category)) ?> Services
                    </h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($rates as $rate): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono"><?= htmlspecialchars($rate['service_code']) ?></code>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($rate['service_name']) ?></div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-500"><?= htmlspecialchars($rate['service_description']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-semibold text-blue-600">$<?= number_format($rate['rate'], 2) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"><?= htmlspecialchars($rate['unit']) ?></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($rate['is_active']): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button type="button" onclick="editRate(<?= htmlspecialchars(json_encode($rate)) ?>)" class="inline-flex items-center px-2 py-1 border border-blue-300 rounded text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors" title="Edit Rate">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </button>
                                            <?php if ($rate['is_active']): ?>
                                                <button type="button" onclick="toggleRateStatus(<?= $rate['id'] ?>, false)" class="inline-flex items-center px-2 py-1 border border-yellow-300 rounded text-yellow-700 bg-yellow-50 hover:bg-yellow-100 transition-colors" title="Deactivate Rate">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </button>
                                            <?php else: ?>
                                                <button type="button" onclick="toggleRateStatus(<?= $rate['id'] ?>, true)" class="inline-flex items-center px-2 py-1 border border-green-300 rounded text-green-700 bg-green-50 hover:bg-green-100 transition-colors" title="Activate Rate">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </button>
                                            <?php endif; ?>
                                            <button type="button" onclick="deleteRate(<?= $rate['id'] ?>, '<?= htmlspecialchars($rate['service_name']) ?>')" class="inline-flex items-center px-2 py-1 border border-red-300 rounded text-red-700 bg-red-50 hover:bg-red-100 transition-colors" title="Delete Rate">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-[60]">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-2xl rounded-2xl bg-white">
        <div class="mt-3">
            <!-- Header -->
            <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>

            <!-- Content -->
            <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete Service Rate</h3>
                <p class="text-sm text-gray-600 mb-1">Are you sure you want to delete:</p>
                <p class="text-base font-medium text-gray-900 mb-4" id="deleteItemName"></p>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div class="text-left">
                            <p class="text-sm font-medium text-yellow-800">Warning</p>
                            <p class="text-sm text-yellow-700">This action cannot be undone. If this rate is being used in billing events, the deletion will be prevented.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex space-x-3">
                <button type="button" onclick="deleteManager.closeModal()" class="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    Cancel
                </button>
                <button type="button" onclick="deleteManager.confirmDelete()" class="flex-1 px-4 py-2.5 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete Rate
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Rate Modal -->
<div id="addRateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="rateModalTitle">Add New Service Rate</h3>
                <button type="button" onclick="closeAddRateModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="rateForm" method="POST" action="<?= url('/app/billing/rates/save') ?>">
                <input type="hidden" id="rate_id" name="rate_id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="service_code" class="block text-sm font-medium text-gray-700 mb-2">Service Code</label>
                        <input type="text" id="service_code" name="service_code" placeholder="e.g., intake.new" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="text-xs text-gray-500 mt-1">Use lowercase with dots for hierarchy (e.g., category.action)</p>
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select id="category" name="category" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select category...</option>
                            <option value="processing">Processing</option>
                            <option value="handling">Handling</option>
                            <option value="search">Search</option>
                            <option value="delivery">Delivery</option>
                            <option value="storage">Storage</option>
                            <option value="barcode">Barcode</option>
                            <option value="supplies">Supplies</option>
                            <option value="digital">Digital</option>
                            <option value="special">Special</option>
                        </select>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="service_name" class="block text-sm font-medium text-gray-700 mb-2">Service Name</label>
                    <input type="text" id="service_name" name="service_name" placeholder="e.g., New Document Intake" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="mb-4">
                    <label for="service_description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="service_description" name="service_description" rows="2" placeholder="Brief description of the service"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="rate" class="block text-sm font-medium text-gray-700 mb-2">Rate ($)</label>
                        <input type="number" id="rate" name="rate" step="0.01" min="0" placeholder="0.00" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="unit" class="block text-sm font-medium text-gray-700 mb-2">Unit</label>
                        <select id="unit" name="unit" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="each">Each</option>
                            <option value="hour">Hour</option>
                            <option value="day">Day</option>
                            <option value="week">Week</option>
                            <option value="month">Month</option>
                            <option value="kg">Kilogram</option>
                            <option value="page">Page</option>
                            <option value="box">Box</option>
                            <option value="bundle">Bundle</option>
                        </select>
                    </div>
                </div>

                <div class="mb-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_active" name="is_active" checked
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-900">
                            Active (available for billing)
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeAddRateModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                        </svg>
                        Save Rate
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openAddRateModal() {
    const modal = document.getElementById('addRateModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

function closeAddRateModal() {
    const modal = document.getElementById('addRateModal');
    if (modal) {
        modal.classList.add('hidden');
        // Reset form
        document.getElementById('rateForm').reset();
        document.getElementById('rate_id').value = '';
        document.getElementById('rateModalTitle').textContent = 'Add New Service Rate';
    }
}

function editRate(rate) {
    document.getElementById('rateModalTitle').textContent = 'Edit Service Rate';
    document.getElementById('rate_id').value = rate.id;
    document.getElementById('service_code').value = rate.service_code;
    document.getElementById('service_name').value = rate.service_name;
    document.getElementById('service_description').value = rate.service_description || '';
    document.getElementById('rate').value = rate.rate;
    document.getElementById('unit').value = rate.unit;
    document.getElementById('category').value = rate.category;
    document.getElementById('is_active').checked = rate.is_active == 1;

    openAddRateModal();
}

function toggleRateStatus(rateId, isActive) {
    const action = isActive ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this service rate?`)) {
        // Here you would make an AJAX call to update the rate status
        // For now, we'll just reload the page
        window.location.href = `<?= url('/app/billing/rates/toggle/') ?>${rateId}?active=${isActive ? 1 : 0}`;
    }
}

// Delete functionality with improved error handling
class DeleteRateManager {
    constructor() {
        this.currentRateId = null;
        this.currentServiceName = null;
        this.isProcessing = false;
    }

    // Initialize delete process
    initDelete(rateId, serviceName) {
        try {
            // Validate inputs
            if (!rateId || !serviceName) {
                this.showError('Invalid rate information provided');
                return;
            }

            // Store rate information
            this.currentRateId = parseInt(rateId);
            this.currentServiceName = serviceName;

            // Update modal content
            const nameElement = document.getElementById('deleteItemName');
            if (nameElement) {
                nameElement.textContent = serviceName;
            }

            // Show confirmation modal
            this.showModal();

            console.log('Delete initialized for rate:', this.currentRateId, this.currentServiceName);
        } catch (error) {
            console.error('Error initializing delete:', error);
            this.showError('Failed to initialize delete process');
        }
    }

    // Show the confirmation modal
    showModal() {
        const modal = document.getElementById('deleteConfirmModal');
        if (modal) {
            modal.classList.remove('hidden');
            // Focus on cancel button for accessibility
            const cancelBtn = modal.querySelector('button[onclick="deleteManager.closeModal()"]');
            if (cancelBtn) {
                setTimeout(() => cancelBtn.focus(), 100);
            }
        }
    }

    // Close the confirmation modal
    closeModal() {
        const modal = document.getElementById('deleteConfirmModal');
        if (modal) {
            modal.classList.add('hidden');
        }
        this.reset();
    }

    // Reset the manager state
    reset() {
        this.currentRateId = null;
        this.currentServiceName = null;
        this.isProcessing = false;
    }

    // Confirm and execute delete
    async confirmDelete() {
        if (this.isProcessing) {
            console.log('Delete already in progress');
            return;
        }

        if (!this.currentRateId) {
            this.showError('No rate selected for deletion');
            this.closeModal();
            return;
        }

        try {
            this.isProcessing = true;

            // Show loading state
            this.setLoadingState(true);

            // Execute delete
            await this.executeDelete();

        } catch (error) {
            console.error('Delete confirmation error:', error);
            this.showError('Failed to delete service rate: ' + error.message);
        } finally {
            this.isProcessing = false;
            this.setLoadingState(false);
            this.closeModal();
        }
    }

    // Execute the actual delete operation
    async executeDelete() {
        return new Promise((resolve, reject) => {
            try {
                // Generate delete URL
                const deleteUrl = `<?= url('/app/billing/rates/delete/') ?>${this.currentRateId}`;
                console.log('Executing delete to URL:', deleteUrl);

                // Create and configure form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = deleteUrl;
                form.style.display = 'none';

                // Add CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (csrfToken && csrfToken.getAttribute('content')) {
                    const tokenInput = document.createElement('input');
                    tokenInput.type = 'hidden';
                    tokenInput.name = '_token';
                    tokenInput.value = csrfToken.getAttribute('content');
                    form.appendChild(tokenInput);
                    console.log('CSRF token added');
                } else {
                    console.warn('CSRF token not found');
                }

                // Add rate ID as hidden field for extra validation
                const rateIdInput = document.createElement('input');
                rateIdInput.type = 'hidden';
                rateIdInput.name = 'rate_id';
                rateIdInput.value = this.currentRateId;
                form.appendChild(rateIdInput);

                // Submit form
                document.body.appendChild(form);
                console.log('Submitting delete form');
                form.submit();

                resolve();
            } catch (error) {
                reject(error);
            }
        });
    }

    // Set loading state for buttons
    setLoadingState(loading) {
        const confirmBtn = document.querySelector('button[onclick="deleteManager.confirmDelete()"]');
        const cancelBtn = document.querySelector('button[onclick="deleteManager.closeModal()"]');

        if (confirmBtn) {
            if (loading) {
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-2 inline animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Deleting...
                `;
            } else {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = `
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete Rate
                `;
            }
        }

        if (cancelBtn) {
            cancelBtn.disabled = loading;
        }
    }

    // Show error message
    showError(message) {
        console.error('Delete error:', message);

        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-20 right-4 z-[9999] max-w-sm';
        errorDiv.innerHTML = `
            <div class="bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg border border-red-600">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>${this.escapeHtml(message)}</span>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200 focus:outline-none">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(errorDiv);

        // Auto-remove after 7 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 7000);
    }

    // Escape HTML to prevent XSS
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Create global instance
const deleteManager = new DeleteRateManager();

// Legacy function for backward compatibility
function deleteRate(rateId, serviceName) {
    deleteManager.initDelete(rateId, serviceName);
}

function closeDeleteModal() {
    deleteManager.closeModal();
}

function confirmDelete() {
    deleteManager.confirmDelete();
}

// Enhanced modal management
document.addEventListener('DOMContentLoaded', function() {
    // Close modals when clicking outside
    document.addEventListener('click', function(event) {
        const addModal = document.getElementById('addRateModal');
        const deleteModal = document.getElementById('deleteConfirmModal');

        if (addModal && event.target === addModal) {
            closeAddRateModal();
        }

        if (deleteModal && event.target === deleteModal) {
            deleteManager.closeModal();
        }
    });

    // Close modals with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const addModal = document.getElementById('addRateModal');
            const deleteModal = document.getElementById('deleteConfirmModal');

            if (addModal && !addModal.classList.contains('hidden')) {
                closeAddRateModal();
            }

            if (deleteModal && !deleteModal.classList.contains('hidden')) {
                deleteManager.closeModal();
            }
        }
    });

    // Prevent form submission on Enter key in delete modal
    const deleteModal = document.getElementById('deleteConfirmModal');
    if (deleteModal) {
        deleteModal.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                deleteManager.confirmDelete();
            }
        });
    }

    console.log('Delete rate management system initialized');
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
