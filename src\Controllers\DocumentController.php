<?php

namespace App\Controllers;

use App\Services\BillingEventLogger;

/**
 * Document Controller - Phase 2 Implementation
 * 
 * Handles document management features including:
 * - File upload with validation
 * - Document CRUD operations
 * - Metadata management
 * - Version control
 * - Document preview
 * - Advanced search and filtering
 */
class DocumentController extends BaseController
{
    private $allowedMimeTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        'video/mp4',
        'video/avi',
        'video/quicktime',
        'audio/mpeg',
        'audio/wav',
        'application/zip',
        'application/x-rar-compressed'
    ];

    private $maxFileSize = 100 * 1024 * 1024; // 100MB

    /**
     * Redirect to boxes page since documents page is redundant
     * The workflow is INTAKE → BUNDLE → BOX (storage is just location info)
     */
    public function index()
    {
        $this->requireAuth();

        // Redirect to boxes page which handles the final stage of the workflow
        $this->redirect('/app/boxes');
    }

    /**
     * Show document upload form
     */
    public function create()
    {
        $this->requireAuth();

        // Get available storage locations
        $locations = $this->getStorageLocations();
        $categories = $this->getDocumentCategories();

        // Check for bundle_id parameter
        $bundleId = $_GET['bundle_id'] ?? null;
        $bundle = null;

        if ($bundleId) {
            // Get bundle information for context
            $bundle = $this->db->fetch(
                "SELECT id, name, reference_number FROM bundles WHERE id = ? AND company_id = ?",
                [$bundleId, $this->user['company_id']]
            );
        }

        $this->view('documents/create', [
            'title' => 'Upload Document',
            'locations' => $locations,
            'categories' => $categories,
            'bundle_id' => $bundleId,
            'bundle' => $bundle
        ]);
    }

    /**
     * Handle document upload with advanced validation
     */
    public function store()
    {
        $this->requireAuth();
        
        try {
            // Validate request
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new \Exception('Invalid request method');
            }

            // Check if files were uploaded
            if (empty($_FILES['documents']['name'][0])) {
                throw new \Exception('No files selected for upload');
            }

            $uploadedDocuments = [];
            $errors = [];

            // Process multiple file uploads
            for ($i = 0; $i < count($_FILES['documents']['name']); $i++) {
                if ($_FILES['documents']['error'][$i] === UPLOAD_ERR_OK) {
                    try {
                        $document = $this->processFileUpload($i);
                        $uploadedDocuments[] = $document;

                        // Log billing event for document processing
                        if (!empty($_POST['client_id'])) {
                            BillingEventLogger::logDocumentProcessing(
                                $_POST['client_id'],
                                $document['id'],
                                'upload',
                                $document['reference_number'] ?? null
                            );
                        }
                    } catch (\Exception $e) {
                        $errors[] = $_FILES['documents']['name'][$i] . ': ' . $e->getMessage();
                    }
                }
            }

            if (!empty($uploadedDocuments)) {
                $message = count($uploadedDocuments) . ' document(s) uploaded successfully';
                if (!empty($errors)) {
                    $message .= '. Some files failed: ' . implode(', ', $errors);
                }
                $this->setFlashMessage($message, 'success');

                // Redirect back to bundle if bundle_id was provided
                $bundleId = $_POST['bundle_id'] ?? null;
                if ($bundleId) {
                    $this->redirect('/app/bundles/' . $bundleId);
                } else {
                    $this->redirect('/app/documents');
                }
            } else {
                $this->setFlashMessage('No documents were uploaded. Errors: ' . implode(', ', $errors), 'error');
                $this->redirect('/app/documents/create');
            }

        } catch (\Exception $e) {
            $this->setFlashMessage('Upload failed: ' . $e->getMessage(), 'error');
            $this->redirect('/app/documents/create');
        }
    }

    /**
     * Show document details with version history
     */
    public function show($id)
    {
        $this->requireAuth();
        
        $document = $this->getDocumentById($id);
        if (!$document) {
            $this->setFlashMessage('Document not found', 'error');
            $this->redirect('/app/documents');
            return;
        }

        // Get version history
        $versions = $this->getDocumentVersions($id);
        
        // Get document location
        $location = $this->getDocumentLocation($id);
        
        // Get related documents
        $relatedDocuments = $this->getRelatedDocuments($id);

        $this->view('documents/show', [
            'title' => $document['title'],
            'document' => $document,
            'versions' => $versions,
            'location' => $location,
            'relatedDocuments' => $relatedDocuments
        ]);
    }

    /**
     * Show document edit form
     */
    public function edit($id)
    {
        $this->requireAuth();
        
        $document = $this->getDocumentById($id);
        if (!$document) {
            $this->setFlashMessage('Document not found', 'error');
            $this->redirect('/app/documents');
            return;
        }

        $locations = $this->getStorageLocations();
        $categories = $this->getDocumentCategories();

        $this->view('documents/edit', [
            'title' => 'Edit Document',
            'document' => $document,
            'locations' => $locations,
            'categories' => $categories
        ]);
    }

    /**
     * Update document metadata
     */
    public function update($id)
    {
        $this->requireAuth();
        
        try {
            $document = $this->getDocumentById($id);
            if (!$document) {
                throw new \Exception('Document not found');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'title' => 'required|max:500',
                'description' => 'max:2000',
                'category_id' => 'integer',
                'tags' => 'max:1000',
                'location_id' => 'integer'
            ]);

            // Update document
            $this->db->execute(
                "UPDATE documents SET 
                 title = ?, description = ?, category_id = ?, tags = ?, updated_at = NOW()
                 WHERE id = ? AND company_id = ?",
                [
                    $data['title'],
                    $data['description'] ?? null,
                    $data['category_id'] ?? null,
                    $data['tags'] ?? null,
                    $id,
                    $this->user['company_id']
                ]
            );

            // Update location if provided
            if (!empty($data['location_id'])) {
                $this->updateDocumentLocation($id, $data['location_id']);
            }

            // Log activity
            $this->logActivity('update', 'document', $id, "Updated document: {$data['title']}");

            $this->setFlashMessage('Document updated successfully', 'success');
            $this->redirect("/app/documents/{$id}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Update failed: ' . $e->getMessage(), 'error');
            $this->redirect("/app/documents/{$id}/edit");
        }
    }

    /**
     * Delete document (soft delete)
     */
    public function delete($id)
    {
        $this->requireAuth();
        
        try {
            $document = $this->getDocumentById($id);
            if (!$document) {
                throw new \Exception('Document not found');
            }

            // Soft delete
            $this->db->execute(
                "UPDATE documents SET status = 'deleted', updated_at = NOW() 
                 WHERE id = ? AND company_id = ?",
                [$id, $this->user['company_id']]
            );

            // Log activity
            $this->logActivity('delete', 'document', $id, "Deleted document: {$document['title']}");

            $this->setFlashMessage('Document deleted successfully', 'success');
            $this->redirect('/app/documents');

        } catch (\Exception $e) {
            $this->setFlashMessage('Delete failed: ' . $e->getMessage(), 'error');
            $this->redirect("/app/documents/{$id}");
        }
    }

    /**
     * Download document file
     */
    public function download($id)
    {
        $this->requireAuth();
        
        try {
            $document = $this->getDocumentById($id);
            if (!$document) {
                throw new \Exception('Document not found');
            }

            $filePath = UPLOAD_PATH . '/' . $document['file_path'];
            if (!file_exists($filePath)) {
                throw new \Exception('File not found on disk');
            }

            // Update download count
            $this->db->execute(
                "UPDATE documents SET download_count = download_count + 1 WHERE id = ?",
                [$id]
            );

            // Log activity
            $this->logActivity('download', 'document', $id, "Downloaded document: {$document['title']}");

            // Log billing event for document access
            if (!empty($document['client_id'])) {
                BillingEventLogger::logDocumentAccess(
                    $document['client_id'],
                    $id,
                    'download',
                    $document['reference_number'] ?? null
                );
            }

            // Send file
            header('Content-Type: ' . $document['mime_type']);
            header('Content-Disposition: attachment; filename="' . $document['file_name'] . '"');
            header('Content-Length: ' . $document['file_size']);
            readfile($filePath);
            exit;

        } catch (\Exception $e) {
            $this->setFlashMessage('Download failed: ' . $e->getMessage(), 'error');
            $this->redirect('/app/documents');
        }
    }

    /**
     * Get document by ID
     */
    private function getDocumentById($id)
    {
        return $this->db->fetch(
            "SELECT d.*, u.first_name, u.last_name, c.name as category_name
             FROM documents d
             LEFT JOIN users u ON d.created_by = u.id
             LEFT JOIN categories c ON d.category_id = c.id
             WHERE d.id = ? AND d.company_id = ? AND d.status != 'deleted'",
            [$id, $this->user['company_id']]
        );
    }

    /**
     * Get document versions
     */
    private function getDocumentVersions($documentId)
    {
        // Since there's no parent_id column for versioning, just return the current document
        // This can be enhanced later when proper versioning is implemented
        return $this->db->fetchAll(
            "SELECT d.*, u.first_name, u.last_name
             FROM documents d
             LEFT JOIN users u ON d.created_by = u.id
             WHERE d.id = ? AND d.company_id = ?
             ORDER BY d.version DESC",
            [$documentId, $this->user['company_id']]
        );
    }

    /**
     * Get document location
     */
    private function getDocumentLocation($documentId)
    {
        // Get location information from the documents table directly
        return $this->db->fetch(
            "SELECT d.location_id, sl.name as location_name, sl.identifier,
                    w.name as warehouse_name, w.address as warehouse_address
             FROM documents d
             LEFT JOIN storage_locations sl ON d.location_id = sl.id
             LEFT JOIN warehouses w ON sl.warehouse_id = w.id
             WHERE d.id = ? AND d.company_id = ?",
            [$documentId, $this->user['company_id']]
        );
    }

    /**
     * Get related documents based on tags and category
     */
    private function getRelatedDocuments($documentId)
    {
        $document = $this->getDocumentById($documentId);
        if (!$document) return [];

        $where = ["d.company_id = ?", "d.id != ?", "d.status != 'deleted'"];
        $params = [$this->user['company_id'], $documentId];

        if ($document['category_id']) {
            $where[] = "d.category_id = ?";
            $params[] = $document['category_id'];
        }

        $sql = "SELECT d.id, d.title, d.file_name, d.created_at
                FROM documents d
                WHERE " . implode(' AND ', $where) . "
                ORDER BY d.created_at DESC
                LIMIT 5";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get storage locations for dropdown
     */
    private function getStorageLocations()
    {
        return $this->db->fetchAll(
            "SELECT sl.id, sl.name, sl.identifier, w.name as warehouse_name
             FROM storage_locations sl
             JOIN warehouses w ON sl.warehouse_id = w.id
             WHERE w.company_id = ? AND sl.status = 'active' AND sl.type = 'box'
             ORDER BY w.name, sl.name",
            [$this->user['company_id']]
        );
    }



    /**
     * Get storage statistics for Storage Management page
     */
    private function getStorageStatistics()
    {
        try {
            // Get total counts following INTAKE → BUNDLE → BOX → STORAGE workflow
            $stats = [];

            // Total documents in storage
            $stats['total_documents'] = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM documents WHERE company_id = ? AND status != 'deleted'",
                [$this->user['company_id']]
            );

            // Active bundles
            $stats['active_bundles'] = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM bundles WHERE company_id = ? AND status = 'active'",
                [$this->user['company_id']]
            );

            // Storage boxes
            $stats['storage_boxes'] = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM boxes WHERE company_id = ? AND status != 'archived'",
                [$this->user['company_id']]
            );

            // Warehouses
            $stats['warehouses'] = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM warehouses WHERE company_id = ? AND status = 'active'",
                [$this->user['company_id']]
            );

            // Storage utilization
            $stats['storage_utilization'] = $this->db->fetch(
                "SELECT
                    COUNT(DISTINCT b.id) as total_boxes,
                    COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') THEN b.id END) as occupied_boxes,
                    ROUND(
                        (COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') THEN b.id END) /
                         NULLIF(COUNT(DISTINCT b.id), 0)) * 100, 1
                    ) as utilization_percentage
                 FROM boxes b
                 WHERE b.company_id = ? AND b.status != 'archived'",
                [$this->user['company_id']]
            );

            return $stats;

        } catch (\Exception $e) {
            // Return default stats if there's an error
            return [
                'total_documents' => 0,
                'active_bundles' => 0,
                'storage_boxes' => 0,
                'warehouses' => 0,
                'storage_utilization' => [
                    'total_boxes' => 0,
                    'occupied_boxes' => 0,
                    'utilization_percentage' => 0
                ]
            ];
        }
    }

    /**
     * Get document categories
     */
    private function getDocumentCategories()
    {
        // First try with status column, fallback to is_active if status doesn't exist
        try {
            return $this->db->fetchAll(
                "SELECT id, name, description
                 FROM categories
                 WHERE company_id = ? AND status = 'active'
                 ORDER BY name",
                [$this->user['company_id']]
            );
        } catch (\Exception $e) {
            // Fallback to is_active column if status column doesn't exist
            return $this->db->fetchAll(
                "SELECT id, name, description
                 FROM categories
                 WHERE company_id = ? AND is_active = 1
                 ORDER BY name",
                [$this->user['company_id']]
            );
        }
    }

    /**
     * Get document types for filtering
     */
    private function getDocumentTypes()
    {
        return [
            'contract' => 'Contract',
            'invoice' => 'Invoice',
            'report' => 'Report',
            'image' => 'Image',
            'video' => 'Video',
            'audio' => 'Audio',
            'spreadsheet' => 'Spreadsheet',
            'presentation' => 'Presentation',
            'other' => 'Other'
        ];
    }

    /**
     * Detect document type based on MIME type
     */
    private function detectDocumentType($mimeType)
    {
        $typeMap = [
            'application/pdf' => 'report',
            'application/msword' => 'report',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'report',
            'application/vnd.ms-excel' => 'spreadsheet',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'spreadsheet',
            'application/vnd.ms-powerpoint' => 'presentation',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'presentation',
            'text/plain' => 'report',
            'text/csv' => 'spreadsheet',
            'image/jpeg' => 'image',
            'image/png' => 'image',
            'image/gif' => 'image',
            'image/webp' => 'image',
            'video/mp4' => 'video',
            'video/avi' => 'video',
            'audio/mpeg' => 'audio',
            'audio/wav' => 'audio'
        ];

        return $typeMap[$mimeType] ?? 'other';
    }

    /**
     * Extract metadata from uploaded file
     */
    private function extractMetadata($filePath, $mimeType)
    {
        $metadata = [
            'file_size_formatted' => formatBytes(filesize($filePath)),
            'upload_date' => date('Y-m-d H:i:s'),
            'mime_type' => $mimeType
        ];

        // Extract image metadata
        if (strpos($mimeType, 'image/') === 0) {
            $imageInfo = getimagesize($filePath);
            if ($imageInfo) {
                $metadata['width'] = $imageInfo[0];
                $metadata['height'] = $imageInfo[1];
                $metadata['dimensions'] = $imageInfo[0] . 'x' . $imageInfo[1];
            }

            // Extract EXIF data
            if (function_exists('exif_read_data') && in_array($mimeType, ['image/jpeg', 'image/tiff'])) {
                $exif = @exif_read_data($filePath);
                if ($exif) {
                    $metadata['exif'] = array_filter($exif, function($key) {
                        return in_array($key, ['DateTime', 'Make', 'Model', 'Software', 'GPS']);
                    }, ARRAY_FILTER_USE_KEY);
                }
            }
        }

        return $metadata;
    }

    /**
     * Generate thumbnail for images
     */
    private function generateThumbnail($filePath, $documentId)
    {
        try {
            $thumbnailDir = UPLOAD_PATH . '/thumbnails';
            if (!is_dir($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }

            $thumbnailPath = $thumbnailDir . '/' . $documentId . '_thumb.jpg';

            // Create thumbnail using GD library
            $imageInfo = getimagesize($filePath);
            if (!$imageInfo) return false;

            $sourceImage = null;
            switch ($imageInfo['mime']) {
                case 'image/jpeg':
                    $sourceImage = imagecreatefromjpeg($filePath);
                    break;
                case 'image/png':
                    $sourceImage = imagecreatefrompng($filePath);
                    break;
                case 'image/gif':
                    $sourceImage = imagecreatefromgif($filePath);
                    break;
                default:
                    return false;
            }

            if (!$sourceImage) return false;

            // Calculate thumbnail dimensions
            $thumbWidth = 300;
            $thumbHeight = 200;
            $sourceWidth = imagesx($sourceImage);
            $sourceHeight = imagesy($sourceImage);

            $ratio = min($thumbWidth / $sourceWidth, $thumbHeight / $sourceHeight);
            $newWidth = $sourceWidth * $ratio;
            $newHeight = $sourceHeight * $ratio;

            // Create thumbnail
            $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
            imagecopyresampled($thumbnail, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);

            // Save thumbnail
            imagejpeg($thumbnail, $thumbnailPath, 85);

            // Clean up
            imagedestroy($sourceImage);
            imagedestroy($thumbnail);

            // Update database with thumbnail path
            $this->db->execute(
                "UPDATE documents SET thumbnail_path = ? WHERE id = ?",
                ['thumbnails/' . $documentId . '_thumb.jpg', $documentId]
            );

            return true;

        } catch (\Exception $e) {
            logMessage("Thumbnail generation failed: " . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * Get filtered documents with pagination
     */
    private function getFilteredDocuments($filters)
    {
        $where = ["d.company_id = ?", "d.status != 'deleted'"];
        $params = [$this->user['company_id']];

        // Apply filters
        if (!empty($filters['search'])) {
            $where[] = "(d.title LIKE ? OR d.description LIKE ? OR d.file_name LIKE ? OR d.tags LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }

        if (!empty($filters['category'])) {
            $where[] = "d.category_id = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['type'])) {
            $where[] = "d.document_type = ?";
            $params[] = $filters['type'];
        }

        if (!empty($filters['location'])) {
            $where[] = "d.location_id = ?";
            $params[] = $filters['location'];
        }

        if (!empty($filters['storage_type'])) {
            $where[] = "d.storage_type = ?";
            $params[] = $filters['storage_type'];
        }

        if (!empty($filters['date_from'])) {
            $where[] = "DATE(d.created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where[] = "DATE(d.created_at) <= ?";
            $params[] = $filters['date_to'];
        }

        // Build ORDER BY clause
        $allowedSorts = ['title', 'created_at', 'file_size', 'download_count'];
        $sort = in_array($filters['sort'], $allowedSorts) ? $filters['sort'] : 'created_at';
        $order = $filters['order'] === 'ASC' ? 'ASC' : 'DESC';

        // Calculate offset
        $offset = ($filters['page'] - 1) * $filters['per_page'];

        $sql = "SELECT d.*, u.first_name, u.last_name, c.name as category_name,
                       sl.name as location_name, sl.identifier as location_identifier,
                       w.name as warehouse_name, b.name as bundle_name
                FROM documents d
                LEFT JOIN users u ON d.created_by = u.id
                LEFT JOIN categories c ON d.category_id = c.id
                LEFT JOIN storage_locations sl ON d.location_id = sl.id
                LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                LEFT JOIN bundles b ON d.bundle_id = b.id
                WHERE " . implode(' AND ', $where) . "
                ORDER BY d.{$sort} {$order}
                LIMIT {$filters['per_page']} OFFSET {$offset}";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get total document count for pagination
     */
    private function getDocumentCount($filters)
    {
        $where = ["d.company_id = ?", "d.status != 'deleted'"];
        $params = [$this->user['company_id']];

        // Apply same filters as getFilteredDocuments
        if (!empty($filters['search'])) {
            $where[] = "(d.title LIKE ? OR d.description LIKE ? OR d.file_name LIKE ? OR d.tags LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }

        if (!empty($filters['category'])) {
            $where[] = "d.category_id = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['type'])) {
            $where[] = "d.document_type = ?";
            $params[] = $filters['type'];
        }

        if (!empty($filters['location'])) {
            $where[] = "d.location_id = ?";
            $params[] = $filters['location'];
        }

        if (!empty($filters['storage_type'])) {
            $where[] = "d.storage_type = ?";
            $params[] = $filters['storage_type'];
        }

        if (!empty($filters['date_from'])) {
            $where[] = "DATE(d.created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where[] = "DATE(d.created_at) <= ?";
            $params[] = $filters['date_to'];
        }

        $sql = "SELECT COUNT(*) as count FROM documents d WHERE " . implode(' AND ', $where);
        $result = $this->db->fetch($sql, $params);
        return $result['count'] ?? 0;
    }

    /**
     * Process individual file upload
     */
    private function processFileUpload($index)
    {
        $fileName = $_FILES['documents']['name'][$index];
        $tmpName = $_FILES['documents']['tmp_name'][$index];
        $fileSize = $_FILES['documents']['size'][$index];
        $error = $_FILES['documents']['error'][$index];

        // Validate file
        if ($error !== UPLOAD_ERR_OK) {
            throw new \Exception("Upload error code: {$error}");
        }

        if ($fileSize > MAX_FILE_SIZE) {
            throw new \Exception("File size exceeds maximum allowed size");
        }

        // Get file info
        $fileInfo = pathinfo($fileName);
        $extension = strtolower($fileInfo['extension'] ?? '');
        $mimeType = mime_content_type($tmpName);

        // Validate file type
        if (!$this->isAllowedFileType($extension, $mimeType)) {
            throw new \Exception("File type not allowed");
        }

        // Generate unique filename
        $uniqueFileName = uniqid() . '_' . time() . '.' . $extension;
        $uploadPath = UPLOAD_PATH . '/' . date('Y/m');

        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        $filePath = $uploadPath . '/' . $uniqueFileName;
        $relativePath = date('Y/m') . '/' . $uniqueFileName;

        // Move uploaded file
        if (!move_uploaded_file($tmpName, $filePath)) {
            throw new \Exception("Failed to move uploaded file");
        }

        // Get form data
        $title = $_POST['title'] ?? $fileInfo['filename'];
        $description = $_POST['description'] ?? null;
        $categoryId = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;
        $locationId = !empty($_POST['location_id']) ? (int)$_POST['location_id'] : null;
        $bundleId = !empty($_POST['bundle_id']) ? (int)$_POST['bundle_id'] : null;
        $tags = $_POST['tags'] ?? null;
        $storageType = $_POST['storage_type'] ?? 'physical';
        $onlineStoragePath = $_POST['online_storage_path'] ?? null;

        // Detect document type
        $documentType = $this->detectDocumentType($mimeType);

        // Insert document record with bundle_id and storage type
        $documentId = $this->db->execute(
            "INSERT INTO documents (
                company_id, title, description, file_name, file_path, file_size,
                mime_type, document_type, category_id, location_id, bundle_id, tags,
                storage_type, online_storage_path, status, created_by, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'approved', ?, NOW(), NOW())",
            [
                $this->user['company_id'],
                $title,
                $description,
                $fileName,
                $relativePath,
                $fileSize,
                $mimeType,
                $documentType,
                $categoryId,
                $storageType === 'physical' ? $locationId : null, // Only set location_id for physical storage
                $bundleId,
                $tags,
                $storageType,
                $storageType === 'online' ? $onlineStoragePath : null, // Only set online path for online storage
                $this->user['id']
            ]
        );

        // Generate thumbnail for images
        if (strpos($mimeType, 'image/') === 0) {
            $this->generateThumbnail($filePath, $documentId);
        }

        // Log activity
        $this->logActivity('create', 'document', $documentId, "Uploaded document: {$title}");

        return [
            'id' => $documentId,
            'title' => $title,
            'file_name' => $fileName,
            'file_size' => $fileSize,
            'bundle_id' => $bundleId
        ];
    }

    /**
     * Check if file type is allowed
     */
    private function isAllowedFileType($extension, $mimeType)
    {
        $allowedExtensions = [
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
            'txt', 'csv', 'jpg', 'jpeg', 'png', 'gif', 'webp',
            'mp4', 'avi', 'mov', 'mp3', 'wav', 'zip', 'rar'
        ];

        $allowedMimeTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'video/mp4',
            'video/quicktime',
            'audio/mpeg',
            'audio/wav',
            'application/zip',
            'application/x-rar-compressed'
        ];

        return in_array($extension, $allowedExtensions) && in_array($mimeType, $allowedMimeTypes);
    }





    /**
     * Update document location
     */
    private function updateDocumentLocation($documentId, $locationId)
    {
        // Update document location directly
        $this->db->execute(
            "UPDATE documents SET location_id = ?, updated_at = NOW() WHERE id = ? AND company_id = ?",
            [$locationId, $documentId, $this->user['company_id']]
        );
    }
}