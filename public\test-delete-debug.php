<?php
/**
 * Debug Delete Functionality
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';
$dbConfig = require_once CONFIG_PATH . '/database.php';

// Initialize database
$db = new App\Core\Database($dbConfig);

echo "<h1>Delete Functionality Debug</h1>";

// Test URL generation
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

echo "<h2>URL Generation Test</h2>";
echo "<p><strong>Base Path:</strong> " . (BASE_PATH ?: '(empty)') . "</p>";
echo "<p><strong>Delete URL for rate ID 1:</strong> " . url('/app/billing/rates/delete/1') . "</p>";

// Test database connection and get some rates
echo "<h2>Available Service Rates</h2>";
try {
    $rates = $db->fetchAll("SELECT * FROM billing_rates ORDER BY id LIMIT 5");
    
    if (empty($rates)) {
        echo "<p>No service rates found in database.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Service Name</th><th>Rate</th><th>Active</th><th>Test Delete</th></tr>";
        
        foreach ($rates as $rate) {
            echo "<tr>";
            echo "<td>" . $rate['id'] . "</td>";
            echo "<td>" . htmlspecialchars($rate['service_name']) . "</td>";
            echo "<td>$" . number_format($rate['rate'], 2) . "</td>";
            echo "<td>" . ($rate['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "<td>";
            
            // Test delete button
            echo "<button onclick=\"testDelete(" . $rate['id'] . ", '" . htmlspecialchars($rate['service_name']) . "')\" style='background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>Test Delete</button>";
            
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error fetching rates: " . $e->getMessage() . "</p>";
}

// Check if this is a delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_delete'])) {
    $rateId = $_POST['rate_id'] ?? null;
    
    echo "<h2>Delete Test Result</h2>";
    echo "<p><strong>Rate ID:</strong> " . htmlspecialchars($rateId) . "</p>";
    echo "<p><strong>Request Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</p>";
    echo "<p><strong>POST Data:</strong></p>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    if ($rateId) {
        try {
            // Test if rate exists
            $rate = $db->fetch("SELECT * FROM billing_rates WHERE id = ?", [$rateId]);
            if ($rate) {
                echo "<p>✅ Rate found: " . htmlspecialchars($rate['service_name']) . "</p>";
                
                // Test if rate is in use
                $usage = $db->fetch("SELECT COUNT(*) as count FROM billing_events WHERE service_code = ?", [$rate['service_code']]);
                $isUsed = (int)$usage['count'] > 0;
                
                echo "<p><strong>Usage check:</strong> " . ($isUsed ? "Rate is in use ({$usage['count']} events)" : "Rate is not in use") . "</p>";
                
                if (!$isUsed) {
                    echo "<p>✅ Rate can be safely deleted</p>";
                } else {
                    echo "<p>⚠️ Rate cannot be deleted - it's being used</p>";
                }
            } else {
                echo "<p>❌ Rate not found</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    }
}

?>

<script>
function testDelete(rateId, serviceName) {
    const message = `Test delete for service rate "${serviceName}" (ID: ${rateId})\n\nThis will test the delete functionality without actually deleting.`;
    
    if (confirm(message)) {
        // Create a form and submit it for POST request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = window.location.href;
        
        // Add rate ID
        const rateInput = document.createElement('input');
        rateInput.type = 'hidden';
        rateInput.name = 'rate_id';
        rateInput.value = rateId;
        form.appendChild(rateInput);
        
        // Add test flag
        const testInput = document.createElement('input');
        testInput.type = 'hidden';
        testInput.name = 'test_delete';
        testInput.value = '1';
        form.appendChild(testInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Test the actual delete URL generation
function testActualDelete(rateId, serviceName) {
    const deleteUrl = '<?= url('/app/billing/rates/delete/') ?>' + rateId;
    console.log('Generated delete URL:', deleteUrl);
    
    const message = `ACTUAL DELETE for service rate "${serviceName}"\n\nThis will attempt to delete the rate. Continue?`;
    
    if (confirm(message)) {
        // Create a form and submit it for POST request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<h2>Manual Test</h2>
<p>You can also test the delete URL manually:</p>
<ul>
    <li><strong>Correct URL format:</strong> <code><?= url('/app/billing/rates/delete/1') ?></code></li>
    <li><strong>Method:</strong> POST</li>
</ul>

<h2>Browser Console</h2>
<p>Open your browser's developer tools (F12) and check the Console tab for any JavaScript errors when clicking the delete button.</p>

<hr>
<p><a href="<?= url('/app/billing/rates') ?>">← Back to Service Rates</a></p>
