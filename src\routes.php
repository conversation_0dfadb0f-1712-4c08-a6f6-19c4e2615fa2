<?php
/**
 * Application Routes
 * 
 * Define all routes for the Document Management System
 */

// Web Routes
$router->get('/', 'HomeController@index');
$router->get('/dashboard', 'DashboardController@index');

// Authentication Routes
$router->get('/login', 'Auth<PERSON><PERSON>roller@showLogin');
$router->post('/login', 'Auth<PERSON><PERSON>roller@login');
$router->get('/register', 'AuthController@showRegister');
$router->post('/register', 'AuthController@register');
$router->post('/logout', 'AuthController@logout');
$router->get('/forgot-password', 'AuthController@showForgotPassword');
$router->post('/forgot-password', 'AuthController@forgotPassword');
$router->get('/reset-password/{token}', 'AuthController@showResetPassword');
$router->post('/reset-password', 'AuthController@resetPassword');

// Company Registration Routes
$router->get('/company/register', 'CompanyController@showRegister');
$router->post('/company/register', 'CompanyController@register');

// Client Portal Routes (Step 4: Online Integration/Access)
$router->group('/client', ['App\Middleware\AuthMiddleware'], function($router, $prefix) {
    $router->get('/dashboard', 'ClientPortalController@dashboard');
    $router->get('/documents', 'ClientPortalController@documents');
    $router->get('/documents/{id}', 'ClientPortalController@viewDocument');
    $router->get('/requests', 'ClientPortalController@requests');
    $router->get('/create-request', 'ClientPortalController@createRequest');
    $router->post('/requests', 'ClientPortalController@storeRequest');
    $router->get('/profile', 'ClientPortalController@profile');
    $router->post('/profile', 'ClientPortalController@updateProfile');
    $router->get('/help', 'ClientPortalController@help');
    $router->get('/contact', 'ClientPortalController@contact');
    $router->post('/contact', 'ClientPortalController@submitContact');
});

// Super Admin Routes (Super Admin only)
$router->group('/super-admin', ['App\Middleware\AuthMiddleware'], function($router, $prefix) {
    $router->get('/dashboard', 'SuperAdminController@dashboard');
    $router->get('/analytics', 'SuperAdminController@analytics');
    $router->get('/settings', 'SuperAdminController@settings');
    $router->post('/settings', 'SuperAdminController@updateSettings');

    // Advanced Warehouse Management
    $router->get('/warehouse-management', 'WarehouseManagementController@index');
    $router->get('/warehouse-management/layout', 'WarehouseManagementController@layout');
    $router->get('/warehouse-management/layout/{id}', 'WarehouseManagementController@layout');
    $router->get('/warehouse-management/capacity', 'WarehouseManagementController@capacity');
    $router->get('/warehouse-management/movements', 'WarehouseManagementController@movements');
    $router->get('/warehouse-management/efficiency', 'WarehouseManagementController@efficiency');

    // Advanced Barcode Management
    $router->get('/barcode-management', 'BarcodeManagementController@index');
    $router->get('/barcode-management/bulk-generate', 'BarcodeManagementController@bulkGenerate');
    $router->post('/barcode-management/bulk-generate', 'BarcodeManagementController@bulkGenerate');
    $router->get('/barcode-management/mobile-scanner', 'BarcodeManagementController@mobileScanner');
    $router->get('/barcode-management/analytics', 'BarcodeManagementController@analytics');
    $router->get('/barcode-management/audit-trail', 'BarcodeManagementController@auditTrail');

    // Compliance & Retention Management
    $router->get('/compliance-management', 'ComplianceController@index');
    $router->get('/compliance-management/retention-policies', 'ComplianceController@retentionPolicies');
    $router->post('/compliance-management/retention-policies', 'ComplianceController@retentionPolicies');
    $router->get('/compliance-management/destruction-schedule', 'ComplianceController@destructionSchedule');
    $router->get('/compliance-management/legal-holds', 'ComplianceController@legalHolds');
    $router->post('/compliance-management/legal-holds', 'ComplianceController@legalHolds');
    $router->get('/compliance-management/reports', 'ComplianceController@reports');

    // Real-time System Monitoring
    $router->get('/system-monitoring', 'SystemMonitoringController@index');
    $router->get('/system-monitoring/performance', 'SystemMonitoringController@performance');
    $router->get('/system-monitoring/security', 'SystemMonitoringController@security');
    $router->get('/system-monitoring/alerts', 'SystemMonitoringController@alerts');
    $router->post('/system-monitoring/alerts', 'SystemMonitoringController@alerts');
    $router->get('/system-monitoring/health-api', 'SystemMonitoringController@healthApi');

    // Advanced Business Intelligence
    $router->get('/business-intelligence', 'BusinessIntelligenceController@index');
    $router->get('/business-intelligence/revenue', 'BusinessIntelligenceController@revenue');
    $router->get('/business-intelligence/clients', 'BusinessIntelligenceController@clients');
    $router->get('/business-intelligence/operations', 'BusinessIntelligenceController@operations');
    $router->get('/business-intelligence/predictive', 'BusinessIntelligenceController@predictive');
});

// Protected Routes (require authentication)
$router->group('/app', ['App\Middleware\AuthMiddleware'], function($router, $prefix) {
    
    // Dashboard
    $router->get('/', 'DashboardController@index');
    $router->get('/dashboard', 'DashboardController@index');
    
    // Document Management
    $router->get('/documents', 'DocumentController@index');
    $router->get('/documents/create', 'DocumentController@create');
    $router->post('/documents', 'DocumentController@store');
    $router->get('/documents/{id}', 'DocumentController@show');
    $router->get('/documents/{id}/edit', 'DocumentController@edit');
    $router->put('/documents/{id}', 'DocumentController@update');
    $router->delete('/documents/{id}', 'DocumentController@delete');
    $router->get('/documents/{id}/download', 'DocumentController@download');
    $router->get('/documents/{id}/preview', 'DocumentController@preview');
    $router->post('/documents/{id}/versions', 'DocumentController@createVersion');
    $router->get('/documents/{id}/versions', 'DocumentController@versions');
    
    // Document Categories
    $router->get('/categories', 'CategoryController@index');
    $router->post('/categories', 'CategoryController@store');
    $router->put('/categories/{id}', 'CategoryController@update');
    $router->delete('/categories/{id}', 'CategoryController@delete');
    $router->get('/categories/api', 'CategoryController@getCategories');
    
    // Warehouse Management
    $router->get('/warehouses', 'WarehouseController@index');
    $router->get('/warehouses/create', 'WarehouseController@create');
    $router->post('/warehouse-ajax-create', 'WarehouseController@storeAjax'); // AJAX endpoint for modal creation
    $router->post('/warehouses', 'WarehouseController@store');
    $router->get('/warehouses/{id}', 'WarehouseController@show');
    $router->get('/warehouses/{id}/debug', 'WarehouseController@debug');
    $router->get('/warehouses/{id}/edit', 'WarehouseController@edit');
    $router->put('/warehouses/{id}', 'WarehouseController@update');
    $router->delete('/warehouses/{id}', 'WarehouseController@delete');

    // Bundle Management
    $router->get('/bundles', 'BundleController@index');
    $router->get('/bundles/create', 'BundleController@create');
    $router->post('/bundles', 'BundleController@store');
    $router->get('/bundles/{id}', 'BundleController@show');
    $router->get('/bundles/{id}/edit', 'BundleController@edit');
    $router->put('/bundles/{id}', 'BundleController@update');
    $router->delete('/bundles/{id}', 'BundleController@delete');
    $router->get('/bundles/{id}/archive', 'BundleController@archive');
    $router->get('/bundles/{id}/unarchive', 'BundleController@unarchive');
    $router->post('/bundles/{id}/documents', 'BundleController@addDocument');
    $router->delete('/bundles/{bundleId}/documents/{documentId}', 'BundleController@removeDocument');

    // Box Management (Updated for documentation compliance)
    $router->get('/boxes', 'BoxController@index');
    $router->get('/boxes/available', 'BoxController@getAvailable');
    $router->get('/boxes/create', 'BoxController@create');
    $router->post('/boxes', 'BoxController@store');
    $router->get('/boxes/{id}', 'BoxController@show');
    $router->get('/boxes/{id}/edit', 'BoxController@edit');
    $router->put('/boxes/{id}', 'BoxController@update');
    $router->delete('/boxes/{id}', 'BoxController@delete');
    $router->get('/boxes/{id}/archive', 'BoxController@archive');
    $router->get('/boxes/{id}/unarchive', 'BoxController@unarchive');
    $router->post('/boxes/{id}/generate-barcode', 'BoxController@generateBarcode');

    // Storage Locations
    $router->get('/warehouses/{id}/locations', 'LocationController@index');
    $router->get('/warehouses/{id}/locations/create', 'LocationController@create');
    $router->post('/warehouses/{id}/locations', 'LocationController@store');
    $router->get('/locations/{id}', 'LocationController@show');
    $router->get('/locations/{id}/edit', 'LocationController@edit');
    $router->put('/locations/{id}', 'LocationController@update');
    $router->delete('/locations/{id}', 'LocationController@delete');
    $router->get('/locations/{id}/documents', 'LocationController@documents');

    // Alternative storage-locations routes for convenience
    $router->get('/storage-locations/create', 'LocationController@createStandalone');
    $router->get('/storage-locations/{id}', 'LocationController@showStandalone');
    $router->get('/storage-locations/{id}/edit', 'LocationController@editStandalone');
    $router->put('/storage-locations/{id}', 'LocationController@updateStandalone');
    $router->delete('/storage-locations/{id}', 'LocationController@deleteStandalone');
    $router->get('/storage-locations/{id}/documents', 'LocationController@documentsStandalone');
    
    // Search
    $router->get('/search', 'SearchController@index');
    $router->post('/search', 'SearchController@search');
    $router->get('/search/suggestions', 'SearchController@suggestions');

    // Settings
    $router->get('/settings', 'SettingsController@index');
    $router->post('/settings/profile', 'SettingsController@updateProfile');
    $router->post('/settings/password', 'SettingsController@updatePassword');
    $router->post('/settings/notifications', 'SettingsController@updateNotifications');

    // Document Requests Management
    $router->get('/requests', 'RequestController@index');
    $router->get('/requests/{id}', 'RequestController@show');
    $router->post('/requests/{id}/approve', 'RequestController@approve');
    $router->post('/requests/{id}/reject', 'RequestController@reject');
    $router->post('/requests/{id}/complete', 'RequestController@complete');
    $router->post('/requests/{id}/assign', 'RequestController@assign');

    // Automated Alerts Management
    $router->get('/alerts', 'AlertController@index');
    $router->get('/alerts/{id}', 'AlertController@show');
    $router->post('/alerts/{id}/acknowledge', 'AlertController@acknowledge');
    $router->post('/alerts/{id}/resolve', 'AlertController@resolve');

    // Company Management (Super Admin & Company Admin)
    $router->get('/companies', 'CompanyController@index');
    $router->get('/companies/create', 'CompanyController@create');
    $router->post('/companies', 'CompanyController@store');
    $router->get('/companies/{id}', 'CompanyController@show');
    $router->get('/companies/{id}/edit', 'CompanyController@edit');
    $router->put('/companies/{id}', 'CompanyController@update');
    $router->delete('/companies/{id}', 'CompanyController@delete');

    // User Management (Super Admin & Company Admin)
    $router->get('/users', 'UserController@index');
    $router->get('/users/create', 'UserController@create');
    $router->post('/users', 'UserController@store');
    $router->get('/users/{id}', 'UserController@show');
    $router->get('/users/{id}/edit', 'UserController@edit');
    $router->put('/users/{id}', 'UserController@update');
    $router->delete('/users/{id}', 'UserController@delete');

    // Document Intake routes
    $router->get('/intake', 'IntakeController@index');
    $router->get('/intake/create', 'IntakeController@create');
    $router->post('/intake', 'IntakeController@store');
    $router->get('/intake/{id}', 'IntakeController@show');
    $router->post('/intake/{id}/process', 'IntakeController@process');
    $router->patch('/intake/{id}/process', 'IntakeController@updateStatus');
    $router->post('/intake/{id}/complete', 'IntakeController@complete');
    $router->delete('/intake/{id}', 'IntakeController@delete');
    $router->post('/intake/document-types', 'IntakeController@createDocumentType');
    $router->get('/intake/document-types', 'IntakeController@getDocumentTypesApi');
    $router->post('/intake/sorting-categories', 'IntakeController@createSortingCategory');
    
    // User Management (Admin only)
    $router->get('/users', 'UserController@index');
    $router->get('/users/create', 'UserController@create');
    $router->post('/users', 'UserController@store');
    $router->get('/users/{id}', 'UserController@show');
    $router->get('/users/{id}/edit', 'UserController@edit');
    $router->put('/users/{id}', 'UserController@update');
    $router->delete('/users/{id}', 'UserController@delete');
    
    // Company Settings (Admin only)
    $router->get('/company', 'CompanyController@show');
    $router->get('/company/edit', 'CompanyController@edit');
    $router->put('/company', 'CompanyController@update');
    $router->get('/company/settings', 'CompanyController@settings');
    $router->put('/company/settings', 'CompanyController@updateSettings');
    
    // Profile Management
    $router->get('/profile', 'ProfileController@show');
    $router->get('/profile/edit', 'ProfileController@edit');
    $router->put('/profile', 'ProfileController@update');
    $router->post('/profile/avatar', 'ProfileController@uploadAvatar');
    $router->put('/profile/password', 'ProfileController@changePassword');
    
    // Reports & Analytics
    $router->get('/reports', 'ReportController@index');
    $router->get('/reports/usage', 'ReportController@usage');
    $router->get('/reports/storage', 'ReportController@storage');
    $router->get('/reports/audit', 'ReportController@audit');
    $router->post('/reports/export', 'ReportController@export');

    // Billing Management
    $router->get('/billing', 'BillingController@index');
    $router->get('/billing/client/{id}', 'BillingController@clientBilling');
    $router->post('/billing/generate-invoice', 'BillingController@generateInvoice');
    $router->get('/billing/invoice/{id}', 'BillingController@viewInvoice');
    $router->post('/billing/invoice/{id}/mark-paid', 'BillingController@markPaid');
    $router->get('/billing/rates', 'BillingController@serviceRates');
    $router->post('/billing/rates/save', 'BillingController@saveServiceRate');
    $router->get('/billing/rates/toggle/{id}', 'BillingController@toggleServiceRate');
    $router->post('/billing/rates/delete/{id}', 'BillingController@deleteServiceRate');
    $router->post('/billing/log-event', 'BillingController@logEvent');

    // Business Services API Routes
    $router->group('/services', [], function($router, $prefix) {

        // Box Handling Services
        $router->post('/boxes/create', 'BusinessServiceController@createBox');
        $router->post('/boxes/{id}/move', 'BusinessServiceController@moveBox');
        $router->post('/boxes/add-bundle', 'BusinessServiceController@addBundleToBox');

        // Bundle Handling Services
        $router->post('/bundles/create', 'BusinessServiceController@createBundle');
        $router->post('/bundles/{id}/close', 'BusinessServiceController@closeBundle');

        // Delivery Services
        $router->post('/delivery/create', 'BusinessServiceController@createDeliveryRequest');
        $router->get('/delivery/track/{reference}', 'BusinessServiceController@trackDelivery');

        // Search Services
        $router->get('/search', 'BusinessServiceController@search');
        $router->get('/search/suggestions', 'BusinessServiceController@searchSuggestions');

        // Intake Services
        $router->post('/intake/create', 'BusinessServiceController@createIntakeRequest');

        // Service Status
        $router->get('/status', 'BusinessServiceController@getServiceStatus');
    });

    // Test pages
    $router->get('/test/business-services', 'TestController@businessServices');
    $router->get('/test/route-debug', 'TestController@routeDebug');
});

// API Routes
$router->group('/api/v1', [], function($router, $prefix) {
    
    // Authentication API
    $router->post('/auth/login', 'Api\AuthController@login');
    $router->post('/auth/logout', 'Api\AuthController@logout');
    $router->post('/auth/refresh', 'Api\AuthController@refresh');
    
    // Protected API Routes
    $router->group('', ['App\Middleware\ApiAuthMiddleware'], function($router, $prefix) {
        
        // Documents API
        $router->get('/documents', 'Api\DocumentController@index');
        $router->post('/documents', 'Api\DocumentController@store');
        $router->get('/documents/{id}', 'Api\DocumentController@show');
        $router->put('/documents/{id}', 'Api\DocumentController@update');
        $router->delete('/documents/{id}', 'Api\DocumentController@delete');
        $router->post('/documents/upload', 'Api\DocumentController@upload');
        $router->get('/documents/{id}/download', 'Api\DocumentController@download');
        
        // Warehouses API
        $router->get('/warehouses', 'Api\WarehouseController@index');
        $router->post('/warehouses', 'Api\WarehouseController@store');
        $router->get('/warehouses/{id}', 'Api\WarehouseController@show');
        $router->put('/warehouses/{id}', 'Api\WarehouseController@update');
        $router->delete('/warehouses/{id}', 'Api\WarehouseController@delete');
        
        // Locations API
        $router->get('/warehouses/{id}/locations', 'Api\LocationController@index');
        $router->post('/warehouses/{id}/locations', 'Api\LocationController@store');
        $router->get('/locations/{id}', 'Api\LocationController@show');
        $router->put('/locations/{id}', 'Api\LocationController@update');
        $router->delete('/locations/{id}', 'Api\LocationController@delete');
        
        // Barcode API
        $router->post('/barcodes/generate', 'Api\BarcodeController@generate');
        $router->post('/barcodes/scan', 'Api\BarcodeController@scan');
        $router->get('/barcodes/{value}', 'Api\BarcodeController@lookup');
        $router->post('/barcodes/batch-generate', 'Api\BarcodeController@batchGenerate');
        $router->get('/barcodes/validate/{value}', 'Api\BarcodeController@validate');
        
        // Search API
        $router->get('/search', 'Api\SearchController@search');
        $router->get('/search/suggestions', 'Api\SearchController@suggestions');
        
        // Analytics API
        $router->get('/analytics/dashboard', 'Api\AnalyticsController@dashboard');
        $router->get('/analytics/usage', 'Api\AnalyticsController@usage');
        $router->get('/analytics/storage', 'Api\AnalyticsController@storage');
        
        // User API
        $router->get('/users', 'Api\UserController@index');
        $router->get('/users/{id}', 'Api\UserController@show');
        $router->get('/profile', 'Api\UserController@profile');
        $router->put('/profile', 'Api\UserController@updateProfile');
    });
});

// File Upload Routes (with special handling)
$router->post('/upload', 'UploadController@handle');
$router->post('/upload/chunk', 'UploadController@chunk');
$router->post('/upload/complete', 'UploadController@complete');

// Barcode Scanner Routes
$router->get('/scanner', 'ScannerController@index');
$router->post('/scanner/scan', 'ScannerController@scan');

// Public file serving (for previews, thumbnails, etc.)
$router->get('/files/{type}/{file}', 'FileController@serve');
