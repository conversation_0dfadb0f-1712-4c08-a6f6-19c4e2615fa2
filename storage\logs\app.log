[2025-06-07 07:53:41] [error] Database connection failed: SQLSTATE[HY000] [1049] Unknown database 'dms_system'
[2025-06-07 07:54:12] [info] Database connected successfully
[2025-06-07 07:55:15] [info] Database connected successfully
[2025-06-07 07:55:15] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table '`dms_system`.`companies`' already exists SQL: -- Create companies table
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    storage_used BIGINT DEFAULT 0,
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_status (status),
    INDEX idx_companies_created (created_at)
)
[2025-06-07 07:55:15] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table '`dms_system`.`users`' already exists SQL: -- Create users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_users_company (company_id),
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
)
[2025-06-07 07:55:15] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    IND...' at line 20 SQL: -- Create warehouses table
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    INDEX idx_warehouses_company (company_id),
    INDEX idx_warehouses_manager (manager_id),
    INDEX idx_warehouses_status (status),
    INDEX idx_warehouses_created (created_at)
)
[2025-06-07 07:55:22] [error] SQL execution failed: SQLSTATE[HY000]: General error: 2006 MySQL server has gone away SQL: -- Create storage_locations table
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_location (warehouse_id, identifier),
    INDEX idx_locations_warehouse (warehouse_id),
    INDEX idx_locations_parent (parent_id),
    INDEX idx_locations_type (type),
    INDEX idx_locations_barcode (barcode_value),
    INDEX idx_locations_status (status),
    INDEX idx_locations_hierarchy (warehouse_id, parent_id, type)
)
[2025-06-07 07:55:36] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-07 07:57:06] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-07 07:59:09] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-07 07:59:52] [info] Database connected successfully
[2025-06-07 08:01:59] [info] Database connected successfully
[2025-06-07 08:07:00] [info] Database connected successfully
[2025-06-07 08:07:04] [info] Database connected successfully
[2025-06-07 08:10:25] [info] Database connected successfully
[2025-06-07 08:10:33] [info] Database connected successfully
[2025-06-07 08:10:40] [info] Database connected successfully
[2025-06-07 08:11:03] [info] Database connected successfully
[2025-06-07 08:11:03] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.companies' doesn't exist SQL: SELECT id, name FROM companies WHERE status = 'active' ORDER BY name
[2025-06-07 08:15:10] [info] Database connected successfully
[2025-06-07 08:15:57] [info] Database connected successfully
[2025-06-07 08:18:01] [info] Database connected successfully
[2025-06-07 08:18:01] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.users' doesn't exist SQL: SELECT * FROM users WHERE email = ? AND status = 'active'
[2025-06-07 08:18:21] [info] Database connected successfully
[2025-06-07 08:19:40] [info] Database connected successfully
[2025-06-07 08:19:40] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.users' doesn't exist SQL: SELECT * FROM users WHERE email = ? AND status = 'active'
[2025-06-07 08:22:28] [info] Database connected successfully
[2025-06-07 08:23:56] [info] Database connected successfully
[2025-06-07 08:25:50] [info] Database connected successfully
[2025-06-07 08:25:59] [info] Database connected successfully
[2025-06-07 08:25:59] [error] SQL execution failed: SQLSTATE[HY000]: General error: 1813 Tablespace for table '`dms_system`.`companies`' exists. Please DISCARD the tablespace before IMPORT SQL: -- Create companies table
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    storage_used BIGINT DEFAULT 0,
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_status (status),
    INDEX idx_companies_created (created_at)
)
[2025-06-07 08:25:59] [error] SQL execution failed: SQLSTATE[HY000]: General error: 1813 Tablespace for table '`dms_system`.`users`' exists. Please DISCARD the tablespace before IMPORT SQL: -- Create users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_users_company (company_id),
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
)
[2025-06-07 08:25:59] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    IND...' at line 20 SQL: -- Create warehouses table
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    INDEX idx_warehouses_company (company_id),
    INDEX idx_warehouses_manager (manager_id),
    INDEX idx_warehouses_status (status),
    INDEX idx_warehouses_created (created_at)
)
[2025-06-07 08:25:59] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'storage_locations' already exists SQL: -- Create storage_locations table
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_location (warehouse_id, identifier),
    INDEX idx_locations_warehouse (warehouse_id),
    INDEX idx_locations_parent (parent_id),
    INDEX idx_locations_type (type),
    INDEX idx_locations_barcode (barcode_value),
    INDEX idx_locations_status (status),
    INDEX idx_locations_hierarchy (warehouse_id, parent_id, type)
)
[2025-06-07 08:25:59] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table '`dms_system`.`document_categories`' already exists SQL: -- Create document_categories table
CREATE TABLE document_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES document_categories(id) ON DELETE CASCADE,
    
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_sort (sort_order),
    INDEX idx_categories_system (is_system)
)
[2025-06-07 08:25:59] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY ...' at line 39 SQL: -- Create documents table
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL, -- For version control
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash
    version VARCHAR(20) DEFAULT '1.0',
    is_latest_version BOOLEAN DEFAULT TRUE,
    document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
    category_id INT,
    tags JSON,
    metadata JSON,
    ocr_text LONGTEXT,
    thumbnail_path VARCHAR(500),
    preview_path VARCHAR(500),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    access_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    retention_date DATE,
    expiry_date DATE,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES document_categories(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    
    INDEX idx_documents_company (company_id),
    INDEX idx_documents_parent (parent_id),
    INDEX idx_documents_category (category_id),
    INDEX idx_documents_creator (created_by),
    INDEX idx_documents_type (document_type),
    INDEX idx_documents_status (status),
    INDEX idx_documents_version (is_latest_version),
    INDEX idx_documents_created (created_at),
    INDEX idx_documents_hash (file_hash),
    INDEX idx_documents_company_search (company_id, status, created_at),
    
    FULLTEXT KEY ft_documents_content (title, description, ocr_text)
)
[2025-06-07 08:25:59] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx...' at line 16 SQL: -- Create document_locations table
CREATE TABLE document_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    document_id INT NOT NULL,
    location_id INT,
    storage_type ENUM('physical', 'digital', 'hybrid') NOT NULL,
    physical_reference VARCHAR(255), -- Box number, shelf reference, etc.
    digital_path VARCHAR(1000),
    quantity INT DEFAULT 1,
    condition_notes TEXT,
    moved_by INT NOT NULL,
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx_doc_locations_document (document_id),
    INDEX idx_doc_locations_location (location_id),
    INDEX idx_doc_locations_current (is_current),
    INDEX idx_doc_locations_moved (moved_at),
    INDEX idx_doc_locations_type (storage_type)
)
[2025-06-07 08:26:08] [info] Database connected successfully
[2025-06-07 08:26:41] [info] Database connected successfully
[2025-06-07 08:28:27] [info] Database connected successfully
[2025-06-07 08:29:30] [info] Database connected successfully
[2025-06-07 08:29:30] [error] SQL execution failed: SQLSTATE[HY000]: General error: 1813 Tablespace for table '`dms_system`.`companies`' exists. Please DISCARD the tablespace before IMPORT SQL: 
        CREATE TABLE IF NOT EXISTS companies (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            domain VARCHAR(255) UNIQUE,
            logo_path VARCHAR(500),
            address TEXT,
            phone VARCHAR(20),
            email VARCHAR(255),
            settings JSON,
            subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
            storage_limit BIGINT DEFAULT 5368709120,
            storage_used BIGINT DEFAULT 0,
            status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    
[2025-06-07 08:30:59] [info] Database connected successfully
[2025-06-07 08:31:55] [info] Database connected successfully
[2025-06-07 08:31:55] [error] Query failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 SQL: SHOW TABLES LIKE :table
[2025-06-07 08:32:12] [info] Database connected successfully
[2025-06-07 08:32:12] [error] Query failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 SQL: SHOW TABLES LIKE :table
[2025-06-07 08:32:17] [info] Database connected successfully
[2025-06-07 08:32:17] [error] Query failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 SQL: SHOW TABLES LIKE :table
[2025-06-07 08:32:37] [info] Database connected successfully
[2025-06-07 08:33:00] [info] Database connected successfully
[2025-06-07 08:34:26] [info] Database connected successfully
[2025-06-07 08:34:27] [error] Query failed: SQLSTATE[HY093]: Invalid parameter number: mixed named and positional parameters SQL: UPDATE users SET last_login = :last_login, login_attempts = :login_attempts, locked_until = :locked_until WHERE id = ?
[2025-06-07 08:36:00] [info] Database connected successfully
[2025-06-07 08:36:28] [info] Database connected successfully
[2025-06-07 08:36:39] [info] Database connected successfully
[2025-06-07 08:36:40] [info] Database connected successfully
[2025-06-07 08:37:17] [info] Database connected successfully
[2025-06-07 08:37:17] [info] Database connected successfully
[2025-06-07 08:37:49] [info] Database connected successfully
[2025-06-07 08:37:49] [error] Query failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 SQL: SHOW TABLES LIKE ?
[2025-06-07 08:38:16] [info] Database connected successfully
[2025-06-07 08:38:31] [info] Database connected successfully
[2025-06-07 08:38:32] [info] Database connected successfully
[2025-06-07 08:40:28] [info] Database connected successfully
[2025-06-07 08:41:21] [info] Database connected successfully
[2025-06-07 08:41:21] [info] Database connected successfully
[2025-06-07 08:41:43] [info] Database connected successfully
[2025-06-07 08:41:43] [info] Database connected successfully
[2025-06-07 08:41:47] [info] Database connected successfully
[2025-06-07 08:41:53] [info] Database connected successfully
[2025-06-07 08:41:53] [info] Database connected successfully
[2025-06-07 08:42:27] [info] Database connected successfully
[2025-06-07 08:42:55] [info] Database connected successfully
[2025-06-07 08:42:56] [info] Database connected successfully
[2025-06-07 08:52:30] [info] Database connected successfully
[2025-06-07 08:58:48] [info] Database connected successfully
[2025-06-07 09:02:07] [info] Database connected successfully
[2025-06-07 09:07:15] [info] Database connected successfully
[2025-06-07 09:13:06] [info] Database connected successfully
[2025-06-07 09:13:07] [info] Database connected successfully
[2025-06-07 09:21:49] [info] Database connected successfully
[2025-06-07 09:22:46] [info] Database connected successfully
[2025-06-07 09:24:39] [info] Database connected successfully
[2025-06-07 09:25:14] [info] Database connected successfully
[2025-06-07 09:35:46] [info] Database connected successfully
[2025-06-07 09:49:41] [info] Database connected successfully
[2025-06-07 10:00:59] [info] Database connected successfully
[2025-06-07 10:05:39] [info] Database connected successfully
[2025-06-07 10:05:39] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.city' in 'field list' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:05:52] [info] Database connected successfully
[2025-06-07 10:05:52] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'on clause' SQL: SELECT w.*, 
                        COUNT(DISTINCT sl.id) as total_locations,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' THEN sl.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' AND sl.status = 'occupied' THEN sl.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-07 10:05:58] [info] Database connected successfully
[2025-06-07 10:05:58] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'field list' SQL: SELECT b.*, 
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.location_id) as box_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM bundles b
                 LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.status = 'active'
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 10:06:05] [info] Database connected successfully
[2025-06-07 10:06:05] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:06:05] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:06:12] [info] Database connected successfully
[2025-06-07 10:07:57] [info] Database connected successfully
[2025-06-07 10:07:57] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'field list' SQL: SELECT b.*, 
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.location_id) as box_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM bundles b
                 LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.status = 'active'
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 10:07:57] [info] Database connected successfully
[2025-06-07 10:08:04] [info] Database connected successfully
[2025-06-07 10:08:04] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'on clause' SQL: SELECT w.*, 
                        COUNT(DISTINCT sl.id) as total_locations,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' THEN sl.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' AND sl.status = 'occupied' THEN sl.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-07 10:08:04] [info] Database connected successfully
[2025-06-07 10:08:13] [info] Database connected successfully
[2025-06-07 10:08:13] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.city' in 'field list' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:08:13] [info] Database connected successfully
[2025-06-07 10:08:21] [info] Database connected successfully
[2025-06-07 10:08:21] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:08:21] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:08:22] [info] Database connected successfully
[2025-06-07 10:08:23] [info] Database connected successfully
[2025-06-07 10:08:23] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:08:23] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:08:23] [info] Database connected successfully
[2025-06-07 10:10:46] [info] Database connected successfully
[2025-06-07 10:10:46] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'on clause' SQL: SELECT w.*, 
                        COUNT(DISTINCT sl.id) as total_locations,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' THEN sl.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' AND sl.status = 'occupied' THEN sl.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-07 10:10:46] [info] Database connected successfully
[2025-06-07 10:10:56] [info] Database connected successfully
[2025-06-07 10:10:56] [info] Database connected successfully
[2025-06-07 10:10:56] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.city' in 'field list' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:10:56] [info] Database connected successfully
[2025-06-07 10:11:04] [info] Database connected successfully
[2025-06-07 10:11:07] [info] Database connected successfully
[2025-06-07 10:11:09] [info] Database connected successfully
[2025-06-07 10:11:09] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'field list' SQL: SELECT b.*, 
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.location_id) as box_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM bundles b
                 LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.status = 'active'
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 10:11:09] [info] Database connected successfully
[2025-06-07 10:11:16] [info] Database connected successfully
[2025-06-07 10:11:16] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:11:16] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:11:17] [info] Database connected successfully
[2025-06-07 10:11:21] [info] Database connected successfully
[2025-06-07 10:11:21] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'on clause' SQL: SELECT w.*, 
                        COUNT(DISTINCT sl.id) as total_locations,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' THEN sl.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' AND sl.status = 'occupied' THEN sl.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-07 10:11:21] [info] Database connected successfully
[2025-06-07 10:11:23] [info] Database connected successfully
[2025-06-07 10:11:23] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'on clause' SQL: SELECT w.*, 
                        COUNT(DISTINCT sl.id) as total_locations,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' THEN sl.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' AND sl.status = 'occupied' THEN sl.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-07 10:11:23] [info] Database connected successfully
[2025-06-07 10:11:24] [info] Database connected successfully
[2025-06-07 10:11:29] [info] Database connected successfully
[2025-06-07 10:11:29] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.location_id' in 'field list' SQL: SELECT b.*, 
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.location_id) as box_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM bundles b
                 LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.status = 'active'
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 10:11:29] [info] Database connected successfully
[2025-06-07 10:11:31] [info] Database connected successfully
[2025-06-07 10:11:31] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.city' in 'field list' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:11:31] [info] Database connected successfully
[2025-06-07 10:14:30] [info] Database connected successfully
[2025-06-07 10:14:30] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.city' in 'field list' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:14:31] [info] Database connected successfully
[2025-06-07 10:14:39] [info] Database connected successfully
[2025-06-07 10:14:39] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.created_by' in 'on clause' SQL: SELECT w.*, 
                        COUNT(DISTINCT sl.id) as total_locations,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' THEN sl.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' AND sl.status = 'occupied' THEN sl.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-07 10:14:39] [info] Database connected successfully
[2025-06-07 10:14:48] [info] Database connected successfully
[2025-06-07 10:15:01] [info] Database connected successfully
[2025-06-07 10:15:08] [info] Database connected successfully
[2025-06-07 10:15:08] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:15:08] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:15:08] [info] Database connected successfully
[2025-06-07 10:15:14] [info] Database connected successfully
[2025-06-07 10:15:14] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.created_by' in 'on clause' SQL: SELECT w.*, 
                        COUNT(DISTINCT sl.id) as total_locations,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' THEN sl.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' AND sl.status = 'occupied' THEN sl.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-07 10:15:14] [info] Database connected successfully
[2025-06-07 10:15:22] [info] Database connected successfully
[2025-06-07 10:15:22] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.created_by' in 'on clause' SQL: SELECT w.*, 
                        COUNT(DISTINCT sl.id) as total_locations,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' THEN sl.id END) as total_boxes,
                        COUNT(DISTINCT CASE WHEN sl.type = 'box' AND sl.status = 'occupied' THEN sl.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN storage_locations sl ON w.id = sl.warehouse_id AND sl.status = 'active'
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-07 10:15:22] [info] Database connected successfully
[2025-06-07 10:17:30] [info] Database connected successfully
[2025-06-07 10:17:37] [info] Database connected successfully
[2025-06-07 10:17:39] [info] Database connected successfully
[2025-06-07 10:17:39] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:17:39] [info] Database connected successfully
[2025-06-07 10:17:47] [info] Database connected successfully
[2025-06-07 10:17:53] [info] Database connected successfully
[2025-06-07 10:17:53] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:17:53] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:17:53] [info] Database connected successfully
[2025-06-07 10:18:00] [info] Database connected successfully
[2025-06-07 10:18:23] [info] Database connected successfully
[2025-06-07 10:18:25] [info] Database connected successfully
[2025-06-07 10:18:25] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:18:25] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:18:25] [info] Database connected successfully
[2025-06-07 10:18:32] [info] Database connected successfully
[2025-06-07 10:18:32] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:18:32] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:18:32] [info] Database connected successfully
[2025-06-07 10:18:51] [info] Database connected successfully
[2025-06-07 10:18:51] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:18:51] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:18:51] [info] Database connected successfully
[2025-06-07 10:19:01] [info] Database connected successfully
[2025-06-07 10:19:01] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:19:02] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:19:02] [info] Database connected successfully
[2025-06-07 10:19:03] [info] Database connected successfully
[2025-06-07 10:21:43] [info] Database connected successfully
[2025-06-07 10:24:31] [info] Database connected successfully
[2025-06-07 10:24:32] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.categories' doesn't exist SQL: SELECT d.*, u.first_name, u.last_name, c.name as category_name,
                       sl.name as location_name, sl.identifier as location_identifier,
                       w.name as warehouse_name, b.name as bundle_name
                FROM documents d
                LEFT JOIN users u ON d.created_by = u.id
                LEFT JOIN categories c ON d.category_id = c.id
                LEFT JOIN storage_locations sl ON d.location_id = sl.id
                LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                LEFT JOIN bundles b ON d.bundle_id = b.id
                WHERE d.company_id = ? AND d.status != 'deleted'
                ORDER BY d.created_at DESC
                LIMIT 20 OFFSET 0
[2025-06-07 10:24:42] [info] Database connected successfully
[2025-06-07 10:24:42] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:24:42] [info] Database connected successfully
[2025-06-07 10:24:53] [info] Database connected successfully
[2025-06-07 10:25:04] [info] Database connected successfully
[2025-06-07 10:25:10] [info] Database connected successfully
[2025-06-07 10:25:14] [info] Database connected successfully
[2025-06-07 10:25:14] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:25:14] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:25:14] [info] Database connected successfully
[2025-06-07 10:25:36] [info] Database connected successfully
[2025-06-07 10:25:36] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:25:36] [info] Database connected successfully
[2025-06-07 10:25:41] [info] Database connected successfully
[2025-06-07 10:25:43] [info] Database connected successfully
[2025-06-07 10:25:43] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.categories' doesn't exist SQL: SELECT d.*, u.first_name, u.last_name, c.name as category_name,
                       sl.name as location_name, sl.identifier as location_identifier,
                       w.name as warehouse_name, b.name as bundle_name
                FROM documents d
                LEFT JOIN users u ON d.created_by = u.id
                LEFT JOIN categories c ON d.category_id = c.id
                LEFT JOIN storage_locations sl ON d.location_id = sl.id
                LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                LEFT JOIN bundles b ON d.bundle_id = b.id
                WHERE d.company_id = ? AND d.status != 'deleted'
                ORDER BY d.created_at DESC
                LIMIT 20 OFFSET 0
[2025-06-07 10:28:33] [info] Database connected successfully
[2025-06-07 10:28:33] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' SQL: SELECT id, name, description
             FROM categories
             WHERE company_id = ? AND status = 'active'
             ORDER BY name
[2025-06-07 10:28:45] [info] Database connected successfully
[2025-06-07 10:28:45] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:28:45] [info] Database connected successfully
[2025-06-07 10:28:59] [info] Database connected successfully
[2025-06-07 10:29:10] [info] Database connected successfully
[2025-06-07 10:29:21] [info] Database connected successfully
[2025-06-07 10:29:21] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:29:21] [info] Database connected successfully
[2025-06-07 10:29:23] [info] Database connected successfully
[2025-06-07 10:29:25] [info] Database connected successfully
[2025-06-07 10:29:25] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:29:25] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:29:25] [info] Database connected successfully
[2025-06-07 10:29:53] [info] Database connected successfully
[2025-06-07 10:29:55] [info] Database connected successfully
[2025-06-07 10:29:55] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:29:55] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:29:55] [info] Database connected successfully
[2025-06-07 10:29:58] [info] Database connected successfully
[2025-06-07 10:29:58] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' SQL: SELECT id, name, description
             FROM categories
             WHERE company_id = ? AND status = 'active'
             ORDER BY name
[2025-06-07 10:30:02] [info] Database connected successfully
[2025-06-07 10:30:06] [info] Database connected successfully
[2025-06-07 10:30:11] [info] Database connected successfully
[2025-06-07 10:30:11] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:30:11] [info] Database connected successfully
[2025-06-07 10:30:14] [info] Database connected successfully
[2025-06-07 10:30:14] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:30:14] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:30:14] [info] Database connected successfully
[2025-06-07 10:30:16] [info] Database connected successfully
[2025-06-07 10:30:16] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' SQL: SELECT id, name, description
             FROM categories
             WHERE company_id = ? AND status = 'active'
             ORDER BY name
[2025-06-07 10:30:20] [info] Database connected successfully
[2025-06-07 10:30:20] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:30:20] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:30:20] [info] Database connected successfully
[2025-06-07 10:30:24] [info] Database connected successfully
[2025-06-07 10:30:24] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:30:24] [info] Database connected successfully
[2025-06-07 10:30:29] [info] Database connected successfully
[2025-06-07 10:30:29] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:30:29] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:30:29] [info] Database connected successfully
[2025-06-07 10:30:40] [info] Database connected successfully
[2025-06-07 10:30:40] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT COUNT(*) as count FROM document_intake 
                 WHERE company_id = ? AND status = 'pending'
[2025-06-07 10:30:40] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_intake' doesn't exist SQL: SELECT di.*, u.first_name, u.last_name,
                    COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20
[2025-06-07 10:30:40] [info] Database connected successfully
[2025-06-07 10:31:27] [info] Database connected successfully
[2025-06-07 10:31:27] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'companies' already exists SQL: -- Create companies table
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    storage_used BIGINT DEFAULT 0,
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_status (status),
    INDEX idx_companies_created (created_at)
)
[2025-06-07 10:31:27] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists SQL: -- Create users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_users_company (company_id),
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
)
[2025-06-07 10:31:27] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    IND...' at line 20 SQL: -- Create warehouses table
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    INDEX idx_warehouses_company (company_id),
    INDEX idx_warehouses_manager (manager_id),
    INDEX idx_warehouses_status (status),
    INDEX idx_warehouses_created (created_at)
)
[2025-06-07 10:31:27] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'storage_locations' already exists SQL: -- Create storage_locations table
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_location (warehouse_id, identifier),
    INDEX idx_locations_warehouse (warehouse_id),
    INDEX idx_locations_parent (parent_id),
    INDEX idx_locations_type (type),
    INDEX idx_locations_barcode (barcode_value),
    INDEX idx_locations_status (status),
    INDEX idx_locations_hierarchy (warehouse_id, parent_id, type)
)
[2025-06-07 10:31:27] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'document_categories' already exists SQL: -- Create document_categories table
CREATE TABLE document_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES document_categories(id) ON DELETE CASCADE,
    
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_sort (sort_order),
    INDEX idx_categories_system (is_system)
)
[2025-06-07 10:31:27] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY ...' at line 39 SQL: -- Create documents table
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL, -- For version control
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash
    version VARCHAR(20) DEFAULT '1.0',
    is_latest_version BOOLEAN DEFAULT TRUE,
    document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
    category_id INT,
    tags JSON,
    metadata JSON,
    ocr_text LONGTEXT,
    thumbnail_path VARCHAR(500),
    preview_path VARCHAR(500),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    access_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    retention_date DATE,
    expiry_date DATE,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES document_categories(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    
    INDEX idx_documents_company (company_id),
    INDEX idx_documents_parent (parent_id),
    INDEX idx_documents_category (category_id),
    INDEX idx_documents_creator (created_by),
    INDEX idx_documents_type (document_type),
    INDEX idx_documents_status (status),
    INDEX idx_documents_version (is_latest_version),
    INDEX idx_documents_created (created_at),
    INDEX idx_documents_hash (file_hash),
    INDEX idx_documents_company_search (company_id, status, created_at),
    
    FULLTEXT KEY ft_documents_content (title, description, ocr_text)
)
[2025-06-07 10:32:43] [info] Database connected successfully
[2025-06-07 10:32:45] [info] Database connected successfully
[2025-06-07 10:33:24] [info] Database connected successfully
[2025-06-07 10:33:24] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:33:24] [info] Database connected successfully
[2025-06-07 10:34:00] [info] Database connected successfully
[2025-06-07 10:34:13] [info] Database connected successfully
[2025-06-07 10:34:13] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'companies' already exists SQL: -- Create companies table
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    storage_used BIGINT DEFAULT 0,
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_status (status),
    INDEX idx_companies_created (created_at)
)
[2025-06-07 10:34:13] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists SQL: -- Create users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_users_company (company_id),
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
)
[2025-06-07 10:34:13] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    IND...' at line 20 SQL: -- Create warehouses table
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    INDEX idx_warehouses_company (company_id),
    INDEX idx_warehouses_manager (manager_id),
    INDEX idx_warehouses_status (status),
    INDEX idx_warehouses_created (created_at)
)
[2025-06-07 10:34:13] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'storage_locations' already exists SQL: -- Create storage_locations table
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_location (warehouse_id, identifier),
    INDEX idx_locations_warehouse (warehouse_id),
    INDEX idx_locations_parent (parent_id),
    INDEX idx_locations_type (type),
    INDEX idx_locations_barcode (barcode_value),
    INDEX idx_locations_status (status),
    INDEX idx_locations_hierarchy (warehouse_id, parent_id, type)
)
[2025-06-07 10:34:13] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'document_categories' already exists SQL: -- Create document_categories table
CREATE TABLE document_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES document_categories(id) ON DELETE CASCADE,
    
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_sort (sort_order),
    INDEX idx_categories_system (is_system)
)
[2025-06-07 10:34:13] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY ...' at line 39 SQL: -- Create documents table
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL, -- For version control
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash
    version VARCHAR(20) DEFAULT '1.0',
    is_latest_version BOOLEAN DEFAULT TRUE,
    document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
    category_id INT,
    tags JSON,
    metadata JSON,
    ocr_text LONGTEXT,
    thumbnail_path VARCHAR(500),
    preview_path VARCHAR(500),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    access_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    retention_date DATE,
    expiry_date DATE,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES document_categories(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    
    INDEX idx_documents_company (company_id),
    INDEX idx_documents_parent (parent_id),
    INDEX idx_documents_category (category_id),
    INDEX idx_documents_creator (created_by),
    INDEX idx_documents_type (document_type),
    INDEX idx_documents_status (status),
    INDEX idx_documents_version (is_latest_version),
    INDEX idx_documents_created (created_at),
    INDEX idx_documents_hash (file_hash),
    INDEX idx_documents_company_search (company_id, status, created_at),
    
    FULLTEXT KEY ft_documents_content (title, description, ocr_text)
)
[2025-06-07 10:34:14] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx...' at line 16 SQL: -- Create document_locations table
CREATE TABLE document_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    document_id INT NOT NULL,
    location_id INT,
    storage_type ENUM('physical', 'digital', 'hybrid') NOT NULL,
    physical_reference VARCHAR(255), -- Box number, shelf reference, etc.
    digital_path VARCHAR(1000),
    quantity INT DEFAULT 1,
    condition_notes TEXT,
    moved_by INT NOT NULL,
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx_doc_locations_document (document_id),
    INDEX idx_doc_locations_location (location_id),
    INDEX idx_doc_locations_current (is_current),
    INDEX idx_doc_locations_moved (moved_at),
    INDEX idx_doc_locations_type (storage_type)
)
[2025-06-07 10:34:14] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'barcodes' already exists SQL: -- Create barcodes table
CREATE TABLE barcodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('document', 'location', 'box', 'bundle') NOT NULL,
    entity_id INT NOT NULL,
    barcode_value VARCHAR(255) UNIQUE NOT NULL,
    barcode_type ENUM('qr', 'code128', 'code39', 'ean13', 'datamatrix') DEFAULT 'qr',
    barcode_image_path VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    generated_by INT NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_scanned_at TIMESTAMP NULL,
    scan_count INT DEFAULT 0,
    
    FOREIGN KEY (generated_by) REFERENCES users(id),
    
    INDEX idx_barcodes_value (barcode_value),
    INDEX idx_barcodes_entity (entity_type, entity_id),
    INDEX idx_barcodes_active (is_active),
    INDEX idx_barcodes_generated (generated_at),
    INDEX idx_barcodes_scan (barcode_value, is_active)
)
[2025-06-07 10:34:14] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_type' SQL: -- Service Provider Model Enhancements
-- Updates to support third-party document storage service provider business model

-- Add storage type to documents table (physical vs online)
ALTER TABLE documents 
ADD COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER location_id,
ADD COLUMN online_storage_path VARCHAR(500) NULL AFTER storage_type,
ADD COLUMN physical_location_notes TEXT NULL AFTER online_storage_path,
ADD INDEX idx_storage_type (storage_type),
ADD INDEX idx_company_storage (company_id, storage_type)
[2025-06-07 10:34:14] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX i...' at line 15 SQL: -- Create bundles table
CREATE TABLE bundles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    bundle_type ENUM('intake', 'project', 'department', 'custom') DEFAULT 'custom',
    location_id INT,
    created_by INT NOT NULL,
    status ENUM('open', 'closed', 'archived') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_bundles_company (company_id),
    INDEX idx_bundles_location (location_id),
    INDEX idx_bundles_creator (created_by),
    INDEX idx_bundles_type (bundle_type),
    INDEX idx_bundles_status (status),
    INDEX idx_bundles_created (created_at)
)
[2025-06-07 10:35:13] [info] Database connected successfully
[2025-06-07 10:35:51] [info] Database connected successfully
[2025-06-07 10:35:53] [info] Database connected successfully
[2025-06-07 10:36:01] [info] Database connected successfully
[2025-06-07 10:36:06] [info] Database connected successfully
[2025-06-07 10:36:39] [info] Database connected successfully
[2025-06-07 10:36:39] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.created_by' in 'on clause' SQL: SELECT sl.*, w.name as warehouse_name, w.city, w.state,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT d.bundle_id) as bundle_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name
                 FROM storage_locations sl
                 LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                 LEFT JOIN documents d ON sl.id = d.location_id AND d.status != 'deleted'
                 LEFT JOIN users u ON sl.created_by = u.id
                 WHERE sl.type = 'box' AND sl.status = 'active'
                 GROUP BY sl.id
                 ORDER BY sl.created_at DESC
[2025-06-07 10:36:39] [info] Database connected successfully
[2025-06-07 10:38:01] [info] Database connected successfully
[2025-06-07 10:38:03] [info] Database connected successfully
[2025-06-07 10:38:03] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' SQL: SELECT id, name, description
             FROM categories
             WHERE company_id = ? AND status = 'active'
             ORDER BY name
[2025-06-07 10:38:07] [info] Database connected successfully
[2025-06-07 10:38:59] [info] Database connected successfully
[2025-06-07 10:39:25] [info] Database connected successfully
[2025-06-07 10:39:44] [info] Database connected successfully
[2025-06-07 10:39:49] [info] Database connected successfully
[2025-06-07 10:39:59] [info] Database connected successfully
[2025-06-07 10:40:01] [info] Database connected successfully
[2025-06-07 10:40:07] [info] Database connected successfully
[2025-06-07 10:40:13] [info] Database connected successfully
[2025-06-07 10:43:54] [info] Database connected successfully
[2025-06-07 10:44:19] [info] Database connected successfully
[2025-06-07 10:44:39] [info] Database connected successfully
[2025-06-07 10:44:59] [info] Database connected successfully
[2025-06-07 10:49:01] [info] Database connected successfully
[2025-06-07 10:49:21] [info] Database connected successfully
[2025-06-07 10:49:55] [info] Database connected successfully
[2025-06-07 10:50:01] [info] Database connected successfully
[2025-06-07 10:50:02] [info] Database connected successfully
[2025-06-07 10:50:04] [info] Database connected successfully
[2025-06-07 10:50:04] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' SQL: SELECT id, name, description
             FROM categories
             WHERE company_id = ? AND status = 'active'
             ORDER BY name
[2025-06-07 10:51:42] [info] Database connected successfully
[2025-06-07 10:51:47] [info] Database connected successfully
[2025-06-07 10:51:56] [info] Database connected successfully
[2025-06-07 10:51:57] [info] Database connected successfully
[2025-06-07 10:53:36] [info] Database connected successfully
[2025-06-07 10:53:40] [info] Database connected successfully
[2025-06-07 10:54:09] [info] Database connected successfully
[2025-06-07 10:54:09] [info] Database connected successfully
[2025-06-07 10:54:12] [info] Database connected successfully
[2025-06-07 10:54:12] [info] Database connected successfully
[2025-06-07 10:54:17] [info] Database connected successfully
[2025-06-07 10:54:52] [info] Database connected successfully
[2025-06-07 10:54:56] [info] Database connected successfully
[2025-06-07 10:55:39] [info] Database connected successfully
[2025-06-07 10:56:03] [info] Database connected successfully
[2025-06-07 10:57:04] [info] Database connected successfully
[2025-06-07 10:57:13] [info] Database connected successfully
[2025-06-07 10:57:27] [info] Database connected successfully
[2025-06-07 10:57:34] [info] Database connected successfully
[2025-06-07 10:57:34] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.reference_number' in 'field list' SQL: SELECT d.*, b.name as bundle_name, b.reference_number as bundle_reference,
                    c.name as company_name, u.first_name, u.last_name
             FROM documents d
             LEFT JOIN bundles b ON d.bundle_id = b.id
             LEFT JOIN companies c ON d.company_id = c.id
             LEFT JOIN users u ON d.created_by = u.id
             WHERE d.location_id = ? AND d.status != 'deleted'
             ORDER BY d.created_at DESC
[2025-06-07 10:57:34] [info] Database connected successfully
[2025-06-07 10:59:09] [info] Database connected successfully
[2025-06-07 10:59:42] [info] Database connected successfully
[2025-06-07 11:00:46] [info] Database connected successfully
[2025-06-07 11:00:48] [info] Database connected successfully
[2025-06-07 11:01:19] [info] Database connected successfully
[2025-06-07 11:01:25] [info] Database connected successfully
[2025-06-07 11:01:28] [info] Database connected successfully
[2025-06-07 11:01:30] [info] Database connected successfully
[2025-06-07 11:01:32] [info] Database connected successfully
[2025-06-07 11:01:33] [info] Database connected successfully
[2025-06-07 11:01:35] [info] Database connected successfully
[2025-06-07 11:01:36] [info] Database connected successfully
[2025-06-07 11:01:37] [info] Database connected successfully
[2025-06-07 11:01:39] [info] Database connected successfully
[2025-06-07 11:06:38] [info] Database connected successfully
[2025-06-07 11:07:56] [info] Database connected successfully
[2025-06-07 11:08:15] [info] Database connected successfully
[2025-06-07 11:31:34] [info] Database connected successfully
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'companies' already exists SQL: -- Create companies table
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    storage_used BIGINT DEFAULT 0,
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_status (status),
    INDEX idx_companies_created (created_at)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists SQL: -- Create users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_users_company (company_id),
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    IND...' at line 20 SQL: -- Create warehouses table
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    INDEX idx_warehouses_company (company_id),
    INDEX idx_warehouses_manager (manager_id),
    INDEX idx_warehouses_status (status),
    INDEX idx_warehouses_created (created_at)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'storage_locations' already exists SQL: -- Create storage_locations table
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_location (warehouse_id, identifier),
    INDEX idx_locations_warehouse (warehouse_id),
    INDEX idx_locations_parent (parent_id),
    INDEX idx_locations_type (type),
    INDEX idx_locations_barcode (barcode_value),
    INDEX idx_locations_status (status),
    INDEX idx_locations_hierarchy (warehouse_id, parent_id, type)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'document_categories' already exists SQL: -- Create document_categories table
CREATE TABLE document_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES document_categories(id) ON DELETE CASCADE,
    
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_sort (sort_order),
    INDEX idx_categories_system (is_system)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY ...' at line 39 SQL: -- Create documents table
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL, -- For version control
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash
    version VARCHAR(20) DEFAULT '1.0',
    is_latest_version BOOLEAN DEFAULT TRUE,
    document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
    category_id INT,
    tags JSON,
    metadata JSON,
    ocr_text LONGTEXT,
    thumbnail_path VARCHAR(500),
    preview_path VARCHAR(500),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    access_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    retention_date DATE,
    expiry_date DATE,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES document_categories(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    
    INDEX idx_documents_company (company_id),
    INDEX idx_documents_parent (parent_id),
    INDEX idx_documents_category (category_id),
    INDEX idx_documents_creator (created_by),
    INDEX idx_documents_type (document_type),
    INDEX idx_documents_status (status),
    INDEX idx_documents_version (is_latest_version),
    INDEX idx_documents_created (created_at),
    INDEX idx_documents_hash (file_hash),
    INDEX idx_documents_company_search (company_id, status, created_at),
    
    FULLTEXT KEY ft_documents_content (title, description, ocr_text)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx...' at line 16 SQL: -- Create document_locations table
CREATE TABLE document_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    document_id INT NOT NULL,
    location_id INT,
    storage_type ENUM('physical', 'digital', 'hybrid') NOT NULL,
    physical_reference VARCHAR(255), -- Box number, shelf reference, etc.
    digital_path VARCHAR(1000),
    quantity INT DEFAULT 1,
    condition_notes TEXT,
    moved_by INT NOT NULL,
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx_doc_locations_document (document_id),
    INDEX idx_doc_locations_location (location_id),
    INDEX idx_doc_locations_current (is_current),
    INDEX idx_doc_locations_moved (moved_at),
    INDEX idx_doc_locations_type (storage_type)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'barcodes' already exists SQL: -- Create barcodes table
CREATE TABLE barcodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('document', 'location', 'box', 'bundle') NOT NULL,
    entity_id INT NOT NULL,
    barcode_value VARCHAR(255) UNIQUE NOT NULL,
    barcode_type ENUM('qr', 'code128', 'code39', 'ean13', 'datamatrix') DEFAULT 'qr',
    barcode_image_path VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    generated_by INT NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_scanned_at TIMESTAMP NULL,
    scan_count INT DEFAULT 0,
    
    FOREIGN KEY (generated_by) REFERENCES users(id),
    
    INDEX idx_barcodes_value (barcode_value),
    INDEX idx_barcodes_entity (entity_type, entity_id),
    INDEX idx_barcodes_active (is_active),
    INDEX idx_barcodes_generated (generated_at),
    INDEX idx_barcodes_scan (barcode_value, is_active)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_type' SQL: -- Service Provider Model Enhancements
-- Updates to support third-party document storage service provider business model

-- Add storage type to documents table (physical vs online)
ALTER TABLE documents 
ADD COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER location_id,
ADD COLUMN online_storage_path VARCHAR(500) NULL AFTER storage_type,
ADD COLUMN physical_location_notes TEXT NULL AFTER online_storage_path,
ADD INDEX idx_storage_type (storage_type),
ADD INDEX idx_company_storage (company_id, storage_type)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX i...' at line 15 SQL: -- Create bundles table
CREATE TABLE bundles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    bundle_type ENUM('intake', 'project', 'department', 'custom') DEFAULT 'custom',
    location_id INT,
    created_by INT NOT NULL,
    status ENUM('open', 'closed', 'archived') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_bundles_company (company_id),
    INDEX idx_bundles_location (location_id),
    INDEX idx_bundles_creator (created_by),
    INDEX idx_bundles_type (bundle_type),
    INDEX idx_bundles_status (status),
    INDEX idx_bundles_created (created_at)
)
[2025-06-07 11:31:34] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    INDEX idx_audit_company (company_id),
    INDEX idx_audit_...' at line 17 SQL: -- Create audit_logs table
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    user_id INT,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    action ENUM('create', 'read', 'update', 'delete', 'login', 'logout', 'download', 'upload', 'scan') NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON SET NULL,
    
    INDEX idx_audit_company (company_id),
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_entity (entity_type, entity_id),
    INDEX idx_audit_action (action),
    INDEX idx_audit_created (created_at),
    INDEX idx_audit_logs_search (company_id, entity_type, action, created_at),
    INDEX idx_audit_company_date (company_id, created_at),
    INDEX idx_audit_user_action (user_id, action, created_at)
)
[2025-06-07 11:33:43] [info] Database connected successfully
[2025-06-07 11:33:43] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'box_id' doesn't exist in table SQL: -- Update document_intake table to link to boxes
ALTER TABLE document_intake 
ADD FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL,
ADD INDEX idx_intake_box (box_id)
[2025-06-07 11:33:43] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: -- Update bundles table to match documentation requirements
-- Implements Bundle Creation from documentation (Step 3)

-- Add new columns to bundles table for documentation compliance
ALTER TABLE bundles 
ADD COLUMN box_id INT NULL AFTER location_id,
ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation',
ADD COLUMN year INT NULL COMMENT 'Year as per documentation', 
ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation',
ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation',
ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation',
ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation',
ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation'
[2025-06-07 11:33:43] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundle_box' SQL: -- Add foreign key for box relationship
ALTER TABLE bundles 
ADD FOREIGN KEY (box_id) REFERENCES boxes(id) ON DELETE SET NULL,
ADD INDEX idx_bundle_box (box_id)
[2025-06-07 11:33:43] [error] SQL execution failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.document_type' in 'field list' SQL: -- Update bundle reference numbers to follow CLIENT01-BOX001-BUNDLE03 format
-- This will be handled by the application logic for new bundles

-- Create view for bundle overview with box information
CREATE OR REPLACE VIEW bundle_overview AS
SELECT 
    b.id,
    b.name,
    b.reference_number,
    b.document_type,
    b.year,
    b.department,
    b.confidentiality_flag,
    b.pages_volume,
    b.scan_digitization_status,
    b.contents_summary,
    b.status,
    b.created_at,
    b.updated_at,
    box.box_id,
    box.storage_location_code,
    w.name as warehouse_name,
    c.name as company_name,
    u.first_name,
    u.last_name,
    COUNT(d.id) as document_count,
    SUM(d.file_size) as total_size
FROM bundles b
LEFT JOIN boxes box ON b.box_id = box.id
LEFT JOIN warehouses w ON box.warehouse_id = w.id
LEFT JOIN companies c ON b.company_id = c.id
LEFT JOIN users u ON b.created_by = u.id
LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
WHERE b.status = 'active'
GROUP BY b.id, box.id, w.id, c.id, u.id
[2025-06-07 11:33:43] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'document_type' doesn't exist in table SQL: -- Create indexes for better performance
CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 11:33:43] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'year' doesn't exist in table SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 11:33:43] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'department' doesn't exist in table SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 11:33:44] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'confidentiality_flag' doesn't exist in table SQL: CREATE INDEX idx_bundles_confidentiality ON bundles(confidentiality_flag)
[2025-06-07 11:33:44] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'scan_digitization_status' doesn't exist in table SQL: CREATE INDEX idx_bundles_scan_status ON bundles(scan_digitization_status)
[2025-06-07 11:33:44] [error] SQL execution failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'document_type' in 'where clause' SQL: -- Update sample data to include new fields
UPDATE bundles 
SET 
    document_type = CASE 
        WHEN name LIKE '%invoice%' THEN 'invoice'
        WHEN name LIKE '%contract%' THEN 'contract'
        WHEN name LIKE '%report%' THEN 'report'
        ELSE 'general'
    END,
    year = YEAR(created_at),
    department = CASE 
        WHEN name LIKE '%HR%' OR name LIKE '%human%' THEN 'HR'
        WHEN name LIKE '%finance%' OR name LIKE '%accounting%' THEN 'Finance'
        WHEN name LIKE '%legal%' THEN 'Legal'
        ELSE 'General'
    END,
    confidentiality_flag = CASE 
        WHEN name LIKE '%confidential%' OR name LIKE '%private%' THEN TRUE
        ELSE FALSE
    END,
    scan_digitization_status = 'not_scanned',
    contents_summary = CONCAT('Bundle containing ', name, ' documents')
WHERE document_type IS NULL
[2025-06-07 11:34:20] [info] Database connected successfully
[2025-06-07 11:35:03] [info] Database connected successfully
[2025-06-07 11:39:03] [info] Database connected successfully
[2025-06-07 11:39:03] [info] Database connected successfully
[2025-06-07 11:39:17] [info] Database connected successfully
[2025-06-07 11:39:17] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 11:39:17] [info] Database connected successfully
[2025-06-07 11:39:19] [info] Database connected successfully
[2025-06-07 11:39:21] [info] Database connected successfully
[2025-06-07 11:39:22] [info] Database connected successfully
[2025-06-07 11:39:25] [info] Database connected successfully
[2025-06-07 11:39:29] [info] Database connected successfully
[2025-06-07 11:50:01] [info] Database connected successfully
[2025-06-07 11:50:58] [info] Database connected successfully
[2025-06-07 11:50:58] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.expiry_date' in 'where clause' SQL: SELECT d.*, b.company_id, b.reference_number as bundle_reference, c.name as company_name
             FROM documents d
             JOIN bundles b ON d.bundle_id = b.id
             JOIN companies c ON b.company_id = c.id
             WHERE d.expiry_date IS NOT NULL
             AND d.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
             AND d.expiry_date > CURDATE()
             AND d.status != 'deleted'
             AND NOT EXISTS (
                 SELECT 1 FROM automated_alerts 
                 WHERE target_type = 'document' 
                 AND target_id = d.id 
                 AND alert_type = 'document_expiry'
                 AND status IN ('pending', 'sent')
             )
[2025-06-07 11:52:07] [info] Database connected successfully
[2025-06-07 11:52:07] [info] Database connected successfully
[2025-06-07 11:52:56] [info] Database connected successfully
[2025-06-07 11:53:29] [info] Database connected successfully
[2025-06-07 11:53:32] [info] Database connected successfully
[2025-06-07 12:02:43] [info] Database connected successfully
[2025-06-07 12:04:49] [info] Database connected successfully
[2025-06-07 12:07:32] [info] Database connected successfully
[2025-06-07 12:08:49] [info] Database connected successfully
[2025-06-07 12:10:29] [info] Database connected successfully
[2025-06-07 12:11:23] [info] Database connected successfully
[2025-06-07 12:36:03] [info] Database connected successfully
[2025-06-07 12:36:46] [info] Database connected successfully
[2025-06-07 12:36:49] [info] Database connected successfully
[2025-06-07 12:36:53] [info] Database connected successfully
[2025-06-07 12:39:53] [info] Database connected successfully
[2025-06-07 12:40:16] [info] Database connected successfully
[2025-06-07 12:40:41] [info] Database connected successfully
[2025-06-07 12:40:44] [info] Database connected successfully
[2025-06-07 12:41:13] [info] Database connected successfully
[2025-06-07 12:42:57] [info] Database connected successfully
[2025-06-07 12:43:37] [info] Database connected successfully
[2025-06-07 12:44:55] [info] Database connected successfully
[2025-06-07 12:44:57] [info] Database connected successfully
[2025-06-07 12:45:20] [info] Database connected successfully
[2025-06-07 12:45:20] [info] Database connected successfully
[2025-06-07 12:45:53] [info] Database connected successfully
[2025-06-07 12:45:55] [info] Database connected successfully
[2025-06-07 12:45:57] [info] Database connected successfully
[2025-06-07 12:47:00] [info] Database connected successfully
[2025-06-07 12:47:00] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT al.*, u.first_name, u.last_name, u.email, c.name as company_name
             FROM activity_logs al
             JOIN users u ON al.user_id = u.id
             JOIN companies c ON u.company_id = c.id
             ORDER BY al.created_at DESC
             LIMIT 20
[2025-06-07 12:48:10] [info] Database connected successfully
[2025-06-07 12:48:10] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT al.*, u.first_name, u.last_name, u.email, c.name as company_name
             FROM activity_logs al
             JOIN users u ON al.user_id = u.id
             JOIN companies c ON u.company_id = c.id
             ORDER BY al.created_at DESC
             LIMIT 20
[2025-06-07 12:48:37] [info] Database connected successfully
[2025-06-07 12:50:19] [info] Database connected successfully
[2025-06-07 12:51:04] [info] Database connected successfully
[2025-06-07 12:51:07] [info] Database connected successfully
[2025-06-07 12:51:41] [info] Database connected successfully
[2025-06-07 12:54:52] [info] Database connected successfully
[2025-06-07 12:55:15] [info] Database connected successfully
[2025-06-07 12:55:15] [info] Database connected successfully
[2025-06-07 12:55:17] [info] Database connected successfully
[2025-06-07 12:55:25] [info] Database connected successfully
[2025-06-07 12:55:32] [info] Database connected successfully
[2025-06-07 12:55:40] [info] Database connected successfully
[2025-06-07 12:57:26] [info] Database connected successfully
[2025-06-07 12:57:26] [error] Query failed: SQLSTATE[HY000]: General error: 1364 Field 'target_id' doesn't have a default value SQL: INSERT INTO automated_alerts (company_id, title, message, severity, status, alert_type, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)
[2025-06-07 12:57:50] [info] Database connected successfully
[2025-06-07 12:58:44] [info] Database connected successfully
[2025-06-07 13:00:10] [info] Database connected successfully
[2025-06-07 13:00:14] [info] Database connected successfully
[2025-06-07 13:00:16] [info] Database connected successfully
[2025-06-07 13:00:20] [info] Database connected successfully
[2025-06-07 13:00:57] [info] Database connected successfully
[2025-06-07 13:01:47] [info] Database connected successfully
[2025-06-07 13:01:49] [info] Database connected successfully
[2025-06-07 13:03:22] [info] Database connected successfully
[2025-06-07 13:03:25] [info] Database connected successfully
[2025-06-07 13:04:08] [info] Database connected successfully
[2025-06-07 13:04:11] [info] Database connected successfully
[2025-06-07 13:04:15] [info] Database connected successfully
[2025-06-07 13:05:43] [info] Database connected successfully
[2025-06-07 13:05:55] [info] Database connected successfully
[2025-06-07 13:06:39] [info] Database connected successfully
[2025-06-07 13:09:10] [info] Database connected successfully
[2025-06-07 13:14:11] [info] Database connected successfully
[2025-06-07 13:18:22] [info] Database connected successfully
[2025-06-07 13:19:12] [info] Database connected successfully
[2025-06-07 13:19:24] [info] Database connected successfully
[2025-06-07 13:19:27] [info] Database connected successfully
[2025-06-07 13:19:57] [info] Database connected successfully
[2025-06-07 13:21:53] [info] Database connected successfully
[2025-06-07 13:22:08] [info] Database connected successfully
[2025-06-07 13:22:08] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 13:22:08] [info] Database connected successfully
[2025-06-07 13:22:10] [info] Database connected successfully
[2025-06-07 13:22:12] [info] Database connected successfully
[2025-06-07 13:22:13] [info] Database connected successfully
[2025-06-07 13:22:14] [info] Database connected successfully
[2025-06-07 13:22:15] [info] Database connected successfully
[2025-06-07 13:22:24] [info] Database connected successfully
[2025-06-07 13:23:10] [info] Database connected successfully
[2025-06-07 13:23:21] [info] Database connected successfully
[2025-06-07 13:23:37] [info] Database connected successfully
[2025-06-07 13:24:13] [info] Database connected successfully
[2025-06-07 13:26:05] [info] Database connected successfully
[2025-06-07 13:27:04] [info] Database connected successfully
[2025-06-07 13:28:32] [info] Database connected successfully
[2025-06-07 13:28:38] [info] Database connected successfully
[2025-06-07 13:28:41] [info] Database connected successfully
[2025-06-07 13:29:10] [info] Database connected successfully
[2025-06-07 13:29:14] [info] Database connected successfully
[2025-06-07 13:29:38] [info] Database connected successfully
[2025-06-07 13:30:47] [info] Database connected successfully
[2025-06-07 13:31:25] [info] Database connected successfully
[2025-06-07 13:31:33] [info] Database connected successfully
[2025-06-07 13:31:37] [info] Database connected successfully
[2025-06-07 13:31:40] [info] Database connected successfully
[2025-06-07 13:31:42] [info] Database connected successfully
[2025-06-07 13:31:42] [info] Database connected successfully
[2025-06-07 13:32:08] [info] Database connected successfully
[2025-06-07 13:33:39] [info] Database connected successfully
[2025-06-07 13:36:44] [info] Database connected successfully
[2025-06-07 13:38:31] [info] Database connected successfully
[2025-06-07 13:38:31] [info] Database connected successfully
[2025-06-07 13:38:35] [info] Database connected successfully
[2025-06-07 13:38:40] [info] Database connected successfully
[2025-06-07 13:38:41] [info] Database connected successfully
[2025-06-07 13:41:45] [info] Database connected successfully
[2025-06-07 13:41:45] [info] Database connected successfully
[2025-06-07 13:41:45] [info] Database connected successfully
[2025-06-07 13:41:59] [info] Database connected successfully
[2025-06-07 13:42:04] [info] Database connected successfully
[2025-06-07 13:42:27] [info] Database connected successfully
[2025-06-07 13:43:33] [info] Database connected successfully
[2025-06-07 13:43:41] [info] Database connected successfully
[2025-06-07 13:46:49] [info] Database connected successfully
[2025-06-07 13:48:34] [info] Database connected successfully
[2025-06-07 13:48:42] [info] Database connected successfully
[2025-06-07 13:51:51] [info] Database connected successfully
[2025-06-07 13:51:55] [info] Database connected successfully
[2025-06-07 13:51:55] [info] Database connected successfully
[2025-06-07 13:52:05] [info] Database connected successfully
[2025-06-07 13:52:17] [info] Database connected successfully
[2025-06-07 13:52:27] [info] Database connected successfully
[2025-06-07 13:52:27] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT al.*, u.first_name, u.last_name
                 FROM activity_logs al
                 LEFT JOIN users u ON al.user_id = u.id
                 WHERE al.company_id = ?
                 ORDER BY al.created_at DESC
                 LIMIT 20
[2025-06-07 13:53:07] [info] Database connected successfully
[2025-06-07 13:53:36] [info] Database connected successfully
[2025-06-07 13:53:43] [info] Database connected successfully
[2025-06-07 13:56:53] [info] Database connected successfully
[2025-06-07 13:56:57] [info] Database connected successfully
[2025-06-07 13:58:38] [info] Database connected successfully
[2025-06-07 13:58:45] [info] Database connected successfully
[2025-06-07 13:59:46] [info] Database connected successfully
[2025-06-07 13:59:48] [info] Database connected successfully
[2025-06-07 14:00:15] [info] Database connected successfully
[2025-06-07 14:00:19] [info] Database connected successfully
[2025-06-07 14:00:22] [info] Database connected successfully
[2025-06-07 14:00:27] [info] Database connected successfully
[2025-06-07 14:00:28] [info] Database connected successfully
[2025-06-07 14:01:55] [info] Database connected successfully
[2025-06-07 14:02:07] [info] Database connected successfully
[2025-06-07 14:02:09] [info] Database connected successfully
[2025-06-07 14:02:13] [info] Database connected successfully
[2025-06-07 14:03:39] [info] Database connected successfully
[2025-06-07 14:03:46] [info] Database connected successfully
[2025-06-07 14:06:56] [info] Database connected successfully
[2025-06-07 14:08:40] [info] Database connected successfully
[2025-06-07 14:08:47] [info] Database connected successfully
[2025-06-07 14:11:58] [info] Database connected successfully
[2025-06-07 14:13:42] [info] Database connected successfully
[2025-06-07 14:13:49] [info] Database connected successfully
[2025-06-07 14:17:00] [info] Database connected successfully
[2025-06-07 14:18:44] [info] Database connected successfully
[2025-06-07 14:18:51] [info] Database connected successfully
[2025-06-07 14:22:02] [info] Database connected successfully
[2025-06-07 14:23:46] [info] Database connected successfully
[2025-06-07 14:23:52] [info] Database connected successfully
[2025-06-07 14:27:04] [info] Database connected successfully
[2025-06-07 14:28:48] [info] Database connected successfully
[2025-06-07 14:28:54] [info] Database connected successfully
[2025-06-07 14:30:37] [info] Database connected successfully
[2025-06-07 14:31:37] [info] Database connected successfully
[2025-06-07 14:31:37] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT u.*, c.name as company_name,
                        COUNT(DISTINCT al.id) as activity_count,
                        MAX(al.created_at) as last_activity
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 LEFT JOIN activity_logs al ON u.id = al.user_id
                 
                 GROUP BY u.id
                 ORDER BY u.created_at DESC
                 LIMIT ? OFFSET ?
[2025-06-07 14:31:37] [info] Database connected successfully
[2025-06-07 14:31:42] [info] Database connected successfully
[2025-06-07 14:32:05] [info] Database connected successfully
[2025-06-07 14:32:06] [info] Database connected successfully
[2025-06-07 14:32:08] [info] Database connected successfully
[2025-06-07 14:32:08] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT u.*, c.name as company_name,
                        COUNT(DISTINCT al.id) as activity_count,
                        MAX(al.created_at) as last_activity
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 LEFT JOIN activity_logs al ON u.id = al.user_id
                 
                 GROUP BY u.id
                 ORDER BY u.created_at DESC
                 LIMIT ? OFFSET ?
[2025-06-07 14:32:08] [info] Database connected successfully
[2025-06-07 14:32:14] [info] Database connected successfully
[2025-06-07 14:32:16] [info] Database connected successfully
[2025-06-07 14:32:18] [info] Database connected successfully
[2025-06-07 14:32:18] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT u.*, c.name as company_name,
                        COUNT(DISTINCT al.id) as activity_count,
                        MAX(al.created_at) as last_activity
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 LEFT JOIN activity_logs al ON u.id = al.user_id
                 
                 GROUP BY u.id
                 ORDER BY u.created_at DESC
                 LIMIT ? OFFSET ?
[2025-06-07 14:32:18] [info] Database connected successfully
[2025-06-07 14:32:28] [info] Database connected successfully
[2025-06-07 14:32:30] [info] Database connected successfully
[2025-06-07 14:33:49] [info] Database connected successfully
[2025-06-07 14:33:55] [info] Database connected successfully
[2025-06-07 14:37:08] [info] Database connected successfully
[2025-06-07 14:38:00] [info] Database connected successfully
[2025-06-07 14:38:00] [info] Database connected successfully
[2025-06-07 14:38:50] [info] Database connected successfully
[2025-06-07 14:38:56] [info] Database connected successfully
[2025-06-07 14:39:00] [info] Database connected successfully
[2025-06-07 14:39:00] [info] Database connected successfully
[2025-06-07 14:39:01] [info] Database connected successfully
[2025-06-07 14:39:01] [info] Database connected successfully
[2025-06-07 14:39:09] [info] Database connected successfully
[2025-06-07 14:39:09] [info] Database connected successfully
[2025-06-07 14:39:21] [info] Database connected successfully
[2025-06-07 14:39:21] [info] Database connected successfully
[2025-06-07 14:41:38] [info] Database connected successfully
[2025-06-07 14:42:10] [info] Database connected successfully
[2025-06-07 14:42:12] [info] Database connected successfully
[2025-06-07 14:42:15] [info] Database connected successfully
[2025-06-07 14:42:15] [info] Database connected successfully
[2025-06-07 14:42:17] [info] Database connected successfully
[2025-06-07 14:42:17] [info] Database connected successfully
[2025-06-07 14:42:21] [info] Database connected successfully
[2025-06-07 14:42:21] [info] Database connected successfully
[2025-06-07 14:42:30] [info] Database connected successfully
[2025-06-07 14:42:30] [info] Database connected successfully
[2025-06-07 14:43:19] [info] Database connected successfully
[2025-06-07 14:43:19] [info] Database connected successfully
[2025-06-07 14:43:58] [info] Database connected successfully
[2025-06-07 14:45:59] [info] Database connected successfully
[2025-06-07 14:46:30] [info] Database connected successfully
[2025-06-07 14:46:34] [info] Database connected successfully
[2025-06-07 14:46:37] [info] Database connected successfully
[2025-06-07 14:46:37] [info] Database connected successfully
[2025-06-07 14:46:40] [info] Database connected successfully
[2025-06-07 14:46:40] [info] Database connected successfully
[2025-06-07 14:47:46] [info] Database connected successfully
[2025-06-07 14:47:56] [info] Database connected successfully
[2025-06-07 14:48:01] [info] Database connected successfully
[2025-06-07 14:48:20] [info] Database connected successfully
[2025-06-07 14:48:26] [info] Database connected successfully
[2025-06-07 14:48:26] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: INSERT INTO activity_logs (user_id, company_id, action, entity_type, entity_id, description, ip_address, created_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
[2025-06-07 14:48:26] [info] Database connected successfully
[2025-06-07 14:48:35] [info] Database connected successfully
[2025-06-07 14:48:39] [info] Database connected successfully
[2025-06-07 14:49:00] [info] Database connected successfully
[2025-06-07 14:51:13] [info] Database connected successfully
[2025-06-07 14:51:42] [info] Database connected successfully
[2025-06-07 14:51:47] [info] Database connected successfully
[2025-06-07 14:52:07] [info] Database connected successfully
[2025-06-07 14:52:12] [info] Database connected successfully
[2025-06-07 14:52:14] [info] Database connected successfully
[2025-06-07 14:52:17] [info] Database connected successfully
[2025-06-07 14:52:17] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT u.*, c.name as company_name,
                        COUNT(DISTINCT al.id) as activity_count,
                        MAX(al.created_at) as last_activity
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 LEFT JOIN activity_logs al ON u.id = al.user_id
                 
                 GROUP BY u.id
                 ORDER BY u.created_at DESC
                 LIMIT ? OFFSET ?
[2025-06-07 14:52:17] [info] Database connected successfully
[2025-06-07 14:53:22] [info] Database connected successfully
[2025-06-07 14:53:41] [info] Database connected successfully
[2025-06-07 14:54:02] [info] Database connected successfully
[2025-06-07 14:56:16] [info] Database connected successfully
[2025-06-07 14:58:24] [info] Database connected successfully
[2025-06-07 14:58:43] [info] Database connected successfully
[2025-06-07 14:59:04] [info] Database connected successfully
[2025-06-07 15:01:18] [info] Database connected successfully
[2025-06-07 15:03:26] [info] Database connected successfully
[2025-06-07 15:03:45] [info] Database connected successfully
[2025-06-07 15:04:05] [info] Database connected successfully
[2025-06-07 15:04:09] [info] Database connected successfully
[2025-06-07 15:04:20] [info] Database connected successfully
[2025-06-07 15:04:23] [info] Database connected successfully
[2025-06-07 15:04:23] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT u.*, c.name as company_name,
                        COUNT(DISTINCT al.id) as activity_count,
                        MAX(al.created_at) as last_activity
                 FROM users u
                 LEFT JOIN companies c ON u.company_id = c.id
                 LEFT JOIN activity_logs al ON u.id = al.user_id
                 
                 GROUP BY u.id
                 ORDER BY u.created_at DESC
                 LIMIT ? OFFSET ?
[2025-06-07 15:04:23] [info] Database connected successfully
[2025-06-07 15:06:22] [info] Database connected successfully
[2025-06-07 15:08:28] [info] Database connected successfully
[2025-06-07 15:09:09] [info] Database connected successfully
[2025-06-07 15:11:24] [info] Database connected successfully
[2025-06-07 15:13:30] [info] Database connected successfully
[2025-06-07 15:14:11] [info] Database connected successfully
[2025-06-07 15:16:26] [info] Database connected successfully
[2025-06-07 15:18:32] [info] Database connected successfully
[2025-06-07 15:19:13] [info] Database connected successfully
[2025-06-07 15:19:28] [info] Database connected successfully
[2025-06-07 15:19:37] [info] Database connected successfully
[2025-06-07 15:19:40] [info] Database connected successfully
[2025-06-07 15:19:47] [info] Database connected successfully
[2025-06-07 15:19:47] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.last_scanned_at' in 'field list' SQL: SELECT 
                COUNT(DISTINCT b.id) as total_barcodes,
                COUNT(DISTINCT CASE WHEN b.is_active = 1 THEN b.id END) as active_barcodes,
                COUNT(DISTINCT CASE WHEN b.last_scanned_at IS NOT NULL THEN b.id END) as scanned_barcodes,
                COUNT(DISTINCT CASE WHEN DATE(b.generated_at) = CURDATE() THEN b.id END) as generated_today,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'box' THEN b.id END) as box_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'bundle' THEN b.id END) as bundle_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'document' THEN b.id END) as document_barcodes,
                SUM(b.scan_count) as total_scans,
                AVG(b.scan_count) as avg_scans_per_barcode
             FROM barcodes b
             WHERE b.is_active = 1
[2025-06-07 15:19:47] [info] Database connected successfully
[2025-06-07 15:19:51] [info] Database connected successfully
[2025-06-07 15:19:51] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.capacity_percentage' in 'field list' SQL: SELECT 
                COUNT(DISTINCT w.id) as total_warehouses,
                COUNT(DISTINCT b.id) as total_boxes,
                COUNT(DISTINCT CASE WHEN b.status = 'occupied' THEN b.id END) as occupied_boxes,
                COUNT(DISTINCT bun.id) as total_bundles,
                COUNT(DISTINCT d.id) as total_documents,
                ROUND(AVG(w.capacity_percentage), 2) as avg_capacity_usage,
                COUNT(DISTINCT CASE WHEN w.status = 'active' THEN w.id END) as active_warehouses
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             LEFT JOIN bundles bun ON b.id = bun.box_id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.status = 'active'
[2025-06-07 15:19:51] [info] Database connected successfully
[2025-06-07 15:19:56] [info] Database connected successfully
[2025-06-07 15:19:56] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.system_performance_metrics' doesn't exist SQL: SELECT 
                metric_name,
                metric_value,
                metric_unit,
                status,
                recorded_at
             FROM system_performance_metrics 
             WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
             ORDER BY recorded_at DESC
             LIMIT 50
[2025-06-07 15:19:56] [info] Database connected successfully
[2025-06-07 15:20:00] [info] Database connected successfully
[2025-06-07 15:20:00] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.capacity_percentage' in 'field list' SQL: SELECT 
                COUNT(DISTINCT w.id) as total_warehouses,
                COUNT(DISTINCT b.id) as total_boxes,
                COUNT(DISTINCT CASE WHEN b.status = 'occupied' THEN b.id END) as occupied_boxes,
                COUNT(DISTINCT bun.id) as total_bundles,
                COUNT(DISTINCT d.id) as total_documents,
                ROUND(AVG(w.capacity_percentage), 2) as avg_capacity_usage,
                COUNT(DISTINCT CASE WHEN w.status = 'active' THEN w.id END) as active_warehouses
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             LEFT JOIN bundles bun ON b.id = bun.box_id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.status = 'active'
[2025-06-07 15:20:00] [info] Database connected successfully
[2025-06-07 15:20:09] [info] Database connected successfully
[2025-06-07 15:20:12] [info] Database connected successfully
[2025-06-07 15:20:12] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.capacity_percentage' in 'field list' SQL: SELECT 
                COUNT(DISTINCT w.id) as total_warehouses,
                COUNT(DISTINCT b.id) as total_boxes,
                COUNT(DISTINCT CASE WHEN b.status = 'occupied' THEN b.id END) as occupied_boxes,
                COUNT(DISTINCT bun.id) as total_bundles,
                COUNT(DISTINCT d.id) as total_documents,
                ROUND(AVG(w.capacity_percentage), 2) as avg_capacity_usage,
                COUNT(DISTINCT CASE WHEN w.status = 'active' THEN w.id END) as active_warehouses
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             LEFT JOIN bundles bun ON b.id = bun.box_id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.status = 'active'
[2025-06-07 15:20:12] [info] Database connected successfully
[2025-06-07 15:20:22] [info] Database connected successfully
[2025-06-07 15:20:22] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.capacity_percentage' in 'field list' SQL: SELECT 
                COUNT(DISTINCT w.id) as total_warehouses,
                COUNT(DISTINCT b.id) as total_boxes,
                COUNT(DISTINCT CASE WHEN b.status = 'occupied' THEN b.id END) as occupied_boxes,
                COUNT(DISTINCT bun.id) as total_bundles,
                COUNT(DISTINCT d.id) as total_documents,
                ROUND(AVG(w.capacity_percentage), 2) as avg_capacity_usage,
                COUNT(DISTINCT CASE WHEN w.status = 'active' THEN w.id END) as active_warehouses
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             LEFT JOIN bundles bun ON b.id = bun.box_id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.status = 'active'
[2025-06-07 15:20:22] [info] Database connected successfully
[2025-06-07 15:20:31] [info] Database connected successfully
[2025-06-07 15:20:31] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.capacity_percentage' in 'field list' SQL: SELECT 
                COUNT(DISTINCT w.id) as total_warehouses,
                COUNT(DISTINCT b.id) as total_boxes,
                COUNT(DISTINCT CASE WHEN b.status = 'occupied' THEN b.id END) as occupied_boxes,
                COUNT(DISTINCT bun.id) as total_bundles,
                COUNT(DISTINCT d.id) as total_documents,
                ROUND(AVG(w.capacity_percentage), 2) as avg_capacity_usage,
                COUNT(DISTINCT CASE WHEN w.status = 'active' THEN w.id END) as active_warehouses
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             LEFT JOIN bundles bun ON b.id = bun.box_id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.status = 'active'
[2025-06-07 15:20:31] [info] Database connected successfully
[2025-06-07 15:20:40] [info] Database connected successfully
[2025-06-07 15:20:40] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.capacity_percentage' in 'field list' SQL: SELECT 
                COUNT(DISTINCT w.id) as total_warehouses,
                COUNT(DISTINCT b.id) as total_boxes,
                COUNT(DISTINCT CASE WHEN b.status = 'occupied' THEN b.id END) as occupied_boxes,
                COUNT(DISTINCT bun.id) as total_bundles,
                COUNT(DISTINCT d.id) as total_documents,
                ROUND(AVG(w.capacity_percentage), 2) as avg_capacity_usage,
                COUNT(DISTINCT CASE WHEN w.status = 'active' THEN w.id END) as active_warehouses
             FROM warehouses w
             LEFT JOIN boxes b ON w.id = b.warehouse_id
             LEFT JOIN bundles bun ON b.id = bun.box_id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             WHERE w.status = 'active'
[2025-06-07 15:20:40] [info] Database connected successfully
[2025-06-07 15:21:58] [info] Database connected successfully
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 15:21:58] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 15:22:40] [info] Database connected successfully
[2025-06-07 15:23:34] [info] Database connected successfully
[2025-06-07 15:23:41] [info] Database connected successfully
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 15:23:41] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 15:24:05] [info] Database connected successfully
[2025-06-07 15:24:15] [info] Database connected successfully
[2025-06-07 15:24:48] [info] Database connected successfully
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 15:24:48] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 15:25:21] [info] Database connected successfully
[2025-06-07 15:25:41] [info] Database connected successfully
[2025-06-07 15:27:39] [info] Database connected successfully
[2025-06-07 15:27:44] [info] Database connected successfully
[2025-06-07 15:27:44] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.box_movements' doesn't exist SQL: SELECT 
                bm.*,
                b.box_id,
                b.name as box_name,
                u.first_name,
                u.last_name,
                w.name as warehouse_name
             FROM box_movements bm
             JOIN boxes b ON bm.box_id = b.id
             JOIN users u ON bm.moved_by = u.id
             JOIN warehouses w ON b.warehouse_id = w.id
             ORDER BY bm.created_at DESC
             LIMIT 20
[2025-06-07 15:27:44] [info] Database connected successfully
[2025-06-07 15:28:02] [info] Database connected successfully
[2025-06-07 15:28:02] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.box_movements' doesn't exist SQL: SELECT 
                bm.*,
                b.box_id,
                b.name as box_name,
                u.first_name,
                u.last_name,
                w.name as warehouse_name
             FROM box_movements bm
             JOIN boxes b ON bm.box_id = b.id
             JOIN users u ON bm.moved_by = u.id
             JOIN warehouses w ON b.warehouse_id = w.id
             ORDER BY bm.created_at DESC
             LIMIT 20
[2025-06-07 15:28:02] [info] Database connected successfully
[2025-06-07 15:28:36] [info] Database connected successfully
[2025-06-07 15:29:17] [info] Database connected successfully
[2025-06-07 15:30:54] [info] Database connected successfully
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 15:30:54] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 15:30:55] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 15:30:55] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 15:30:55] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 15:30:55] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 15:30:55] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 15:30:55] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 15:31:36] [info] Database connected successfully
[2025-06-07 15:33:03] [info] Database connected successfully
[2025-06-07 15:33:06] [info] Database connected successfully
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 15:33:06] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 15:33:37] [info] Database connected successfully
[2025-06-07 15:33:49] [info] Database connected successfully
[2025-06-07 15:34:12] [info] Database connected successfully
[2025-06-07 15:34:15] [info] Database connected successfully
[2025-06-07 15:34:18] [info] Database connected successfully
[2025-06-07 15:34:35] [info] Database connected successfully
[2025-06-07 15:34:35] [info] Database connected successfully
[2025-06-07 15:34:36] [info] Database connected successfully
[2025-06-07 15:34:36] [info] Database connected successfully
[2025-06-07 15:34:39] [info] Database connected successfully
[2025-06-07 15:34:39] [info] Database connected successfully
[2025-06-07 15:34:40] [info] Database connected successfully
[2025-06-07 15:34:40] [info] Database connected successfully
[2025-06-07 15:34:42] [info] Database connected successfully
[2025-06-07 15:34:42] [info] Database connected successfully
[2025-06-07 15:34:43] [info] Database connected successfully
[2025-06-07 15:34:43] [info] Database connected successfully
[2025-06-07 15:34:45] [info] Database connected successfully
[2025-06-07 15:34:45] [info] Database connected successfully
[2025-06-07 15:35:07] [info] Database connected successfully
[2025-06-07 15:35:07] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.last_scanned_at' in 'field list' SQL: SELECT 
                COUNT(DISTINCT b.id) as total_barcodes,
                COUNT(DISTINCT CASE WHEN b.is_active = 1 THEN b.id END) as active_barcodes,
                COUNT(DISTINCT CASE WHEN b.last_scanned_at IS NOT NULL THEN b.id END) as scanned_barcodes,
                COUNT(DISTINCT CASE WHEN DATE(b.generated_at) = CURDATE() THEN b.id END) as generated_today,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'box' THEN b.id END) as box_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'bundle' THEN b.id END) as bundle_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'document' THEN b.id END) as document_barcodes,
                SUM(b.scan_count) as total_scans,
                AVG(b.scan_count) as avg_scans_per_barcode
             FROM barcodes b
             WHERE b.is_active = 1
[2025-06-07 15:35:07] [info] Database connected successfully
[2025-06-07 15:35:15] [info] Database connected successfully
[2025-06-07 15:35:15] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.retention_policies' doesn't exist SQL: SELECT 
                COUNT(DISTINCT rp.id) as total_policies,
                COUNT(DISTINCT ds.id) as scheduled_destructions,
                COUNT(DISTINCT lh.id) as active_legal_holds,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN ds.id END) as destructions_next_30_days,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN ds.id END) as destructions_next_7_days,
                COUNT(DISTINCT d.id) as documents_under_hold,
                COUNT(DISTINCT CASE WHEN ds.status = 'overdue' THEN ds.id END) as overdue_destructions
             FROM retention_policies rp
             LEFT JOIN destruction_schedules ds ON ds.status IN ('scheduled', 'overdue')
             LEFT JOIN legal_holds lh ON lh.status = 'active'
             LEFT JOIN legal_hold_documents lhd ON lh.id = lhd.legal_hold_id
             LEFT JOIN documents d ON lhd.document_id = d.id
[2025-06-07 15:35:15] [info] Database connected successfully
[2025-06-07 15:35:17] [info] Database connected successfully
[2025-06-07 15:35:27] [info] Database connected successfully
[2025-06-07 15:35:32] [info] Database connected successfully
[2025-06-07 15:35:32] [info] Database connected successfully
[2025-06-07 15:35:54] [info] Database connected successfully
[2025-06-07 15:35:54] [info] Database connected successfully
[2025-06-07 15:36:03] [info] Database connected successfully
[2025-06-07 15:36:18] [info] Database connected successfully
[2025-06-07 15:36:28] [info] Database connected successfully
[2025-06-07 15:36:31] [info] Database connected successfully
[2025-06-07 15:36:33] [info] Database connected successfully
[2025-06-07 15:36:34] [info] Database connected successfully
[2025-06-07 15:36:36] [info] Database connected successfully
[2025-06-07 15:36:40] [info] Database connected successfully
[2025-06-07 15:38:38] [info] Database connected successfully
[2025-06-07 15:39:20] [info] Database connected successfully
[2025-06-07 15:40:33] [info] Database connected successfully
[2025-06-07 15:40:55] [info] Database connected successfully
[2025-06-07 15:40:58] [info] Database connected successfully
[2025-06-07 15:41:00] [info] Database connected successfully
[2025-06-07 15:41:02] [info] Database connected successfully
[2025-06-07 15:41:04] [info] Database connected successfully
[2025-06-07 15:41:05] [info] Database connected successfully
[2025-06-07 15:41:08] [info] Database connected successfully
[2025-06-07 15:41:10] [info] Database connected successfully
[2025-06-07 15:42:09] [info] Database connected successfully
[2025-06-07 15:42:12] [info] Database connected successfully
[2025-06-07 15:42:14] [info] Database connected successfully
[2025-06-07 15:42:17] [info] Database connected successfully
[2025-06-07 15:42:19] [info] Database connected successfully
[2025-06-07 15:42:37] [info] Database connected successfully
[2025-06-07 15:42:42] [info] Database connected successfully
[2025-06-07 15:42:42] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.last_scanned_at' in 'field list' SQL: SELECT 
                COUNT(DISTINCT b.id) as total_barcodes,
                COUNT(DISTINCT CASE WHEN b.is_active = 1 THEN b.id END) as active_barcodes,
                COUNT(DISTINCT CASE WHEN b.last_scanned_at IS NOT NULL THEN b.id END) as scanned_barcodes,
                COUNT(DISTINCT CASE WHEN DATE(b.generated_at) = CURDATE() THEN b.id END) as generated_today,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'box' THEN b.id END) as box_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'bundle' THEN b.id END) as bundle_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'document' THEN b.id END) as document_barcodes,
                SUM(b.scan_count) as total_scans,
                AVG(b.scan_count) as avg_scans_per_barcode
             FROM barcodes b
             WHERE b.is_active = 1
[2025-06-07 15:42:42] [info] Database connected successfully
[2025-06-07 15:42:47] [info] Database connected successfully
[2025-06-07 15:42:49] [info] Database connected successfully
[2025-06-07 15:42:49] [info] Database connected successfully
[2025-06-07 15:42:51] [info] Database connected successfully
[2025-06-07 15:42:51] [info] Database connected successfully
[2025-06-07 15:43:39] [info] Database connected successfully
[2025-06-07 15:44:21] [info] Database connected successfully
[2025-06-07 15:44:52] [info] Database connected successfully
[2025-06-07 15:46:53] [info] Database connected successfully
[2025-06-07 15:48:40] [info] Database connected successfully
[2025-06-07 15:48:54] [info] Database connected successfully
[2025-06-07 15:49:22] [info] Database connected successfully
[2025-06-07 15:50:55] [info] Database connected successfully
[2025-06-07 15:52:56] [info] Database connected successfully
[2025-06-07 15:53:41] [info] Database connected successfully
[2025-06-07 15:54:23] [info] Database connected successfully
[2025-06-07 15:54:57] [info] Database connected successfully
[2025-06-07 15:56:58] [info] Database connected successfully
[2025-06-07 15:58:42] [info] Database connected successfully
[2025-06-07 15:58:59] [info] Database connected successfully
[2025-06-07 15:59:24] [info] Database connected successfully
[2025-06-07 16:01:00] [info] Database connected successfully
[2025-06-07 16:03:01] [info] Database connected successfully
[2025-06-07 16:03:43] [info] Database connected successfully
[2025-06-07 16:04:09] [info] Database connected successfully
[2025-06-07 16:04:09] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.last_scanned_at' in 'field list' SQL: SELECT 
                COUNT(DISTINCT b.id) as total_barcodes,
                COUNT(DISTINCT CASE WHEN b.is_active = 1 THEN b.id END) as active_barcodes,
                COUNT(DISTINCT CASE WHEN b.last_scanned_at IS NOT NULL THEN b.id END) as scanned_barcodes,
                COUNT(DISTINCT CASE WHEN DATE(b.generated_at) = CURDATE() THEN b.id END) as generated_today,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'box' THEN b.id END) as box_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'bundle' THEN b.id END) as bundle_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'document' THEN b.id END) as document_barcodes,
                SUM(b.scan_count) as total_scans,
                AVG(b.scan_count) as avg_scans_per_barcode
             FROM barcodes b
             WHERE b.is_active = 1
[2025-06-07 16:04:09] [info] Database connected successfully
[2025-06-07 16:04:14] [info] Database connected successfully
[2025-06-07 16:04:16] [info] Database connected successfully
[2025-06-07 16:04:19] [info] Database connected successfully
[2025-06-07 16:04:22] [info] Database connected successfully
[2025-06-07 16:04:22] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.last_scanned_at' in 'field list' SQL: SELECT 
                COUNT(DISTINCT b.id) as total_barcodes,
                COUNT(DISTINCT CASE WHEN b.is_active = 1 THEN b.id END) as active_barcodes,
                COUNT(DISTINCT CASE WHEN b.last_scanned_at IS NOT NULL THEN b.id END) as scanned_barcodes,
                COUNT(DISTINCT CASE WHEN DATE(b.generated_at) = CURDATE() THEN b.id END) as generated_today,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'box' THEN b.id END) as box_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'bundle' THEN b.id END) as bundle_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'document' THEN b.id END) as document_barcodes,
                SUM(b.scan_count) as total_scans,
                AVG(b.scan_count) as avg_scans_per_barcode
             FROM barcodes b
             WHERE b.is_active = 1
[2025-06-07 16:04:22] [info] Database connected successfully
[2025-06-07 16:04:25] [info] Database connected successfully
[2025-06-07 16:04:33] [info] Database connected successfully
[2025-06-07 16:04:33] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.last_scanned_at' in 'field list' SQL: SELECT 
                COUNT(DISTINCT b.id) as total_barcodes,
                COUNT(DISTINCT CASE WHEN b.is_active = 1 THEN b.id END) as active_barcodes,
                COUNT(DISTINCT CASE WHEN b.last_scanned_at IS NOT NULL THEN b.id END) as scanned_barcodes,
                COUNT(DISTINCT CASE WHEN DATE(b.generated_at) = CURDATE() THEN b.id END) as generated_today,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'box' THEN b.id END) as box_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'bundle' THEN b.id END) as bundle_barcodes,
                COUNT(DISTINCT CASE WHEN b.entity_type = 'document' THEN b.id END) as document_barcodes,
                SUM(b.scan_count) as total_scans,
                AVG(b.scan_count) as avg_scans_per_barcode
             FROM barcodes b
             WHERE b.is_active = 1
[2025-06-07 16:04:33] [info] Database connected successfully
[2025-06-07 16:06:59] [info] Database connected successfully
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 16:06:59] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 16:07:49] [info] Database connected successfully
[2025-06-07 16:07:49] [error] Query failed: SQLSTATE[HY000]: General error: 4074 Window functions can not be used as arguments to group functions. SQL: SELECT 
            COUNT(DISTINCT scanned_by) as active_scanners,
            COUNT(*) as total_scans_today,
            AVG(TIMESTAMPDIFF(SECOND, LAG(created_at) OVER (PARTITION BY scanned_by ORDER BY created_at), created_at)) as avg_scan_interval
         FROM barcode_audit_trail 
         WHERE DATE(created_at) = CURDATE() AND action = 'scanned'
[2025-06-07 16:08:17] [info] Database connected successfully
[2025-06-07 16:08:39] [info] Database connected successfully
[2025-06-07 16:08:39] [info] Database connected successfully
[2025-06-07 16:08:44] [info] Database connected successfully
[2025-06-07 16:08:46] [info] Database connected successfully
[2025-06-07 16:08:46] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.retention_policies' doesn't exist SQL: SELECT 
                COUNT(DISTINCT rp.id) as total_policies,
                COUNT(DISTINCT ds.id) as scheduled_destructions,
                COUNT(DISTINCT lh.id) as active_legal_holds,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN ds.id END) as destructions_next_30_days,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN ds.id END) as destructions_next_7_days,
                COUNT(DISTINCT d.id) as documents_under_hold,
                COUNT(DISTINCT CASE WHEN ds.status = 'overdue' THEN ds.id END) as overdue_destructions
             FROM retention_policies rp
             LEFT JOIN destruction_schedules ds ON ds.status IN ('scheduled', 'overdue')
             LEFT JOIN legal_holds lh ON lh.status = 'active'
             LEFT JOIN legal_hold_documents lhd ON lh.id = lhd.legal_hold_id
             LEFT JOIN documents d ON lhd.document_id = d.id
[2025-06-07 16:08:46] [info] Database connected successfully
[2025-06-07 16:08:59] [info] Database connected successfully
[2025-06-07 16:09:00] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?, ?, 'qr', 1, 1, NOW(), ?)' at line 2 SQL: INSERT IGNORE INTO barcodes (entity_type, entity_id, barcode_value, barcode_type, is_active, generated_by, generated_at, scan_count) VALUES
                ('bundle', ?, ?, 'qr', 1, 1, NOW(), ?)
[2025-06-07 16:09:46] [info] Database connected successfully
[2025-06-07 16:10:41] [info] Database connected successfully
[2025-06-07 16:10:54] [info] Database connected successfully
[2025-06-07 16:11:20] [info] Database connected successfully
[2025-06-07 16:11:38] [info] Database connected successfully
[2025-06-07 16:11:38] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.retention_policies' doesn't exist SQL: SELECT 
                COUNT(DISTINCT rp.id) as total_policies,
                COUNT(DISTINCT ds.id) as scheduled_destructions,
                COUNT(DISTINCT lh.id) as active_legal_holds,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN ds.id END) as destructions_next_30_days,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN ds.id END) as destructions_next_7_days,
                COUNT(DISTINCT d.id) as documents_under_hold,
                COUNT(DISTINCT CASE WHEN ds.status = 'overdue' THEN ds.id END) as overdue_destructions
             FROM retention_policies rp
             LEFT JOIN destruction_schedules ds ON ds.status IN ('scheduled', 'overdue')
             LEFT JOIN legal_holds lh ON lh.status = 'active'
             LEFT JOIN legal_hold_documents lhd ON lh.id = lhd.legal_hold_id
             LEFT JOIN documents d ON lhd.document_id = d.id
[2025-06-07 16:11:38] [info] Database connected successfully
[2025-06-07 16:11:47] [info] Database connected successfully
[2025-06-07 16:11:47] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.retention_policies' doesn't exist SQL: SELECT 
                COUNT(DISTINCT rp.id) as total_policies,
                COUNT(DISTINCT ds.id) as scheduled_destructions,
                COUNT(DISTINCT lh.id) as active_legal_holds,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN ds.id END) as destructions_next_30_days,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN ds.id END) as destructions_next_7_days,
                COUNT(DISTINCT d.id) as documents_under_hold,
                COUNT(DISTINCT CASE WHEN ds.status = 'overdue' THEN ds.id END) as overdue_destructions
             FROM retention_policies rp
             LEFT JOIN destruction_schedules ds ON ds.status IN ('scheduled', 'overdue')
             LEFT JOIN legal_holds lh ON lh.status = 'active'
             LEFT JOIN legal_hold_documents lhd ON lh.id = lhd.legal_hold_id
             LEFT JOIN documents d ON lhd.document_id = d.id
[2025-06-07 16:11:47] [info] Database connected successfully
[2025-06-07 16:11:52] [info] Database connected successfully
[2025-06-07 16:11:52] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.retention_policies' doesn't exist SQL: SELECT 
                COUNT(DISTINCT rp.id) as total_policies,
                COUNT(DISTINCT ds.id) as scheduled_destructions,
                COUNT(DISTINCT lh.id) as active_legal_holds,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN ds.id END) as destructions_next_30_days,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN ds.id END) as destructions_next_7_days,
                COUNT(DISTINCT d.id) as documents_under_hold,
                COUNT(DISTINCT CASE WHEN ds.status = 'overdue' THEN ds.id END) as overdue_destructions
             FROM retention_policies rp
             LEFT JOIN destruction_schedules ds ON ds.status IN ('scheduled', 'overdue')
             LEFT JOIN legal_holds lh ON lh.status = 'active'
             LEFT JOIN legal_hold_documents lhd ON lh.id = lhd.legal_hold_id
             LEFT JOIN documents d ON lhd.document_id = d.id
[2025-06-07 16:11:52] [info] Database connected successfully
[2025-06-07 16:12:14] [info] Database connected successfully
[2025-06-07 16:13:46] [info] Database connected successfully
[2025-06-07 16:14:27] [info] Database connected successfully
[2025-06-07 16:14:30] [info] Database connected successfully
[2025-06-07 16:14:30] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.retention_policies' doesn't exist SQL: SELECT 
                COUNT(DISTINCT rp.id) as total_policies,
                COUNT(DISTINCT ds.id) as scheduled_destructions,
                COUNT(DISTINCT lh.id) as active_legal_holds,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN ds.id END) as destructions_next_30_days,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN ds.id END) as destructions_next_7_days,
                COUNT(DISTINCT d.id) as documents_under_hold,
                COUNT(DISTINCT CASE WHEN ds.status = 'overdue' THEN ds.id END) as overdue_destructions
             FROM retention_policies rp
             LEFT JOIN destruction_schedules ds ON ds.status IN ('scheduled', 'overdue')
             LEFT JOIN legal_holds lh ON lh.status = 'active'
             LEFT JOIN legal_hold_documents lhd ON lh.id = lhd.legal_hold_id
             LEFT JOIN documents d ON lhd.document_id = d.id
[2025-06-07 16:14:31] [info] Database connected successfully
[2025-06-07 16:14:47] [info] Database connected successfully
[2025-06-07 16:14:47] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.retention_policies' doesn't exist SQL: SELECT 
                COUNT(DISTINCT rp.id) as total_policies,
                COUNT(DISTINCT ds.id) as scheduled_destructions,
                COUNT(DISTINCT lh.id) as active_legal_holds,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN ds.id END) as destructions_next_30_days,
                COUNT(DISTINCT CASE WHEN ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN ds.id END) as destructions_next_7_days,
                COUNT(DISTINCT d.id) as documents_under_hold,
                COUNT(DISTINCT CASE WHEN ds.status = 'overdue' THEN ds.id END) as overdue_destructions
             FROM retention_policies rp
             LEFT JOIN destruction_schedules ds ON ds.status IN ('scheduled', 'overdue')
             LEFT JOIN legal_holds lh ON lh.status = 'active'
             LEFT JOIN legal_hold_documents lhd ON lh.id = lhd.legal_hold_id
             LEFT JOIN documents d ON lhd.document_id = d.id
[2025-06-07 16:14:47] [info] Database connected successfully
[2025-06-07 16:18:07] [info] Database connected successfully
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 16:18:07] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_scanned_at' SQL: ALTER TABLE barcodes ADD COLUMN last_scanned_at TIMESTAMP NULL COMMENT 'Last time this barcode was scanned'
[2025-06-07 16:18:08] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_image_path' SQL: ALTER TABLE barcodes ADD COLUMN barcode_image_path VARCHAR(500) NULL COMMENT 'Path to generated barcode image'
[2025-06-07 16:18:47] [info] Database connected successfully
[2025-06-07 16:18:54] [info] Database connected successfully
[2025-06-07 16:18:54] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.reference_number' in 'field list' SQL: SELECT 
            ds.*,
            d.title as document_title,
            d.reference_number,
            c.name as company_name,
            rp.document_type,
            rp.destruction_method
         FROM destruction_schedules ds
         JOIN documents d ON ds.document_id = d.id
         JOIN companies c ON d.company_id = c.id
         LEFT JOIN retention_policies rp ON d.document_type = rp.document_type
         WHERE ds.status = 'scheduled' 
         AND ds.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
         ORDER BY ds.scheduled_date ASC
         LIMIT 20
[2025-06-07 16:18:54] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.reference_number' in 'field list' SQL: SELECT 
            ds.*,
            d.title as document_title,
            d.reference_number
         FROM destruction_schedules ds
         LEFT JOIN documents d ON ds.document_id = d.id
         WHERE ds.status = 'scheduled'
         ORDER BY ds.scheduled_date ASC
[2025-06-07 16:19:48] [info] Database connected successfully
[2025-06-07 16:20:28] [info] Database connected successfully
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 16:20:28] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 16:20:29] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_scanned_at' SQL: ALTER TABLE barcodes ADD COLUMN last_scanned_at TIMESTAMP NULL COMMENT 'Last time this barcode was scanned'
[2025-06-07 16:20:29] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_image_path' SQL: ALTER TABLE barcodes ADD COLUMN barcode_image_path VARCHAR(500) NULL COMMENT 'Path to generated barcode image'
[2025-06-07 16:20:45] [info] Database connected successfully
[2025-06-07 16:21:03] [info] Database connected successfully
[2025-06-07 16:21:59] [info] Database connected successfully
[2025-06-07 16:22:38] [info] Database connected successfully
[2025-06-07 16:22:43] [info] Database connected successfully
[2025-06-07 16:23:36] [info] Database connected successfully
[2025-06-07 16:23:48] [info] Database connected successfully
[2025-06-07 16:27:44] [info] Database connected successfully
[2025-06-07 16:28:49] [info] Database connected successfully
[2025-06-07 16:32:45] [info] Database connected successfully
[2025-06-07 16:33:50] [info] Database connected successfully
[2025-06-07 16:37:46] [info] Database connected successfully
[2025-06-07 16:38:51] [info] Database connected successfully
[2025-06-07 16:42:47] [info] Database connected successfully
[2025-06-07 16:43:52] [info] Database connected successfully
[2025-06-07 16:47:48] [info] Database connected successfully
[2025-06-07 16:48:53] [info] Database connected successfully
[2025-06-07 16:52:49] [info] Database connected successfully
[2025-06-07 16:53:54] [info] Database connected successfully
[2025-06-07 18:36:22] [info] Database connected successfully
[2025-06-07 18:36:22] [info] Database connected successfully
[2025-06-07 18:37:00] [info] Database connected successfully
[2025-06-07 18:39:11] [info] Database connected successfully
[2025-06-07 18:39:20] [info] Database connected successfully
[2025-06-07 18:39:32] [info] Database connected successfully
[2025-06-07 18:41:25] [info] Database connected successfully
[2025-06-07 18:46:26] [info] Database connected successfully
[2025-06-07 18:47:59] [info] Database connected successfully
[2025-06-07 18:48:55] [info] Database connected successfully
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_scanned_at' SQL: ALTER TABLE barcodes ADD COLUMN last_scanned_at TIMESTAMP NULL COMMENT 'Last time this barcode was scanned'
[2025-06-07 18:48:56] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_image_path' SQL: ALTER TABLE barcodes ADD COLUMN barcode_image_path VARCHAR(500) NULL COMMENT 'Path to generated barcode image'
[2025-06-07 18:48:57] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE documents ADD COLUMN reference_number VARCHAR(100) NULL COMMENT 'Document reference number for tracking'
[2025-06-07 18:48:57] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE documents ADD COLUMN box_id INT NULL COMMENT 'Physical box where document is stored'
[2025-06-07 18:48:57] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'warehouse_id' SQL: ALTER TABLE documents ADD COLUMN warehouse_id INT NULL COMMENT 'Warehouse where document is stored'
[2025-06-07 18:51:16] [info] Database connected successfully
[2025-06-07 18:51:28] [info] Database connected successfully
[2025-06-07 18:51:54] [info] Database connected successfully
[2025-06-07 18:52:18] [info] Database connected successfully
[2025-06-07 18:52:22] [info] Database connected successfully
[2025-06-07 18:53:14] [info] Database connected successfully
[2025-06-07 18:54:59] [info] Database connected successfully
[2025-06-07 18:54:59] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'client_name' cannot be null SQL: INSERT INTO document_intake (
                    company_id, reference_number, client_name, client_id, source, document_type,
                    description, priority, expected_count, date_range_start, date_range_end,
                    sensitivity_level, department, notes, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())
[2025-06-07 18:54:59] [info] Database connected successfully
[2025-06-07 18:56:03] [info] Database connected successfully
[2025-06-07 18:56:03] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'client_name' cannot be null SQL: INSERT INTO document_intake (
                    company_id, reference_number, client_name, client_id, source, document_type,
                    description, priority, expected_count, date_range_start, date_range_end,
                    sensitivity_level, department, notes, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())
[2025-06-07 18:56:03] [info] Database connected successfully
[2025-06-07 18:56:30] [info] Database connected successfully
[2025-06-07 19:00:06] [info] Database connected successfully
[2025-06-07 19:01:31] [info] Database connected successfully
[2025-06-07 19:01:56] [info] Database connected successfully
[2025-06-07 19:02:46] [info] Database connected successfully
[2025-06-07 19:03:27] [info] Database connected successfully
[2025-06-07 19:04:31] [info] Database connected successfully
[2025-06-07 19:04:31] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'source' at row 1 SQL: INSERT INTO document_intake (
                    company_id, client_name, client_id, source, document_type,
                    description, priority, expected_count, date_range_start, date_range_end,
                    sensitivity_level, department, notes, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())
[2025-06-07 19:04:31] [info] Database connected successfully
[2025-06-07 19:05:20] [info] Database connected successfully
[2025-06-07 19:05:52] [info] Database connected successfully
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE document_intake ADD COLUMN box_id INT NULL AFTER bundle_id
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_scanned_at' SQL: ALTER TABLE barcodes ADD COLUMN last_scanned_at TIMESTAMP NULL COMMENT 'Last time this barcode was scanned'
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_image_path' SQL: ALTER TABLE barcodes ADD COLUMN barcode_image_path VARCHAR(500) NULL COMMENT 'Path to generated barcode image'
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE documents ADD COLUMN reference_number VARCHAR(100) NULL COMMENT 'Document reference number for tracking'
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_id' SQL: ALTER TABLE documents ADD COLUMN box_id INT NULL COMMENT 'Physical box where document is stored'
[2025-06-07 19:05:52] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'warehouse_id' SQL: ALTER TABLE documents ADD COLUMN warehouse_id INT NULL COMMENT 'Warehouse where document is stored'
[2025-06-07 19:06:31] [info] Database connected successfully
[2025-06-07 19:06:37] [info] Database connected successfully
[2025-06-07 19:06:56] [info] Database connected successfully
[2025-06-07 19:08:41] [info] Database connected successfully
[2025-06-07 19:09:05] [info] Database connected successfully
[2025-06-07 19:10:05] [info] Database connected successfully
[2025-06-07 19:10:05] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: INSERT INTO activity_logs (user_id, company_id, action, entity_type, entity_id, description, ip_address, created_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
[2025-06-07 19:10:05] [info] Database connected successfully
[2025-06-07 19:10:29] [info] Database connected successfully
[2025-06-07 19:11:12] [info] Database connected successfully
[2025-06-07 19:11:22] [info] Database connected successfully
[2025-06-07 19:11:38] [info] Database connected successfully
[2025-06-07 19:11:39] [info] Database connected successfully
[2025-06-07 19:11:46] [info] Database connected successfully
[2025-06-07 19:11:52] [info] Database connected successfully
[2025-06-07 19:12:13] [info] Database connected successfully
[2025-06-07 19:12:58] [info] Database connected successfully
[2025-06-07 19:13:05] [info] Database connected successfully
[2025-06-07 19:13:10] [info] Database connected successfully
[2025-06-07 19:14:00] [info] Database connected successfully
[2025-06-07 19:14:44] [info] Database connected successfully
[2025-06-07 19:14:51] [info] Database connected successfully
[2025-06-07 19:15:08] [info] Database connected successfully
[2025-06-07 19:15:12] [info] Database connected successfully
[2025-06-07 19:15:34] [info] Database connected successfully
[2025-06-07 19:16:20] [info] Database connected successfully
[2025-06-07 19:16:23] [info] Database connected successfully
[2025-06-07 19:16:41] [info] Database connected successfully
[2025-06-07 19:16:57] [info] Database connected successfully
[2025-06-07 19:16:57] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 19:16:57] [info] Database connected successfully
[2025-06-07 19:17:12] [info] Database connected successfully
[2025-06-07 19:17:16] [info] Database connected successfully
[2025-06-07 19:17:19] [info] Database connected successfully
[2025-06-07 19:17:47] [info] Database connected successfully
[2025-06-07 19:17:48] [info] Database connected successfully
[2025-06-07 19:17:50] [info] Database connected successfully
[2025-06-07 19:17:51] [info] Database connected successfully
[2025-06-07 19:17:51] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 19:17:51] [info] Database connected successfully
[2025-06-07 19:17:54] [info] Database connected successfully
[2025-06-07 19:17:54] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 19:17:55] [info] Database connected successfully
[2025-06-07 19:18:02] [info] Database connected successfully
[2025-06-07 19:18:02] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 19:18:02] [info] Database connected successfully
[2025-06-07 19:18:31] [info] Database connected successfully
[2025-06-07 19:18:56] [info] Database connected successfully
[2025-06-07 19:21:43] [info] Database connected successfully
[2025-06-07 19:26:07] [info] Database connected successfully
[2025-06-07 19:26:45] [info] Database connected successfully
[2025-06-07 19:29:18] [info] Database connected successfully
[2025-06-07 19:29:23] [info] Database connected successfully
[2025-06-07 19:29:46] [info] Database connected successfully
[2025-06-07 19:29:51] [info] Database connected successfully
[2025-06-07 19:29:56] [info] Database connected successfully
[2025-06-07 19:30:44] [info] Database connected successfully
[2025-06-07 19:30:44] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO bundles (
                        company_id, name, description, reference_number, category, priority,
                        retention_period, access_level, document_type, year, department,
                        status, created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
[2025-06-07 19:30:44] [info] Database connected successfully
[2025-06-07 19:31:47] [info] Database connected successfully
[2025-06-07 19:36:49] [info] Database connected successfully
[2025-06-07 19:41:38] [info] Database connected successfully
[2025-06-07 19:41:38] [info] Database connected successfully
[2025-06-07 19:41:51] [info] Database connected successfully
[2025-06-07 19:43:31] [info] Database connected successfully
[2025-06-07 19:44:55] [info] Database connected successfully
[2025-06-07 19:44:55] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO bundles (
                        company_id, name, description, reference_number, category, priority,
                        retention_period, access_level, document_type, year, department,
                        status, created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
[2025-06-07 19:44:55] [info] Database connected successfully
[2025-06-07 19:45:59] [info] Database connected successfully
[2025-06-07 19:45:59] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')
[2025-06-07 19:45:59] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
[2025-06-07 19:46:13] [info] Database connected successfully
[2025-06-07 19:46:29] [info] Database connected successfully
[2025-06-07 19:46:33] [info] Database connected successfully
[2025-06-07 19:46:53] [info] Database connected successfully
[2025-06-07 19:47:21] [info] Database connected successfully
[2025-06-07 19:51:13] [info] Database connected successfully
[2025-06-07 19:51:27] [info] Database connected successfully
[2025-06-07 19:51:55] [info] Database connected successfully
[2025-06-07 19:54:56] [info] Database connected successfully
[2025-06-07 19:54:58] [info] Database connected successfully
[2025-06-07 19:55:09] [info] Database connected successfully
[2025-06-07 19:55:09] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: INSERT INTO bundles (company_id, name, description, reference_number, category, priority, retention_period, access_level, status, created_by, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
[2025-06-07 19:55:09] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO bundles (company_id, name, description, reference_number, category, priority, retention_period, access_level, status, created_by, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
[2025-06-07 19:56:12] [info] Database connected successfully
[2025-06-07 19:56:12] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: INSERT INTO bundles (company_id, name, description, reference_number, category, priority, retention_period, access_level, status, created_by, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
[2025-06-07 19:56:12] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO bundles (company_id, name, description, reference_number, category, priority, retention_period, access_level, status, created_by, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
[2025-06-07 19:56:56] [info] Database connected successfully
[2025-06-07 19:57:01] [info] Database connected successfully
[2025-06-07 19:58:34] [info] Database connected successfully
[2025-06-07 19:58:34] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO bundles (company_id, name, description, reference_number, category, priority, retention_period, access_level, status, created_by, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
[2025-06-07 20:00:13] [info] Database connected successfully
[2025-06-07 20:00:24] [info] Database connected successfully
[2025-06-07 20:00:47] [info] Database connected successfully
[2025-06-07 20:01:04] [info] Database connected successfully
[2025-06-07 20:01:51] [info] Database connected successfully
[2025-06-07 20:01:51] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')
[2025-06-07 20:01:51] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
[2025-06-07 20:01:59] [info] Database connected successfully
[2025-06-07 20:02:03] [info] Database connected successfully
[2025-06-07 20:02:03] [info] Database connected successfully
[2025-06-07 20:02:21] [info] Database connected successfully
[2025-06-07 20:02:21] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')
[2025-06-07 20:02:21] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
[2025-06-07 20:02:28] [info] Database connected successfully
[2025-06-07 20:02:28] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')
[2025-06-07 20:02:28] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
[2025-06-07 20:02:40] [info] Database connected successfully
[2025-06-07 20:02:40] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')
[2025-06-07 20:02:40] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
[2025-06-07 20:02:47] [info] Database connected successfully
[2025-06-07 20:02:47] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')
[2025-06-07 20:02:47] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
[2025-06-07 20:03:11] [info] Database connected successfully
[2025-06-07 20:03:11] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')
[2025-06-07 20:03:11] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
[2025-06-07 20:03:26] [info] Database connected successfully
[2025-06-07 20:03:26] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, status)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 'active')
[2025-06-07 20:03:26] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: INSERT INTO categories (company_id, name, description, color, created_by, created_at, updated_at, is_active)
                     VALUES (?, ?, ?, ?, ?, NOW(), NOW(), 1)
[2025-06-07 20:05:10] [info] Database connected successfully
[2025-06-07 20:05:11] [info] Database connected successfully
[2025-06-07 20:07:01] [info] Database connected successfully
[2025-06-07 20:12:02] [info] Database connected successfully
[2025-06-07 20:15:14] [info] Database connected successfully
[2025-06-07 20:15:34] [info] Database connected successfully
[2025-06-07 20:15:34] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: INSERT INTO activity_logs (user_id, company_id, action, entity_type, entity_id, description, ip_address, created_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
[2025-06-07 20:15:52] [info] Database connected successfully
[2025-06-07 20:15:58] [info] Database connected successfully
[2025-06-07 20:16:23] [info] Database connected successfully
[2025-06-07 20:16:29] [info] Database connected successfully
[2025-06-07 20:16:33] [info] Database connected successfully
[2025-06-07 20:16:38] [info] Database connected successfully
[2025-06-07 20:16:49] [info] Database connected successfully
[2025-06-07 20:16:55] [info] Database connected successfully
[2025-06-07 20:17:00] [info] Database connected successfully
[2025-06-07 20:17:04] [info] Database connected successfully
[2025-06-07 20:17:18] [info] Database connected successfully
[2025-06-07 20:21:24] [info] Database connected successfully
[2025-06-07 20:22:06] [info] Database connected successfully
[2025-06-07 20:22:08] [info] Database connected successfully
[2025-06-07 20:22:08] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: INSERT INTO activity_logs (user_id, company_id, action, entity_type, entity_id, description, ip_address, created_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
[2025-06-07 20:22:51] [info] Database connected successfully
[2025-06-07 20:26:16] [info] Database connected successfully
[2025-06-07 20:27:09] [info] Database connected successfully
[2025-06-07 20:27:11] [info] Database connected successfully
[2025-06-07 20:27:25] [info] Database connected successfully
[2025-06-07 20:27:25] [info] Database connected successfully
[2025-06-07 20:28:01] [info] Database connected successfully
[2025-06-07 20:28:01] [info] Database connected successfully
[2025-06-07 20:32:11] [info] Database connected successfully
[2025-06-07 20:37:13] [info] Database connected successfully
[2025-06-07 20:42:15] [info] Database connected successfully
[2025-06-07 20:47:17] [info] Database connected successfully
[2025-06-07 20:52:19] [info] Database connected successfully
[2025-06-07 20:56:07] [info] Database connected successfully
[2025-06-07 20:57:21] [info] Database connected successfully
[2025-06-07 20:58:01] [info] Database connected successfully
[2025-06-07 21:02:23] [info] Database connected successfully
[2025-06-07 21:05:45] [info] Database connected successfully
[2025-06-07 21:06:15] [info] Database connected successfully
[2025-06-07 21:06:15] [info] Database connected successfully
[2025-06-07 21:06:51] [info] Database connected successfully
[2025-06-07 21:06:57] [info] Database connected successfully
[2025-06-07 21:07:02] [info] Database connected successfully
[2025-06-07 21:07:09] [info] Database connected successfully
[2025-06-07 21:07:14] [info] Database connected successfully
[2025-06-07 21:07:25] [info] Database connected successfully
[2025-06-07 21:07:25] [info] Database connected successfully
[2025-06-07 21:07:34] [info] Database connected successfully
[2025-06-07 21:07:49] [info] Database connected successfully
[2025-06-07 21:07:52] [info] Database connected successfully
[2025-06-07 21:07:54] [info] Database connected successfully
[2025-06-07 21:07:55] [info] Database connected successfully
[2025-06-07 21:07:57] [info] Database connected successfully
[2025-06-07 21:08:35] [info] Database connected successfully
[2025-06-07 21:08:48] [info] Database connected successfully
[2025-06-07 21:12:27] [info] Database connected successfully
[2025-06-07 21:17:29] [info] Database connected successfully
[2025-06-07 21:22:31] [info] Database connected successfully
[2025-06-07 21:26:16] [info] Database connected successfully
[2025-06-07 21:26:22] [info] Database connected successfully
[2025-06-07 21:27:12] [info] Database connected successfully
[2025-06-07 21:27:13] [info] Database connected successfully
[2025-06-07 21:27:17] [info] Database connected successfully
[2025-06-07 21:27:17] [info] Database connected successfully
[2025-06-07 21:27:34] [info] Database connected successfully
[2025-06-07 21:28:56] [info] Database connected successfully
[2025-06-07 21:28:56] [info] Database connected successfully
[2025-06-07 21:29:24] [info] Database connected successfully
[2025-06-07 21:29:24] [info] Database connected successfully
[2025-06-07 21:29:46] [info] Database connected successfully
[2025-06-07 21:29:46] [info] Database connected successfully
[2025-06-07 21:30:56] [info] Database connected successfully
[2025-06-07 21:30:56] [info] Database connected successfully
[2025-06-07 21:32:36] [info] Database connected successfully
[2025-06-07 21:33:09] [info] Database connected successfully
[2025-06-07 21:34:49] [info] Database connected successfully
[2025-06-07 21:37:38] [info] Database connected successfully
[2025-06-07 21:42:40] [info] Database connected successfully
[2025-06-07 21:44:08] [info] Database connected successfully
[2025-06-07 21:44:39] [info] Database connected successfully
[2025-06-07 21:45:53] [info] Database connected successfully
[2025-06-07 21:45:57] [info] Database connected successfully
[2025-06-07 21:46:02] [info] Database connected successfully
[2025-06-07 21:46:07] [info] Database connected successfully
[2025-06-07 21:46:14] [info] Database connected successfully
[2025-06-07 21:46:44] [info] Database connected successfully
[2025-06-07 21:47:07] [info] Database connected successfully
[2025-06-07 21:47:18] [info] Database connected successfully
[2025-06-07 21:47:26] [info] Database connected successfully
[2025-06-07 21:47:41] [info] Database connected successfully
[2025-06-07 21:47:59] [info] Database connected successfully
[2025-06-07 21:48:27] [info] Database connected successfully
[2025-06-07 21:50:03] [info] Database connected successfully
[2025-06-07 21:50:06] [info] Database connected successfully
[2025-06-07 21:50:13] [info] Database connected successfully
[2025-06-07 21:50:26] [info] Database connected successfully
[2025-06-07 21:50:26] [info] Database connected successfully
[2025-06-07 21:50:53] [info] Database connected successfully
[2025-06-07 21:50:55] [info] Database connected successfully
[2025-06-07 21:50:59] [info] Database connected successfully
[2025-06-07 21:50:59] [info] Database connected successfully
[2025-06-07 21:51:05] [info] Database connected successfully
[2025-06-07 21:51:06] [info] Database connected successfully
[2025-06-07 21:51:10] [info] Database connected successfully
[2025-06-07 21:51:35] [info] Database connected successfully
[2025-06-07 21:51:35] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 21:51:35] [info] Database connected successfully
[2025-06-07 21:51:36] [info] Database connected successfully
[2025-06-07 21:51:38] [info] Database connected successfully
[2025-06-07 21:51:38] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 21:51:38] [info] Database connected successfully
[2025-06-07 21:52:01] [info] Database connected successfully
[2025-06-07 21:52:01] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 21:52:01] [info] Database connected successfully
[2025-06-07 21:52:42] [info] Database connected successfully
[2025-06-07 21:57:43] [info] Database connected successfully
[2025-06-07 22:02:44] [info] Database connected successfully
[2025-06-07 22:07:45] [info] Database connected successfully
[2025-06-07 22:10:49] [info] Database connected successfully
[2025-06-07 22:12:09] [info] Database connected successfully
[2025-06-07 22:12:17] [info] Database connected successfully
[2025-06-07 22:12:24] [info] Database connected successfully
[2025-06-07 22:12:46] [info] Database connected successfully
[2025-06-07 22:13:44] [info] Database connected successfully
[2025-06-07 22:13:45] [info] Database connected successfully
[2025-06-07 22:13:51] [info] Database connected successfully
[2025-06-07 22:14:33] [info] Database connected successfully
[2025-06-07 22:14:38] [info] Database connected successfully
[2025-06-07 22:17:47] [info] Database connected successfully
[2025-06-07 22:20:32] [info] Database connected successfully
[2025-06-07 22:22:48] [info] Database connected successfully
[2025-06-07 22:26:23] [info] Database connected successfully
[2025-06-07 22:26:30] [info] Database connected successfully
[2025-06-07 22:26:32] [info] Database connected successfully
[2025-06-07 22:26:36] [info] Database connected successfully
[2025-06-07 22:27:23] [info] Database connected successfully
[2025-06-07 22:35:24] [info] Database connected successfully
[2025-06-07 22:42:41] [info] Database connected successfully
[2025-06-07 22:43:28] [info] Database connected successfully
[2025-06-07 22:46:12] [info] Database connected successfully
[2025-06-07 22:46:12] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'emoji' in 'field list' SQL: SELECT category_code, category_name, emoji, color_code
                 FROM document_sorting_categories
                 WHERE company_id = ? AND is_active = 1
                 ORDER BY sort_order, category_name
[2025-06-07 22:47:10] [info] Database connected successfully
[2025-06-07 22:47:10] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'emoji' in 'field list' SQL: INSERT INTO document_sorting_categories (
                    company_id, category_name, category_code, description,
                    color_code, emoji, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
[2025-06-07 22:47:21] [info] Database connected successfully
[2025-06-07 22:47:21] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'emoji' in 'field list' SQL: INSERT INTO document_sorting_categories (
                    company_id, category_name, category_code, description,
                    color_code, emoji, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
[2025-06-07 22:47:26] [info] Database connected successfully
[2025-06-07 22:47:26] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'emoji' in 'field list' SQL: INSERT INTO document_sorting_categories (
                    company_id, category_name, category_code, description,
                    color_code, emoji, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
[2025-06-07 22:47:32] [info] Database connected successfully
[2025-06-07 22:47:32] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'emoji' in 'field list' SQL: INSERT INTO document_sorting_categories (
                    company_id, category_name, category_code, description,
                    color_code, emoji, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
[2025-06-07 22:47:48] [info] Database connected successfully
[2025-06-07 22:47:48] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'emoji' in 'field list' SQL: INSERT INTO document_sorting_categories (
                    company_id, category_name, category_code, description,
                    color_code, emoji, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
[2025-06-07 22:52:20] [info] Database connected successfully
[2025-06-07 22:52:36] [info] Database connected successfully
[2025-06-07 22:53:08] [info] Database connected successfully
[2025-06-07 22:53:21] [info] Database connected successfully
[2025-06-07 22:54:27] [info] Database connected successfully
[2025-06-07 22:54:28] [info] Database connected successfully
[2025-06-07 22:54:41] [info] Database connected successfully
[2025-06-07 22:54:41] [info] Database connected successfully
[2025-06-07 22:54:52] [info] Database connected successfully
[2025-06-07 22:54:58] [info] Database connected successfully
[2025-06-07 22:55:00] [info] Database connected successfully
[2025-06-07 22:55:06] [info] Database connected successfully
[2025-06-07 22:55:09] [info] Database connected successfully
[2025-06-07 22:55:09] [info] Database connected successfully
[2025-06-07 22:55:18] [info] Database connected successfully
[2025-06-07 22:55:18] [info] Database connected successfully
[2025-06-07 22:55:21] [info] Database connected successfully
[2025-06-07 22:55:32] [info] Database connected successfully
[2025-06-07 22:55:48] [info] Database connected successfully
[2025-06-07 22:55:48] [info] Database connected successfully
[2025-06-07 22:56:37] [info] Database connected successfully
[2025-06-07 22:56:37] [info] Database connected successfully
[2025-06-07 22:58:08] [info] Database connected successfully
[2025-06-07 22:58:14] [info] Database connected successfully
[2025-06-07 22:58:29] [info] Database connected successfully
[2025-06-07 22:58:35] [info] Database connected successfully
[2025-06-07 22:58:57] [info] Database connected successfully
[2025-06-07 22:59:07] [info] Database connected successfully
[2025-06-07 22:59:08] [info] Database connected successfully
[2025-06-07 22:59:12] [info] Database connected successfully
[2025-06-07 22:59:16] [info] Database connected successfully
[2025-06-07 22:59:18] [info] Database connected successfully
[2025-06-07 22:59:18] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 22:59:18] [info] Database connected successfully
[2025-06-07 22:59:28] [info] Database connected successfully
[2025-06-07 22:59:28] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 22:59:28] [info] Database connected successfully
[2025-06-07 23:02:07] [info] Database connected successfully
[2025-06-07 23:02:13] [info] Database connected successfully
[2025-06-07 23:02:13] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 23:02:13] [info] Database connected successfully
[2025-06-07 23:02:16] [info] Database connected successfully
[2025-06-07 23:02:56] [info] Database connected successfully
[2025-06-07 23:03:17] [info] Database connected successfully
[2025-06-07 23:03:17] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'name' cannot be null SQL: UPDATE bundles SET 
                 name = ?, description = ?, company_id = ?, category = ?, priority = ?,
                 retention_period = ?, access_level = ?, updated_at = NOW()
                 WHERE id = ?
[2025-06-07 23:03:17] [info] Database connected successfully
[2025-06-07 23:03:31] [info] Database connected successfully
[2025-06-07 23:03:39] [info] Database connected successfully
[2025-06-07 23:03:41] [info] Database connected successfully
[2025-06-07 23:03:51] [info] Database connected successfully
[2025-06-07 23:03:51] [info] Database connected successfully
[2025-06-07 23:04:05] [info] Database connected successfully
[2025-06-07 23:04:05] [info] Database connected successfully
[2025-06-07 23:06:55] [info] Database connected successfully
[2025-06-07 23:07:01] [info] Database connected successfully
[2025-06-07 23:07:01] [info] Database connected successfully
[2025-06-07 23:07:03] [info] Database connected successfully
[2025-06-07 23:07:26] [info] Database connected successfully
[2025-06-07 23:07:30] [info] Database connected successfully
[2025-06-07 23:07:40] [info] Database connected successfully
[2025-06-07 23:07:45] [info] Database connected successfully
[2025-06-07 23:07:45] [info] Database connected successfully
[2025-06-07 23:08:07] [info] Database connected successfully
[2025-06-07 23:08:07] [info] Database connected successfully
[2025-06-07 23:08:19] [info] Database connected successfully
[2025-06-07 23:08:21] [info] Database connected successfully
[2025-06-07 23:08:32] [info] Database connected successfully
[2025-06-07 23:08:33] [info] Database connected successfully
[2025-06-07 23:09:43] [info] Database connected successfully
[2025-06-07 23:09:43] [error] Query failed: SQLSTATE[HY000]: General error: 1364 Field 'code' doesn't have a default value SQL: INSERT INTO warehouses (
                    company_id, name, address, city, state, zip_code, country, 
                    phone, email, capacity, description, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
[2025-06-07 23:09:43] [info] Database connected successfully
[2025-06-07 23:10:19] [info] Database connected successfully
[2025-06-07 23:10:25] [info] Database connected successfully
[2025-06-07 23:10:25] [info] Database connected successfully
[2025-06-07 23:10:44] [info] Database connected successfully
[2025-06-07 23:10:44] [info] Database connected successfully
[2025-06-07 23:10:47] [info] Database connected successfully
[2025-06-07 23:14:41] [info] Database connected successfully
[2025-06-07 23:23:10] [info] Database connected successfully
[2025-06-07 23:23:12] [info] Database connected successfully
[2025-06-07 23:23:13] [info] Database connected successfully
[2025-06-07 23:23:52] [info] Database connected successfully
[2025-06-07 23:23:52] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 23:23:52] [info] Database connected successfully
[2025-06-07 23:23:59] [info] Database connected successfully
[2025-06-07 23:24:00] [info] Database connected successfully
[2025-06-07 23:24:00] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 23:24:00] [info] Database connected successfully
[2025-06-07 23:24:16] [info] Database connected successfully
[2025-06-07 23:24:16] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'w.location' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, w.location as warehouse_location,
                        COUNT(DISTINCT bb.bundle_id) as bundle_count,
                        COUNT(DISTINCT d.id) as document_count,
                        SUM(d.file_size) as total_size,
                        u.first_name, u.last_name,
                        c.name as company_name
                 FROM boxes b
                 LEFT JOIN warehouses w ON b.warehouse_id = w.id
                 LEFT JOIN box_bundles bb ON b.id = bb.box_id
                 LEFT JOIN bundles bun ON bb.bundle_id = bun.id
                 LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
                 LEFT JOIN users u ON b.created_by = u.id
                 LEFT JOIN companies c ON b.company_id = c.id
                 WHERE b.company_id = ?
                 GROUP BY b.id
                 ORDER BY b.created_at DESC
[2025-06-07 23:24:16] [info] Database connected successfully
[2025-06-07 23:28:38] [info] Database connected successfully
[2025-06-07 23:28:50] [info] Database connected successfully
[2025-06-07 23:28:50] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'location' in 'field list' SQL: SELECT id, name, location FROM warehouses WHERE status = 'active' ORDER BY name
[2025-06-07 23:29:10] [info] Database connected successfully
[2025-06-07 23:29:12] [info] Database connected successfully
[2025-06-07 23:29:17] [info] Database connected successfully
[2025-06-07 23:29:17] [info] Database connected successfully
[2025-06-07 23:29:22] [info] Database connected successfully
[2025-06-07 23:29:36] [info] Database connected successfully
[2025-06-07 23:29:49] [info] Database connected successfully
[2025-06-07 23:29:57] [info] Database connected successfully
[2025-06-07 23:31:53] [info] Database connected successfully
[2025-06-07 23:31:55] [info] Database connected successfully
[2025-06-07 23:31:57] [info] Database connected successfully
[2025-06-07 23:31:58] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'location' in 'field list' SQL: SELECT id, name, location FROM warehouses WHERE status = 'active' ORDER BY name
[2025-06-07 23:32:13] [info] Database connected successfully
[2025-06-07 23:32:26] [info] Database connected successfully
[2025-06-07 23:32:36] [info] Database connected successfully
[2025-06-07 23:32:38] [info] Database connected successfully
[2025-06-07 23:32:49] [info] Database connected successfully
[2025-06-07 23:32:51] [info] Database connected successfully
[2025-06-07 23:32:55] [info] Database connected successfully
[2025-06-07 23:32:55] [info] Database connected successfully
[2025-06-07 23:39:33] [info] Database connected successfully
[2025-06-07 23:39:33] [info] Database connected successfully
[2025-06-07 23:39:38] [info] Database connected successfully
[2025-06-07 23:39:38] [info] Database connected successfully
[2025-06-07 23:40:03] [info] Database connected successfully
[2025-06-07 23:53:00] [info] Database connected successfully
[2025-06-07 23:53:03] [info] Database connected successfully
[2025-06-07 23:53:47] [info] Database connected successfully
[2025-06-07 23:53:47] [info] Database connected successfully
[2025-06-07 23:55:01] [info] Database connected successfully
[2025-06-07 23:55:06] [info] Database connected successfully
[2025-06-07 23:55:06] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'location' in 'field list' SQL: SELECT id, name, location FROM warehouses WHERE status = 'active' ORDER BY name
[2025-06-07 23:55:17] [info] Database connected successfully
[2025-06-07 23:55:26] [info] Database connected successfully
[2025-06-08 00:01:41] [info] Database connected successfully
[2025-06-08 00:01:49] [info] Database connected successfully
[2025-06-08 00:04:29] [info] Database connected successfully
[2025-06-08 00:04:36] [info] Database connected successfully
[2025-06-08 00:04:36] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
[2025-06-08 00:04:36] [info] Database connected successfully
[2025-06-08 00:04:41] [info] Database connected successfully
[2025-06-08 00:04:46] [info] Database connected successfully
[2025-06-08 00:04:46] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
[2025-06-08 00:04:46] [info] Database connected successfully
[2025-06-08 00:04:49] [info] Database connected successfully
[2025-06-08 00:04:54] [info] Database connected successfully
[2025-06-08 00:04:54] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
[2025-06-08 00:04:54] [info] Database connected successfully
[2025-06-08 00:07:00] [info] Database connected successfully
[2025-06-08 00:07:04] [info] Database connected successfully
[2025-06-08 00:07:07] [info] Database connected successfully
[2025-06-08 00:07:07] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
[2025-06-08 00:07:08] [info] Database connected successfully
[2025-06-08 00:07:10] [info] Database connected successfully
[2025-06-08 00:07:15] [info] Database connected successfully
[2025-06-08 00:07:48] [info] Database connected successfully
[2025-06-08 00:07:48] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.locations' doesn't exist SQL: SELECT l.*, w.company_id
                 FROM locations l
                 JOIN warehouses w ON l.warehouse_id = w.id
                 WHERE l.id = ? AND w.company_id = ?
[2025-06-08 00:07:48] [info] Database connected successfully
[2025-06-08 00:07:48] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
[2025-06-08 00:07:48] [info] Database connected successfully
[2025-06-08 00:07:55] [info] Database connected successfully
[2025-06-08 00:07:59] [info] Database connected successfully
[2025-06-08 00:08:00] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
[2025-06-08 00:08:00] [info] Database connected successfully
[2025-06-08 00:08:03] [info] Database connected successfully
[2025-06-08 00:09:59] [info] Database connected successfully
[2025-06-08 00:10:04] [info] Database connected successfully
[2025-06-08 00:10:04] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
[2025-06-08 00:10:04] [info] Database connected successfully
[2025-06-08 00:10:08] [info] Database connected successfully
[2025-06-08 00:10:38] [info] Database connected successfully
[2025-06-08 00:10:38] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
[2025-06-08 00:10:38] [info] Database connected successfully
[2025-06-08 00:10:41] [info] Database connected successfully
[2025-06-08 00:14:12] [info] Database connected successfully
[2025-06-08 00:14:15] [info] Database connected successfully
[2025-06-08 00:15:12] [info] Database connected successfully
[2025-06-08 00:15:21] [info] Database connected successfully
[2025-06-08 06:07:09] [info] Database connected successfully
[2025-06-08 06:07:14] [info] Database connected successfully
[2025-06-08 06:07:31] [info] Database connected successfully
[2025-06-08 06:07:32] [info] Database connected successfully
[2025-06-08 06:07:53] [info] Database connected successfully
[2025-06-08 06:07:56] [info] Database connected successfully
[2025-06-08 06:07:59] [info] Database connected successfully
[2025-06-08 06:08:03] [info] Database connected successfully
[2025-06-08 06:08:06] [info] Database connected successfully
[2025-06-08 06:08:08] [info] Database connected successfully
[2025-06-08 06:08:15] [info] Database connected successfully
[2025-06-08 06:09:18] [info] Database connected successfully
[2025-06-08 06:09:18] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name, c.name as category_name
                 FROM documents d
                 LEFT JOIN users u ON d.created_by = u.id
                 LEFT JOIN categories c ON d.category_id = c.id
                 WHERE d.storage_location_id = ?
                 ORDER BY d.created_at DESC
                 LIMIT ? OFFSET ?
[2025-06-08 06:09:18] [info] Database connected successfully
[2025-06-08 06:09:22] [info] Database connected successfully
[2025-06-08 06:11:07] [info] Database connected successfully
[2025-06-08 06:11:22] [info] Database connected successfully
[2025-06-08 06:15:51] [info] Database connected successfully
[2025-06-08 06:16:52] [info] Database connected successfully
[2025-06-08 06:17:25] [info] Database connected successfully
[2025-06-08 06:18:25] [info] Database connected successfully
[2025-06-08 06:18:41] [info] Database connected successfully
[2025-06-08 06:18:41] [info] Database connected successfully
[2025-06-08 06:18:54] [info] Database connected successfully
[2025-06-08 06:19:17] [info] Database connected successfully
[2025-06-08 06:19:17] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'on clause' SQL: SELECT l.*, COUNT(d.id) as document_count
                 FROM storage_locations l
                 LEFT JOIN documents d ON l.id = d.storage_location_id
                 WHERE l.warehouse_id = ?
                 GROUP BY l.id
                 ORDER BY l.name
[2025-06-08 06:19:17] [info] Database connected successfully
[2025-06-08 06:19:26] [info] Database connected successfully
[2025-06-08 06:19:27] [info] Database connected successfully
[2025-06-08 06:20:06] [info] Database connected successfully
[2025-06-08 06:20:53] [info] Database connected successfully
[2025-06-08 06:21:11] [info] Database connected successfully
[2025-06-08 06:21:11] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'location' in 'field list' SQL: SELECT id, name, location FROM warehouses WHERE status = 'active' ORDER BY name
[2025-06-08 06:21:53] [info] Database connected successfully
[2025-06-08 06:21:55] [info] Database connected successfully
[2025-06-08 06:21:55] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'location' in 'field list' SQL: SELECT id, name, location FROM warehouses WHERE status = 'active' ORDER BY name
[2025-06-08 06:23:19] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-08 06:23:57] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-08 06:24:20] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-08 06:27:19] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-08 06:28:28] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-08 06:28:35] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-08 06:30:27] [error] Database connection failed: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[2025-06-08 06:35:06] [error] Database connection failed: SQLSTATE[HY000] [2006] MySQL server has gone away
[2025-06-08 06:35:10] [info] Database connected successfully
[2025-06-08 06:35:10] [info] Database connected successfully
[2025-06-08 06:35:32] [info] Database connected successfully
[2025-06-08 06:35:35] [info] Database connected successfully
[2025-06-08 06:35:36] [info] Database connected successfully
[2025-06-08 06:36:44] [info] Database connected successfully
[2025-06-08 06:37:05] [info] Database connected successfully
[2025-06-08 06:37:08] [info] Database connected successfully
[2025-06-08 06:37:38] [info] Database connected successfully
[2025-06-08 06:37:59] [info] Database connected successfully
[2025-06-08 06:37:59] [info] Database connected successfully
[2025-06-08 06:38:01] [info] Database connected successfully
[2025-06-08 06:38:02] [info] Database connected successfully
[2025-06-08 06:38:06] [info] Database connected successfully
[2025-06-08 06:38:14] [info] Database connected successfully
[2025-06-08 06:38:14] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.storage_location_id' in 'on clause' SQL: SELECT l.*, COUNT(d.id) as document_count
                 FROM storage_locations l
                 LEFT JOIN documents d ON l.id = d.storage_location_id
                 WHERE l.warehouse_id = ?
                 GROUP BY l.id
                 ORDER BY l.name
[2025-06-08 06:38:14] [info] Database connected successfully
[2025-06-08 06:38:40] [info] Database connected successfully
[2025-06-08 06:39:18] [info] Database connected successfully
[2025-06-08 06:39:30] [info] Database connected successfully
[2025-06-08 06:39:56] [info] Database connected successfully
[2025-06-08 06:40:07] [info] Database connected successfully
[2025-06-08 06:40:23] [info] Database connected successfully
[2025-06-08 06:40:27] [info] Database connected successfully
[2025-06-08 06:41:09] [info] Database connected successfully
[2025-06-08 06:42:01] [info] Database connected successfully
[2025-06-08 06:42:02] [info] Database connected successfully
[2025-06-08 06:42:05] [info] Database connected successfully
[2025-06-08 06:42:06] [info] Database connected successfully
[2025-06-08 06:42:14] [info] Database connected successfully
[2025-06-08 06:42:41] [info] Database connected successfully
[2025-06-08 06:43:11] [info] Database connected successfully
[2025-06-08 06:43:14] [info] Database connected successfully
[2025-06-08 06:43:17] [info] Database connected successfully
[2025-06-08 06:43:27] [info] Database connected successfully
[2025-06-08 06:43:32] [info] Database connected successfully
[2025-06-08 06:43:50] [info] Database connected successfully
[2025-06-08 06:44:06] [info] Database connected successfully
[2025-06-08 06:44:09] [info] Database connected successfully
[2025-06-08 06:44:18] [info] Database connected successfully
[2025-06-08 06:44:42] [info] Database connected successfully
[2025-06-08 06:44:46] [info] Database connected successfully
[2025-06-08 06:44:48] [info] Database connected successfully
[2025-06-08 06:44:49] [info] Database connected successfully
[2025-06-08 06:44:51] [info] Database connected successfully
[2025-06-08 06:44:52] [info] Database connected successfully
[2025-06-08 06:44:56] [info] Database connected successfully
[2025-06-08 06:44:57] [info] Database connected successfully
[2025-06-08 06:45:03] [info] Database connected successfully
[2025-06-08 06:45:03] [info] Database connected successfully
[2025-06-08 06:45:15] [info] Database connected successfully
[2025-06-08 06:45:34] [info] Database connected successfully
[2025-06-08 06:45:36] [info] Database connected successfully
[2025-06-08 06:45:38] [info] Database connected successfully
[2025-06-08 06:45:45] [info] Database connected successfully
[2025-06-08 06:46:09] [info] Database connected successfully
[2025-06-08 06:46:11] [info] Database connected successfully
[2025-06-08 06:46:12] [info] Database connected successfully
[2025-06-08 06:46:17] [info] Database connected successfully
[2025-06-08 06:46:27] [info] Database connected successfully
[2025-06-08 06:46:33] [info] Database connected successfully
[2025-06-08 06:53:01] [info] Database connected successfully
[2025-06-08 06:56:29] [info] Database connected successfully
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'companies' already exists SQL: -- Create companies table
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    storage_used BIGINT DEFAULT 0,
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_status (status),
    INDEX idx_companies_created (created_at)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists SQL: -- Create users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer', 'client') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_users_company (company_id),
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    IND...' at line 20 SQL: -- Create warehouses table
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    INDEX idx_warehouses_company (company_id),
    INDEX idx_warehouses_manager (manager_id),
    INDEX idx_warehouses_status (status),
    INDEX idx_warehouses_created (created_at)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'storage_locations' already exists SQL: -- Create storage_locations table
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_location (warehouse_id, identifier),
    INDEX idx_locations_warehouse (warehouse_id),
    INDEX idx_locations_parent (parent_id),
    INDEX idx_locations_type (type),
    INDEX idx_locations_barcode (barcode_value),
    INDEX idx_locations_status (status),
    INDEX idx_locations_hierarchy (warehouse_id, parent_id, type)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'document_categories' already exists SQL: -- Create document_categories table
CREATE TABLE document_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES document_categories(id) ON DELETE CASCADE,
    
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_sort (sort_order),
    INDEX idx_categories_system (is_system)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY ...' at line 39 SQL: -- Create documents table
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL, -- For version control
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash
    version VARCHAR(20) DEFAULT '1.0',
    is_latest_version BOOLEAN DEFAULT TRUE,
    document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
    category_id INT,
    tags JSON,
    metadata JSON,
    ocr_text LONGTEXT,
    thumbnail_path VARCHAR(500),
    preview_path VARCHAR(500),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    access_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    retention_date DATE,
    expiry_date DATE,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES document_categories(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    
    INDEX idx_documents_company (company_id),
    INDEX idx_documents_parent (parent_id),
    INDEX idx_documents_category (category_id),
    INDEX idx_documents_creator (created_by),
    INDEX idx_documents_type (document_type),
    INDEX idx_documents_status (status),
    INDEX idx_documents_version (is_latest_version),
    INDEX idx_documents_created (created_at),
    INDEX idx_documents_hash (file_hash),
    INDEX idx_documents_company_search (company_id, status, created_at),
    
    FULLTEXT KEY ft_documents_content (title, description, ocr_text)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx...' at line 16 SQL: -- Create document_locations table
CREATE TABLE document_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    document_id INT NOT NULL,
    location_id INT,
    storage_type ENUM('physical', 'digital', 'hybrid') NOT NULL,
    physical_reference VARCHAR(255), -- Box number, shelf reference, etc.
    digital_path VARCHAR(1000),
    quantity INT DEFAULT 1,
    condition_notes TEXT,
    moved_by INT NOT NULL,
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx_doc_locations_document (document_id),
    INDEX idx_doc_locations_location (location_id),
    INDEX idx_doc_locations_current (is_current),
    INDEX idx_doc_locations_moved (moved_at),
    INDEX idx_doc_locations_type (storage_type)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'barcodes' already exists SQL: -- Create barcodes table
CREATE TABLE barcodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('document', 'location', 'box', 'bundle') NOT NULL,
    entity_id INT NOT NULL,
    barcode_value VARCHAR(255) UNIQUE NOT NULL,
    barcode_type ENUM('qr', 'code128', 'code39', 'ean13', 'datamatrix') DEFAULT 'qr',
    barcode_image_path VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    generated_by INT NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_scanned_at TIMESTAMP NULL,
    scan_count INT DEFAULT 0,
    
    FOREIGN KEY (generated_by) REFERENCES users(id),
    
    INDEX idx_barcodes_value (barcode_value),
    INDEX idx_barcodes_entity (entity_type, entity_id),
    INDEX idx_barcodes_active (is_active),
    INDEX idx_barcodes_generated (generated_at),
    INDEX idx_barcodes_scan (barcode_value, is_active)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_type' SQL: -- Service Provider Model Enhancements
-- Updates to support third-party document storage service provider business model

-- Add storage type to documents table (physical vs online)
ALTER TABLE documents 
ADD COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER location_id,
ADD COLUMN online_storage_path VARCHAR(500) NULL AFTER storage_type,
ADD COLUMN physical_location_notes TEXT NULL AFTER online_storage_path,
ADD INDEX idx_storage_type (storage_type),
ADD INDEX idx_company_storage (company_id, storage_type)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX i...' at line 15 SQL: -- Create bundles table
CREATE TABLE bundles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    bundle_type ENUM('intake', 'project', 'department', 'custom') DEFAULT 'custom',
    location_id INT,
    created_by INT NOT NULL,
    status ENUM('open', 'closed', 'archived') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_bundles_company (company_id),
    INDEX idx_bundles_location (location_id),
    INDEX idx_bundles_creator (created_by),
    INDEX idx_bundles_type (bundle_type),
    INDEX idx_bundles_status (status),
    INDEX idx_bundles_created (created_at)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    INDEX idx_audit_company (company_id),
    INDEX idx_audit_...' at line 17 SQL: -- Create audit_logs table
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    user_id INT,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    action ENUM('create', 'read', 'update', 'delete', 'login', 'logout', 'download', 'upload', 'scan') NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON SET NULL,
    
    INDEX idx_audit_company (company_id),
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_entity (entity_type, entity_id),
    INDEX idx_audit_action (action),
    INDEX idx_audit_created (created_at),
    INDEX idx_audit_logs_search (company_id, entity_type, action, created_at),
    INDEX idx_audit_company_date (company_id, created_at),
    INDEX idx_audit_user_action (user_id, action, created_at)
)
[2025-06-08 06:56:29] [error] SQL execution failed: SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails SQL: -- Update bundles table to match controller expectations
-- Drop and recreate bundles table with proper structure

DROP TABLE IF EXISTS bundles
[2025-06-08 06:56:47] [info] Database connected successfully
[2025-06-08 06:57:14] [info] Database connected successfully
[2025-06-08 06:57:14] [error] Query failed: SQLSTATE[HY000]: General error: 1553 Cannot drop index 'box_id': needed in a foreign key constraint SQL: ALTER TABLE documents DROP COLUMN box_id
[2025-06-08 06:57:47] [info] Database connected successfully
[2025-06-08 07:00:33] [info] Database connected successfully
[2025-06-08 07:00:43] [info] Database connected successfully
[2025-06-08 07:00:43] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.box_id' in 'on clause' SQL: SELECT COUNT(d.id) as count
                 FROM documents d
                 JOIN boxes b ON d.box_id = b.id
                 WHERE b.warehouse_id = ? AND d.status != 'deleted'
[2025-06-08 07:03:31] [info] Database connected successfully
[2025-06-08 07:03:39] [info] Database connected successfully
[2025-06-08 07:03:46] [info] Database connected successfully
[2025-06-08 07:03:54] [info] Database connected successfully
[2025-06-08 07:03:55] [info] Database connected successfully
[2025-06-08 07:04:04] [info] Database connected successfully
[2025-06-08 07:04:09] [info] Database connected successfully
[2025-06-08 07:04:13] [info] Database connected successfully
[2025-06-08 07:04:16] [info] Database connected successfully
[2025-06-08 07:04:17] [info] Database connected successfully
[2025-06-08 07:04:17] [info] Database connected successfully
[2025-06-08 07:04:17] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.box_id' in 'on clause' SQL: SELECT w.*,
                        COUNT(DISTINCT b.id) as total_boxes,
                        COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') THEN b.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN boxes b ON w.id = b.warehouse_id
                 LEFT JOIN documents d ON b.id = d.box_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-08 07:04:17] [info] Database connected successfully
[2025-06-08 07:04:21] [info] Database connected successfully
[2025-06-08 07:04:23] [info] Database connected successfully
[2025-06-08 07:04:24] [info] Database connected successfully
[2025-06-08 07:04:24] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.box_id' in 'on clause' SQL: SELECT w.*,
                        COUNT(DISTINCT b.id) as total_boxes,
                        COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') THEN b.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN boxes b ON w.id = b.warehouse_id
                 LEFT JOIN documents d ON b.id = d.box_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-08 07:04:24] [info] Database connected successfully
[2025-06-08 07:04:30] [info] Database connected successfully
[2025-06-08 07:04:38] [info] Database connected successfully
[2025-06-08 07:04:38] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.box_id' in 'on clause' SQL: SELECT COUNT(d.id) as count
                 FROM documents d
                 JOIN boxes b ON d.box_id = b.id
                 WHERE b.warehouse_id = ? AND d.status != 'deleted'
[2025-06-08 07:04:46] [info] Database connected successfully
[2025-06-08 07:05:05] [info] Database connected successfully
[2025-06-08 07:05:40] [info] Database connected successfully
[2025-06-08 07:07:34] [info] Database connected successfully
[2025-06-08 07:09:37] [info] Database connected successfully
[2025-06-08 07:09:54] [info] Database connected successfully
[2025-06-08 07:10:03] [info] Database connected successfully
[2025-06-08 07:10:08] [info] Database connected successfully
[2025-06-08 07:10:34] [info] Database connected successfully
[2025-06-08 07:10:37] [info] Database connected successfully
[2025-06-08 07:10:38] [info] Database connected successfully
[2025-06-08 07:17:11] [info] Database connected successfully
[2025-06-08 07:17:48] [info] Database connected successfully
[2025-06-08 07:17:50] [info] Database connected successfully
[2025-06-08 07:17:53] [info] Database connected successfully
[2025-06-08 07:17:53] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.box_id' in 'on clause' SQL: SELECT w.*,
                        COUNT(DISTINCT b.id) as total_boxes,
                        COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') THEN b.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN boxes b ON w.id = b.warehouse_id
                 LEFT JOIN documents d ON b.id = d.box_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-08 07:17:53] [info] Database connected successfully
[2025-06-08 07:18:35] [info] Database connected successfully
[2025-06-08 07:19:15] [info] Database connected successfully
[2025-06-08 07:19:56] [info] Database connected successfully
[2025-06-08 07:19:56] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.box_id' in 'on clause' SQL: SELECT w.*,
                        COUNT(DISTINCT b.id) as total_boxes,
                        COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') THEN b.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN boxes b ON w.id = b.warehouse_id
                 LEFT JOIN documents d ON b.id = d.box_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-08 07:19:56] [info] Database connected successfully
[2025-06-08 07:20:06] [info] Database connected successfully
[2025-06-08 07:20:06] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.box_id' in 'on clause' SQL: SELECT w.*,
                        COUNT(DISTINCT b.id) as total_boxes,
                        COUNT(DISTINCT CASE WHEN b.status IN ('partial', 'full') THEN b.id END) as occupied_boxes,
                        COUNT(DISTINCT d.id) as total_documents,
                        u.first_name, u.last_name
                 FROM warehouses w
                 LEFT JOIN boxes b ON w.id = b.warehouse_id
                 LEFT JOIN documents d ON b.id = d.box_id AND d.status != 'deleted'
                 LEFT JOIN users u ON w.created_by = u.id
                 WHERE w.status = 'active'
                 GROUP BY w.id
                 ORDER BY w.name
[2025-06-08 07:20:06] [info] Database connected successfully
[2025-06-08 07:21:34] [info] Database connected successfully
[2025-06-08 07:21:44] [info] Database connected successfully
[2025-06-08 07:21:51] [info] Database connected successfully
[2025-06-08 07:21:58] [info] Database connected successfully
[2025-06-08 07:22:03] [info] Database connected successfully
[2025-06-08 07:22:22] [info] Database connected successfully
[2025-06-08 07:22:31] [info] Database connected successfully
[2025-06-08 07:22:40] [info] Database connected successfully
[2025-06-08 07:22:50] [info] Database connected successfully
[2025-06-08 07:23:50] [info] Database connected successfully
[2025-06-08 07:23:55] [info] Database connected successfully
[2025-06-08 07:23:59] [info] Database connected successfully
[2025-06-08 07:27:11] [info] Database connected successfully
[2025-06-08 07:27:55] [info] Database connected successfully
[2025-06-08 07:27:55] [info] Database connected successfully
[2025-06-08 07:28:01] [info] Database connected successfully
[2025-06-08 07:28:01] [info] Database connected successfully
[2025-06-08 07:28:11] [info] Database connected successfully
[2025-06-08 07:28:15] [info] Database connected successfully
[2025-06-08 07:28:18] [info] Database connected successfully
[2025-06-08 07:28:39] [info] Database connected successfully
[2025-06-08 07:28:49] [info] Database connected successfully
[2025-06-08 07:28:49] [info] Database connected successfully
[2025-06-08 07:29:58] [info] Database connected successfully
[2025-06-08 07:33:00] [info] Database connected successfully
[2025-06-08 07:33:05] [info] Database connected successfully
[2025-06-08 07:33:05] [info] Database connected successfully
[2025-06-08 07:33:20] [info] Database connected successfully
[2025-06-08 07:33:20] [info] Database connected successfully
[2025-06-08 07:33:26] [info] Database connected successfully
[2025-06-08 07:33:28] [info] Database connected successfully
[2025-06-08 07:39:19] [info] Database connected successfully
[2025-06-08 07:39:29] [info] Database connected successfully
[2025-06-08 07:39:29] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 07:39:29] [info] Database connected successfully
[2025-06-08 07:39:32] [info] Database connected successfully
[2025-06-08 07:40:53] [info] Database connected successfully
[2025-06-08 07:42:11] [info] Database connected successfully
[2025-06-08 07:49:27] [info] Database connected successfully
[2025-06-08 07:49:28] [info] Database connected successfully
[2025-06-08 07:49:37] [info] Database connected successfully
[2025-06-08 07:49:41] [info] Database connected successfully
[2025-06-08 07:49:41] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 07:49:41] [info] Database connected successfully
[2025-06-08 07:49:46] [info] Database connected successfully
[2025-06-08 07:50:09] [info] Database connected successfully
[2025-06-08 07:50:09] [info] Database connected successfully
[2025-06-08 07:50:12] [info] Database connected successfully
[2025-06-08 07:50:29] [info] Database connected successfully
[2025-06-08 07:50:34] [info] Database connected successfully
[2025-06-08 07:50:41] [info] Database connected successfully
[2025-06-08 07:50:41] [info] Database connected successfully
[2025-06-08 07:50:56] [info] Database connected successfully
[2025-06-08 07:51:56] [info] Database connected successfully
[2025-06-08 07:53:03] [info] Database connected successfully
[2025-06-08 07:54:01] [info] Database connected successfully
[2025-06-08 07:54:04] [info] Database connected successfully
[2025-06-08 07:54:08] [info] Database connected successfully
[2025-06-08 07:54:14] [info] Database connected successfully
[2025-06-08 07:54:14] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 07:54:14] [info] Database connected successfully
[2025-06-08 07:57:01] [info] Database connected successfully
[2025-06-08 07:59:10] [info] Database connected successfully
[2025-06-08 08:00:54] [info] Database connected successfully
[2025-06-08 08:01:20] [info] Database connected successfully
[2025-06-08 08:02:04] [info] Database connected successfully
[2025-06-08 08:03:13] [info] Database connected successfully
[2025-06-08 08:04:00] [info] Database connected successfully
[2025-06-08 08:06:52] [info] Database connected successfully
[2025-06-08 08:08:15] [info] Database connected successfully
[2025-06-08 08:08:43] [info] Database connected successfully
[2025-06-08 08:09:30] [info] Database connected successfully
[2025-06-08 08:09:39] [info] Database connected successfully
[2025-06-08 08:09:41] [info] Database connected successfully
[2025-06-08 08:13:20] [info] Database connected successfully
[2025-06-08 08:14:56] [info] Database connected successfully
[2025-06-08 08:16:03] [info] Database connected successfully
[2025-06-08 08:16:50] [info] Database connected successfully
[2025-06-08 08:18:25] [info] Database connected successfully
[2025-06-08 08:19:42] [info] Database connected successfully
[2025-06-08 08:22:18] [info] Database connected successfully
[2025-06-08 08:22:46] [info] Database connected successfully
[2025-06-08 08:23:38] [info] Database connected successfully
[2025-06-08 08:24:36] [info] Database connected successfully
[2025-06-08 08:24:50] [info] Database connected successfully
[2025-06-08 08:24:50] [info] Database connected successfully
[2025-06-08 08:25:00] [info] Database connected successfully
[2025-06-08 08:25:00] [info] Database connected successfully
[2025-06-08 08:25:08] [info] Database connected successfully
[2025-06-08 08:26:13] [info] Database connected successfully
[2025-06-08 08:26:19] [info] Database connected successfully
[2025-06-08 08:26:19] [info] Database connected successfully
[2025-06-08 08:27:34] [info] Database connected successfully
[2025-06-08 08:27:45] [info] Database connected successfully
[2025-06-08 08:27:45] [info] Database connected successfully
[2025-06-08 08:27:56] [info] Database connected successfully
[2025-06-08 08:27:56] [info] Database connected successfully
[2025-06-08 08:29:05] [info] Database connected successfully
[2025-06-08 08:31:16] [info] Database connected successfully
[2025-06-08 08:31:31] [info] Database connected successfully
[2025-06-08 08:31:34] [info] Database connected successfully
[2025-06-08 08:31:35] [info] Database connected successfully
[2025-06-08 08:31:37] [info] Database connected successfully
[2025-06-08 08:31:42] [info] Database connected successfully
[2025-06-08 08:31:42] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 08:31:42] [info] Database connected successfully
[2025-06-08 08:31:59] [info] Database connected successfully
[2025-06-08 08:32:26] [info] Database connected successfully
[2025-06-08 08:32:26] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 08:32:26] [info] Database connected successfully
[2025-06-08 08:33:20] [info] Database connected successfully
[2025-06-08 08:33:22] [info] Database connected successfully
[2025-06-08 08:33:31] [info] Database connected successfully
[2025-06-08 08:33:34] [info] Database connected successfully
[2025-06-08 08:34:05] [info] Database connected successfully
[2025-06-08 08:34:47] [info] Database connected successfully
[2025-06-08 08:35:12] [info] Database connected successfully
[2025-06-08 08:35:42] [info] Database connected successfully
[2025-06-08 08:36:06] [info] Database connected successfully
[2025-06-08 08:36:35] [info] Database connected successfully
[2025-06-08 08:36:49] [info] Database connected successfully
[2025-06-08 08:36:55] [info] Database connected successfully
[2025-06-08 08:36:55] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 08:36:56] [info] Database connected successfully
[2025-06-08 08:37:05] [info] Database connected successfully
[2025-06-08 08:37:10] [info] Database connected successfully
[2025-06-08 08:37:10] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 08:37:10] [info] Database connected successfully
[2025-06-08 08:39:12] [info] Database connected successfully
[2025-06-08 08:39:15] [info] Database connected successfully
[2025-06-08 08:39:16] [info] Database connected successfully
[2025-06-08 08:39:20] [info] Database connected successfully
[2025-06-08 08:39:20] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 08:39:20] [info] Database connected successfully
[2025-06-08 08:39:27] [info] Database connected successfully
[2025-06-08 08:39:57] [info] Database connected successfully
[2025-06-08 08:39:57] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 08:39:57] [info] Database connected successfully
[2025-06-08 08:40:53] [info] Database connected successfully
[2025-06-08 08:40:57] [info] Database connected successfully
[2025-06-08 08:40:57] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 SQL: UPDATE bundles SET status = 'deleted', updated_at = NOW() WHERE id = ?
[2025-06-08 08:40:57] [info] Database connected successfully
[2025-06-08 08:42:29] [info] Database connected successfully
[2025-06-08 08:42:59] [info] Database connected successfully
[2025-06-08 08:42:59] [info] Database connected successfully
[2025-06-08 08:43:12] [info] Database connected successfully
[2025-06-08 08:43:12] [info] Database connected successfully
[2025-06-08 08:43:32] [info] Database connected successfully
[2025-06-08 08:43:32] [info] Database connected successfully
[2025-06-08 08:48:54] [info] Database connected successfully
[2025-06-08 08:49:00] [info] Database connected successfully
[2025-06-08 08:49:12] [info] Database connected successfully
[2025-06-08 08:49:17] [info] Database connected successfully
[2025-06-08 08:49:17] [info] Database connected successfully
[2025-06-08 08:49:39] [info] Database connected successfully
[2025-06-08 08:51:08] [info] Database connected successfully
[2025-06-08 08:51:11] [info] Database connected successfully
[2025-06-08 08:52:57] [info] Database connected successfully
[2025-06-08 08:53:06] [info] Database connected successfully
[2025-06-08 08:53:06] [info] Database connected successfully
[2025-06-08 08:53:16] [info] Database connected successfully
[2025-06-08 08:53:16] [info] Database connected successfully
[2025-06-08 08:53:37] [info] Database connected successfully
[2025-06-08 08:53:37] [info] Database connected successfully
[2025-06-08 08:54:22] [info] Database connected successfully
[2025-06-08 08:54:22] [info] Database connected successfully
[2025-06-08 08:54:29] [info] Database connected successfully
[2025-06-08 08:55:18] [info] Database connected successfully
[2025-06-08 08:55:33] [info] Database connected successfully
[2025-06-08 08:59:52] [info] Database connected successfully
[2025-06-08 08:59:57] [info] Database connected successfully
[2025-06-08 08:59:58] [info] Database connected successfully
[2025-06-08 09:00:04] [info] Database connected successfully
[2025-06-08 09:00:04] [info] Database connected successfully
[2025-06-08 09:00:15] [info] Database connected successfully
[2025-06-08 09:00:15] [info] Database connected successfully
[2025-06-08 09:01:54] [info] Database connected successfully
[2025-06-08 09:01:59] [info] Database connected successfully
[2025-06-08 09:01:59] [info] Database connected successfully
[2025-06-08 09:03:52] [info] Database connected successfully
[2025-06-08 09:04:24] [info] Database connected successfully
[2025-06-08 09:04:24] [info] Database connected successfully
[2025-06-08 09:05:14] [info] Database connected successfully
[2025-06-08 09:05:19] [info] Database connected successfully
[2025-06-08 09:05:19] [info] Database connected successfully
[2025-06-08 09:05:26] [info] Database connected successfully
[2025-06-08 09:05:26] [info] Database connected successfully
[2025-06-08 09:06:18] [info] Database connected successfully
[2025-06-08 09:07:33] [info] Database connected successfully
[2025-06-08 09:07:33] [info] Database connected successfully
[2025-06-08 09:16:31] [info] Database connected successfully
[2025-06-08 09:16:39] [info] Database connected successfully
[2025-06-08 09:16:39] [info] Database connected successfully
[2025-06-08 09:16:46] [info] Database connected successfully
[2025-06-08 09:16:46] [info] Database connected successfully
[2025-06-08 09:20:57] [info] Database connected successfully
[2025-06-08 09:21:02] [info] Database connected successfully
[2025-06-08 09:21:02] [info] Database connected successfully
[2025-06-08 09:27:19] [info] Database connected successfully
[2025-06-08 09:27:58] [info] Database connected successfully
[2025-06-08 09:29:01] [info] Database connected successfully
[2025-06-08 09:29:15] [info] Database connected successfully
[2025-06-08 09:29:30] [info] Database connected successfully
[2025-06-08 09:31:38] [info] Database connected successfully
[2025-06-08 09:31:40] [info] Database connected successfully
[2025-06-08 09:31:42] [info] Database connected successfully
[2025-06-08 09:31:43] [info] Database connected successfully
[2025-06-08 09:36:33] [info] Database connected successfully
[2025-06-08 09:36:57] [info] Database connected successfully
[2025-06-08 09:37:02] [info] Database connected successfully
[2025-06-08 09:37:14] [info] Database connected successfully
[2025-06-08 09:37:15] [info] Database connected successfully
[2025-06-08 09:39:19] [info] Database connected successfully
[2025-06-08 09:40:12] [info] Database connected successfully
[2025-06-08 09:41:12] [info] Database connected successfully
[2025-06-08 09:41:18] [info] Database connected successfully
[2025-06-08 09:41:21] [info] Database connected successfully
[2025-06-08 09:41:24] [info] Database connected successfully
[2025-06-08 09:41:32] [info] Database connected successfully
[2025-06-08 09:41:35] [info] Database connected successfully
[2025-06-08 09:42:05] [info] Database connected successfully
[2025-06-08 09:44:01] [info] Database connected successfully
[2025-06-08 09:44:14] [info] Database connected successfully
[2025-06-08 09:44:22] [info] Database connected successfully
[2025-06-08 09:44:29] [info] Database connected successfully
[2025-06-08 09:44:32] [info] Database connected successfully
[2025-06-08 09:44:33] [info] Database connected successfully
[2025-06-08 09:49:32] [info] Database connected successfully
[2025-06-08 09:49:39] [info] Database connected successfully
[2025-06-08 09:50:55] [info] Database connected successfully
[2025-06-08 09:51:05] [info] Database connected successfully
[2025-06-08 09:55:39] [info] Database connected successfully
[2025-06-08 09:56:41] [info] Database connected successfully
[2025-06-08 09:56:56] [info] Database connected successfully
[2025-06-08 09:58:16] [info] Database connected successfully
[2025-06-08 09:58:16] [info] Database connected successfully
[2025-06-08 09:58:22] [info] Database connected successfully
[2025-06-08 09:59:41] [info] Database connected successfully
[2025-06-08 10:00:03] [info] Database connected successfully
[2025-06-08 10:14:05] [info] Database connected successfully
[2025-06-08 10:14:24] [info] Database connected successfully
[2025-06-08 10:26:01] [info] Database connected successfully
[2025-06-08 10:26:09] [info] Database connected successfully
[2025-06-08 10:26:32] [info] Database connected successfully
[2025-06-08 10:27:31] [info] Database connected successfully
[2025-06-08 10:27:33] [info] Database connected successfully
[2025-06-08 10:28:53] [info] Database connected successfully
[2025-06-08 10:28:58] [info] Database connected successfully
[2025-06-08 10:40:02] [info] Database connected successfully
[2025-06-08 10:43:07] [info] Database connected successfully
[2025-06-08 10:43:58] [info] Database connected successfully
[2025-06-08 10:47:44] [info] Database connected successfully
[2025-06-08 10:49:06] [info] Database connected successfully
[2025-06-08 10:49:51] [info] Database connected successfully
[2025-06-08 10:49:59] [info] Database connected successfully
[2025-06-08 10:49:59] [info] Database connected successfully
[2025-06-08 10:55:20] [info] Database connected successfully
[2025-06-08 10:57:38] [info] Database connected successfully
[2025-06-08 10:57:53] [info] Database connected successfully
[2025-06-08 11:02:12] [info] Database connected successfully
[2025-06-08 11:03:16] [info] Database connected successfully
[2025-06-08 11:03:16] [info] Database connected successfully
[2025-06-08 11:03:35] [info] Database connected successfully
[2025-06-08 11:04:04] [info] Database connected successfully
[2025-06-08 11:06:29] [info] Database connected successfully
[2025-06-08 11:17:02] [info] Database connected successfully
[2025-06-08 11:17:15] [info] Database connected successfully
[2025-06-08 11:23:54] [info] Database connected successfully
[2025-06-08 11:24:05] [info] Database connected successfully
[2025-06-08 11:27:44] [info] Database connected successfully
[2025-06-08 11:45:38] [info] Database connected successfully
[2025-06-08 11:46:01] [info] Database connected successfully
[2025-06-08 11:46:08] [info] Database connected successfully
[2025-06-08 11:55:30] [info] Database connected successfully
[2025-06-08 11:55:41] [info] Database connected successfully
[2025-06-08 12:06:01] [info] Database connected successfully
[2025-06-08 12:06:13] [info] Database connected successfully
[2025-06-08 12:13:01] [info] Database connected successfully
[2025-06-08 12:13:11] [info] Database connected successfully
[2025-06-08 12:19:47] [info] Database connected successfully
[2025-06-08 12:20:23] [info] Database connected successfully
[2025-06-08 12:26:16] [info] Database connected successfully
[2025-06-08 12:26:29] [info] Database connected successfully
[2025-06-08 12:27:27] [info] Database connected successfully
[2025-06-08 12:35:20] [info] Database connected successfully
[2025-06-08 12:35:33] [info] Database connected successfully
[2025-06-08 12:36:52] [info] Database connected successfully
[2025-06-08 12:38:44] [info] Database connected successfully
[2025-06-08 12:38:50] [info] Database connected successfully
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'companies' already exists SQL: -- Create companies table
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    storage_used BIGINT DEFAULT 0,
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_status (status),
    INDEX idx_companies_created (created_at)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists SQL: -- Create users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer', 'client') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_users_company (company_id),
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    IND...' at line 20 SQL: -- Create warehouses table
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    INDEX idx_warehouses_company (company_id),
    INDEX idx_warehouses_manager (manager_id),
    INDEX idx_warehouses_status (status),
    INDEX idx_warehouses_created (created_at)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'storage_locations' already exists SQL: -- Create storage_locations table
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_location (warehouse_id, identifier),
    INDEX idx_locations_warehouse (warehouse_id),
    INDEX idx_locations_parent (parent_id),
    INDEX idx_locations_type (type),
    INDEX idx_locations_barcode (barcode_value),
    INDEX idx_locations_status (status),
    INDEX idx_locations_hierarchy (warehouse_id, parent_id, type)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'document_categories' already exists SQL: -- Create document_categories table
CREATE TABLE document_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES document_categories(id) ON DELETE CASCADE,
    
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_sort (sort_order),
    INDEX idx_categories_system (is_system)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY ...' at line 39 SQL: -- Create documents table
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL, -- For version control
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash
    version VARCHAR(20) DEFAULT '1.0',
    is_latest_version BOOLEAN DEFAULT TRUE,
    document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
    category_id INT,
    tags JSON,
    metadata JSON,
    ocr_text LONGTEXT,
    thumbnail_path VARCHAR(500),
    preview_path VARCHAR(500),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    access_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    retention_date DATE,
    expiry_date DATE,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES document_categories(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    
    INDEX idx_documents_company (company_id),
    INDEX idx_documents_parent (parent_id),
    INDEX idx_documents_category (category_id),
    INDEX idx_documents_creator (created_by),
    INDEX idx_documents_type (document_type),
    INDEX idx_documents_status (status),
    INDEX idx_documents_version (is_latest_version),
    INDEX idx_documents_created (created_at),
    INDEX idx_documents_hash (file_hash),
    INDEX idx_documents_company_search (company_id, status, created_at),
    
    FULLTEXT KEY ft_documents_content (title, description, ocr_text)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx...' at line 16 SQL: -- Create document_locations table
CREATE TABLE document_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    document_id INT NOT NULL,
    location_id INT,
    storage_type ENUM('physical', 'digital', 'hybrid') NOT NULL,
    physical_reference VARCHAR(255), -- Box number, shelf reference, etc.
    digital_path VARCHAR(1000),
    quantity INT DEFAULT 1,
    condition_notes TEXT,
    moved_by INT NOT NULL,
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx_doc_locations_document (document_id),
    INDEX idx_doc_locations_location (location_id),
    INDEX idx_doc_locations_current (is_current),
    INDEX idx_doc_locations_moved (moved_at),
    INDEX idx_doc_locations_type (storage_type)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'barcodes' already exists SQL: -- Create barcodes table
CREATE TABLE barcodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('document', 'location', 'box', 'bundle') NOT NULL,
    entity_id INT NOT NULL,
    barcode_value VARCHAR(255) UNIQUE NOT NULL,
    barcode_type ENUM('qr', 'code128', 'code39', 'ean13', 'datamatrix') DEFAULT 'qr',
    barcode_image_path VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    generated_by INT NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_scanned_at TIMESTAMP NULL,
    scan_count INT DEFAULT 0,
    
    FOREIGN KEY (generated_by) REFERENCES users(id),
    
    INDEX idx_barcodes_value (barcode_value),
    INDEX idx_barcodes_entity (entity_type, entity_id),
    INDEX idx_barcodes_active (is_active),
    INDEX idx_barcodes_generated (generated_at),
    INDEX idx_barcodes_scan (barcode_value, is_active)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_type' SQL: -- Service Provider Model Enhancements
-- Updates to support third-party document storage service provider business model

-- Add storage type to documents table (physical vs online)
ALTER TABLE documents 
ADD COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER location_id,
ADD COLUMN online_storage_path VARCHAR(500) NULL AFTER storage_type,
ADD COLUMN physical_location_notes TEXT NULL AFTER online_storage_path,
ADD INDEX idx_storage_type (storage_type),
ADD INDEX idx_company_storage (company_id, storage_type)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX i...' at line 15 SQL: -- Create bundles table
CREATE TABLE bundles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    bundle_type ENUM('intake', 'project', 'department', 'custom') DEFAULT 'custom',
    location_id INT,
    created_by INT NOT NULL,
    status ENUM('open', 'closed', 'archived') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_bundles_company (company_id),
    INDEX idx_bundles_location (location_id),
    INDEX idx_bundles_creator (created_by),
    INDEX idx_bundles_type (bundle_type),
    INDEX idx_bundles_status (status),
    INDEX idx_bundles_created (created_at)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    INDEX idx_audit_company (company_id),
    INDEX idx_audit_...' at line 17 SQL: -- Create audit_logs table
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    user_id INT,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    action ENUM('create', 'read', 'update', 'delete', 'login', 'logout', 'download', 'upload', 'scan') NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON SET NULL,
    
    INDEX idx_audit_company (company_id),
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_entity (entity_type, entity_id),
    INDEX idx_audit_action (action),
    INDEX idx_audit_created (created_at),
    INDEX idx_audit_logs_search (company_id, entity_type, action, created_at),
    INDEX idx_audit_company_date (company_id, created_at),
    INDEX idx_audit_user_action (user_id, action, created_at)
)
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails SQL: -- Update bundles table to match controller expectations
-- Drop and recreate bundles table with proper structure

DROP TABLE IF EXISTS bundles
[2025-06-08 12:38:50] [error] SQL execution failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'box_id' in 'document_intake' SQL: -- Enhanced Intake Workflow Migration
-- Adds missing columns for physical document management and enhanced tracking

-- Add enhanced tracking columns to document_intake table
ALTER TABLE document_intake 
ADD COLUMN physical_intake_code VARCHAR(100) NULL AFTER reference_number,
ADD COLUMN digitization_required BOOLEAN DEFAULT FALSE AFTER box_id,
ADD COLUMN digitization_status ENUM('pending', 'in_progress', 'completed', 'not_required') DEFAULT 'not_required' AFTER digitization_required,
ADD COLUMN barcode_value VARCHAR(255) NULL AFTER digitization_status,
ADD COLUMN qr_code_value TEXT NULL AFTER barcode_value,
ADD COLUMN barcode_generated_at TIMESTAMP NULL AFTER qr_code_value,
ADD COLUMN retention_period_years INT DEFAULT 7 AFTER barcode_generated_at,
ADD COLUMN destruction_date DATE NULL AFTER retention_period_years,
ADD COLUMN physical_location VARCHAR(255) NULL AFTER destruction_date,
ADD COLUMN sorting_category VARCHAR(100) NULL AFTER physical_location
[2025-06-08 12:39:44] [info] Database connected successfully
[2025-06-08 12:40:05] [info] Database connected successfully
[2025-06-08 12:40:31] [info] Database connected successfully
[2025-06-08 12:41:12] [info] Database connected successfully
[2025-06-08 12:41:41] [info] Database connected successfully
[2025-06-08 12:44:05] [info] Database connected successfully
[2025-06-08 12:46:19] [info] Database connected successfully
[2025-06-08 12:46:31] [info] Database connected successfully
[2025-06-08 12:48:58] [info] Database connected successfully
[2025-06-08 12:51:08] [info] Database connected successfully
[2025-06-08 12:51:08] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'storage_type' in 'field list' SQL: INSERT INTO boxes (
                    company_id, warehouse_id, box_id, name, description,
                    storage_type, capacity, current_count, barcode_value,
                    status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, 'empty', ?, NOW(), NOW())
[2025-06-08 12:51:08] [info] Database connected successfully
[2025-06-08 12:51:35] [info] Database connected successfully
[2025-06-08 12:51:38] [info] Database connected successfully
[2025-06-08 12:51:47] [info] Database connected successfully
[2025-06-08 12:51:55] [info] Database connected successfully
[2025-06-08 12:51:57] [info] Database connected successfully
[2025-06-08 12:52:02] [info] Database connected successfully
[2025-06-08 12:52:05] [info] Database connected successfully
[2025-06-08 12:52:29] [info] Database connected successfully
[2025-06-08 12:52:29] [info] Database connected successfully
[2025-06-08 12:52:33] [info] Database connected successfully
[2025-06-08 12:52:33] [info] Database connected successfully
[2025-06-08 12:52:36] [info] Database connected successfully
[2025-06-08 12:52:36] [info] Database connected successfully
[2025-06-08 12:52:43] [info] Database connected successfully
[2025-06-08 12:52:43] [info] Database connected successfully
[2025-06-08 12:52:47] [info] Database connected successfully
[2025-06-08 12:52:47] [info] Database connected successfully
[2025-06-08 12:52:51] [info] Database connected successfully
[2025-06-08 12:52:54] [info] Database connected successfully
[2025-06-08 12:52:56] [info] Database connected successfully
[2025-06-08 12:52:59] [info] Database connected successfully
[2025-06-08 12:52:59] [info] Database connected successfully
[2025-06-08 12:53:10] [info] Database connected successfully
[2025-06-08 12:53:13] [info] Database connected successfully
[2025-06-08 12:53:26] [info] Database connected successfully
[2025-06-08 12:55:42] [info] Database connected successfully
[2025-06-08 12:58:22] [info] Database connected successfully
[2025-06-08 12:58:22] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'storage_type' in 'field list' SQL: INSERT INTO boxes (
                    company_id, warehouse_id, box_id, name, description,
                    storage_type, capacity, current_count, barcode_value,
                    status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, 'empty', ?, NOW(), NOW())
[2025-06-08 12:58:22] [info] Database connected successfully
[2025-06-08 12:59:49] [info] Database connected successfully
[2025-06-08 13:00:08] [info] Database connected successfully
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'companies' already exists SQL: -- Create companies table
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    logo_path VARCHAR(500),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    settings JSON,
    subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    storage_limit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    storage_used BIGINT DEFAULT 0,
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_status (status),
    INDEX idx_companies_created (created_at)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists SQL: -- Create users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('super_admin', 'company_admin', 'manager', 'editor', 'viewer', 'client') NOT NULL,
    permissions JSON,
    avatar_path VARCHAR(500),
    phone VARCHAR(20),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    
    INDEX idx_users_company (company_id),
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created (created_at)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    IND...' at line 20 SQL: -- Create warehouses table
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    address TEXT,
    manager_id INT,
    total_capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    capacity_unit ENUM('cubic_meters', 'square_meters', 'shelves', 'boxes') DEFAULT 'cubic_meters',
    coordinates JSON, -- {lat: 0, lng: 0}
    settings JSON,
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON SET NULL,
    
    UNIQUE KEY unique_company_code (company_id, code),
    INDEX idx_warehouses_company (company_id),
    INDEX idx_warehouses_manager (manager_id),
    INDEX idx_warehouses_status (status),
    INDEX idx_warehouses_created (created_at)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'storage_locations' already exists SQL: -- Create storage_locations table
CREATE TABLE storage_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    warehouse_id INT NOT NULL,
    parent_id INT NULL,
    type ENUM('building', 'floor', 'room', 'aisle', 'rack', 'shelf', 'box') NOT NULL,
    identifier VARCHAR(100) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    capacity DECIMAL(10,2),
    used_capacity DECIMAL(10,2) DEFAULT 0,
    dimensions JSON, -- {width: 0, height: 0, depth: 0}
    coordinates JSON, -- {x: 0, y: 0, z: 0}
    barcode_value VARCHAR(255) UNIQUE,
    access_level ENUM('public', 'restricted', 'private') DEFAULT 'public',
    temperature_controlled BOOLEAN DEFAULT FALSE,
    humidity_controlled BOOLEAN DEFAULT FALSE,
    security_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'full', 'maintenance') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES storage_locations(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_location (warehouse_id, identifier),
    INDEX idx_locations_warehouse (warehouse_id),
    INDEX idx_locations_parent (parent_id),
    INDEX idx_locations_type (type),
    INDEX idx_locations_barcode (barcode_value),
    INDEX idx_locations_status (status),
    INDEX idx_locations_hierarchy (warehouse_id, parent_id, type)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'document_categories' already exists SQL: -- Create document_categories table
CREATE TABLE document_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES document_categories(id) ON DELETE CASCADE,
    
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_sort (sort_order),
    INDEX idx_categories_system (is_system)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY ...' at line 39 SQL: -- Create documents table
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    parent_id INT NULL, -- For version control
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash
    version VARCHAR(20) DEFAULT '1.0',
    is_latest_version BOOLEAN DEFAULT TRUE,
    document_type ENUM('contract', 'invoice', 'report', 'image', 'video', 'audio', 'other') DEFAULT 'other',
    category_id INT,
    tags JSON,
    metadata JSON,
    ocr_text LONGTEXT,
    thumbnail_path VARCHAR(500),
    preview_path VARCHAR(500),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    access_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal',
    retention_date DATE,
    expiry_date DATE,
    created_by INT NOT NULL,
    updated_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    status ENUM('draft', 'pending', 'approved', 'rejected', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES document_categories(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    
    INDEX idx_documents_company (company_id),
    INDEX idx_documents_parent (parent_id),
    INDEX idx_documents_category (category_id),
    INDEX idx_documents_creator (created_by),
    INDEX idx_documents_type (document_type),
    INDEX idx_documents_status (status),
    INDEX idx_documents_version (is_latest_version),
    INDEX idx_documents_created (created_at),
    INDEX idx_documents_hash (file_hash),
    INDEX idx_documents_company_search (company_id, status, created_at),
    
    FULLTEXT KEY ft_documents_content (title, description, ocr_text)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx...' at line 16 SQL: -- Create document_locations table
CREATE TABLE document_locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    document_id INT NOT NULL,
    location_id INT,
    storage_type ENUM('physical', 'digital', 'hybrid') NOT NULL,
    physical_reference VARCHAR(255), -- Box number, shelf reference, etc.
    digital_path VARCHAR(1000),
    quantity INT DEFAULT 1,
    condition_notes TEXT,
    moved_by INT NOT NULL,
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (moved_by) REFERENCES users(id),
    
    INDEX idx_doc_locations_document (document_id),
    INDEX idx_doc_locations_location (location_id),
    INDEX idx_doc_locations_current (is_current),
    INDEX idx_doc_locations_moved (moved_at),
    INDEX idx_doc_locations_type (storage_type)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'barcodes' already exists SQL: -- Create barcodes table
CREATE TABLE barcodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('document', 'location', 'box', 'bundle') NOT NULL,
    entity_id INT NOT NULL,
    barcode_value VARCHAR(255) UNIQUE NOT NULL,
    barcode_type ENUM('qr', 'code128', 'code39', 'ean13', 'datamatrix') DEFAULT 'qr',
    barcode_image_path VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    generated_by INT NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_scanned_at TIMESTAMP NULL,
    scan_count INT DEFAULT 0,
    
    FOREIGN KEY (generated_by) REFERENCES users(id),
    
    INDEX idx_barcodes_value (barcode_value),
    INDEX idx_barcodes_entity (entity_type, entity_id),
    INDEX idx_barcodes_active (is_active),
    INDEX idx_barcodes_generated (generated_at),
    INDEX idx_barcodes_scan (barcode_value, is_active)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_type' SQL: -- Service Provider Model Enhancements
-- Updates to support third-party document storage service provider business model

-- Add storage type to documents table (physical vs online)
ALTER TABLE documents 
ADD COLUMN storage_type ENUM('physical', 'online') DEFAULT 'physical' AFTER location_id,
ADD COLUMN online_storage_path VARCHAR(500) NULL AFTER storage_type,
ADD COLUMN physical_location_notes TEXT NULL AFTER online_storage_path,
ADD INDEX idx_storage_type (storage_type),
ADD INDEX idx_company_storage (company_id, storage_type)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX i...' at line 15 SQL: -- Create bundles table
CREATE TABLE bundles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    bundle_type ENUM('intake', 'project', 'department', 'custom') DEFAULT 'custom',
    location_id INT,
    created_by INT NOT NULL,
    status ENUM('open', 'closed', 'archived') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES storage_locations(id) ON SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_bundles_company (company_id),
    INDEX idx_bundles_location (location_id),
    INDEX idx_bundles_creator (created_by),
    INDEX idx_bundles_type (bundle_type),
    INDEX idx_bundles_status (status),
    INDEX idx_bundles_created (created_at)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'SET NULL,
    
    INDEX idx_audit_company (company_id),
    INDEX idx_audit_...' at line 17 SQL: -- Create audit_logs table
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    user_id INT,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    action ENUM('create', 'read', 'update', 'delete', 'login', 'logout', 'download', 'upload', 'scan') NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON SET NULL,
    
    INDEX idx_audit_company (company_id),
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_entity (entity_type, entity_id),
    INDEX idx_audit_action (action),
    INDEX idx_audit_created (created_at),
    INDEX idx_audit_logs_search (company_id, entity_type, action, created_at),
    INDEX idx_audit_company_date (company_id, created_at),
    INDEX idx_audit_user_action (user_id, action, created_at)
)
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails SQL: -- Update bundles table to match controller expectations
-- Drop and recreate bundles table with proper structure

DROP TABLE IF EXISTS bundles
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'box_id' in 'document_intake' SQL: -- Enhanced Intake Workflow Migration
-- Adds missing columns for physical document management and enhanced tracking

-- Add enhanced tracking columns to document_intake table
ALTER TABLE document_intake 
ADD COLUMN physical_intake_code VARCHAR(100) NULL AFTER reference_number,
ADD COLUMN digitization_required BOOLEAN DEFAULT FALSE AFTER box_id,
ADD COLUMN digitization_status ENUM('pending', 'in_progress', 'completed', 'not_required') DEFAULT 'not_required' AFTER digitization_required,
ADD COLUMN barcode_value VARCHAR(255) NULL AFTER digitization_status,
ADD COLUMN qr_code_value TEXT NULL AFTER barcode_value,
ADD COLUMN barcode_generated_at TIMESTAMP NULL AFTER qr_code_value,
ADD COLUMN retention_period_years INT DEFAULT 7 AFTER barcode_generated_at,
ADD COLUMN destruction_date DATE NULL AFTER retention_period_years,
ADD COLUMN physical_location VARCHAR(255) NULL AFTER destruction_date,
ADD COLUMN sorting_category VARCHAR(100) NULL AFTER physical_location
[2025-06-08 13:00:08] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'emoji' SQL: -- Add emoji column to document_sorting_categories table
-- This migration fixes the missing emoji column error when creating custom sorting categories

-- Add emoji column
ALTER TABLE document_sorting_categories 
ADD COLUMN emoji VARCHAR(10) NULL AFTER color_code
[2025-06-08 13:00:29] [info] Database connected successfully
[2025-06-08 13:02:59] [info] Database connected successfully
[2025-06-08 13:03:59] [info] Database connected successfully
[2025-06-08 13:03:59] [error] Query failed: SQLSTATE[HY000]: General error: 1364 Field 'client_prefix' doesn't have a default value SQL: INSERT INTO boxes (
        company_id, warehouse_id, box_id, name, description,
        storage_type, capacity, current_count, barcode_value,
        status, created_by, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, 'empty', ?, NOW(), NOW())
[2025-06-08 13:05:08] [info] Database connected successfully
[2025-06-08 13:08:09] [info] Database connected successfully
[2025-06-08 13:08:43] [info] Database connected successfully
[2025-06-08 13:08:43] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list' SQL: INSERT INTO box_bundles (box_id, bundle_id, created_at) VALUES (?, ?, NOW())
[2025-06-08 13:09:17] [info] Database connected successfully
[2025-06-08 13:09:17] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'bundle_id' in 'where clause' SQL: SELECT * FROM boxes WHERE bundle_id = ?
[2025-06-08 13:09:49] [info] Database connected successfully
[2025-06-08 13:10:11] [info] Database connected successfully
[2025-06-08 13:11:33] [info] Database connected successfully
[2025-06-08 13:11:33] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list' SQL: INSERT INTO box_bundles (box_id, bundle_id, created_at) VALUES (?, ?, NOW())
[2025-06-08 13:11:42] [info] Database connected successfully
[2025-06-08 13:12:02] [info] Database connected successfully
[2025-06-08 13:12:34] [info] Database connected successfully
[2025-06-08 13:13:30] [info] Database connected successfully
[2025-06-08 13:15:52] [info] Database connected successfully
[2025-06-08 13:16:35] [info] Database connected successfully
[2025-06-08 13:17:19] [info] Database connected successfully
[2025-06-08 13:21:39] [info] Database connected successfully
[2025-06-08 13:21:49] [info] Database connected successfully
[2025-06-08 13:22:00] [info] Database connected successfully
[2025-06-08 13:23:05] [info] Database connected successfully
[2025-06-08 13:24:37] [info] Database connected successfully
[2025-06-08 13:24:37] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list' SQL: INSERT INTO box_bundles (box_id, bundle_id, created_at) VALUES (?, ?, NOW())
[2025-06-08 13:24:37] [info] Database connected successfully
[2025-06-08 13:25:01] [info] Database connected successfully
[2025-06-08 13:25:09] [info] Database connected successfully
[2025-06-08 13:25:15] [info] Database connected successfully
[2025-06-08 13:25:20] [info] Database connected successfully
[2025-06-08 13:25:39] [info] Database connected successfully
[2025-06-08 13:25:39] [info] Database connected successfully
[2025-06-08 13:25:51] [info] Database connected successfully
[2025-06-08 13:25:57] [info] Database connected successfully
[2025-06-08 13:26:14] [info] Database connected successfully
[2025-06-08 13:26:51] [info] Database connected successfully
[2025-06-08 13:27:12] [info] Database connected successfully
[2025-06-08 13:27:13] [info] Database connected successfully
[2025-06-08 13:27:15] [info] Database connected successfully
[2025-06-08 13:27:16] [info] Database connected successfully
[2025-06-08 13:27:25] [info] Database connected successfully
[2025-06-08 13:27:25] [info] Database connected successfully
[2025-06-08 13:27:32] [info] Database connected successfully
[2025-06-08 13:27:32] [info] Database connected successfully
[2025-06-08 13:27:35] [info] Database connected successfully
[2025-06-08 13:27:37] [info] Database connected successfully
[2025-06-08 13:27:39] [info] Database connected successfully
[2025-06-08 13:27:43] [info] Database connected successfully
[2025-06-08 13:28:13] [info] Database connected successfully
[2025-06-08 13:28:23] [info] Database connected successfully
[2025-06-08 13:28:32] [info] Database connected successfully
[2025-06-08 13:28:56] [info] Database connected successfully
[2025-06-08 13:30:08] [info] Database connected successfully
[2025-06-08 13:37:25] [info] Database connected successfully
[2025-06-08 13:37:32] [info] Database connected successfully
[2025-06-08 13:38:40] [info] Database connected successfully
[2025-06-08 13:38:43] [info] Database connected successfully
[2025-06-08 13:39:06] [info] Database connected successfully
[2025-06-08 13:40:21] [info] Database connected successfully
[2025-06-08 13:56:08] [info] Database connected successfully
[2025-06-08 13:57:27] [info] Database connected successfully
[2025-06-08 14:04:33] [info] Database connected successfully
[2025-06-08 14:06:01] [info] Database connected successfully
[2025-06-08 14:06:03] [info] Database connected successfully
[2025-06-08 14:06:14] [info] Database connected successfully
[2025-06-08 14:06:19] [info] Database connected successfully
[2025-06-08 14:06:29] [info] Database connected successfully
[2025-06-08 14:06:32] [info] Database connected successfully
[2025-06-08 14:06:50] [info] Database connected successfully
[2025-06-08 14:06:53] [info] Database connected successfully
[2025-06-08 14:07:05] [info] Database connected successfully
[2025-06-08 14:07:17] [info] Database connected successfully
[2025-06-08 14:07:24] [info] Database connected successfully
[2025-06-08 14:07:36] [info] Database connected successfully
[2025-06-08 14:07:40] [info] Database connected successfully
[2025-06-08 14:11:28] [info] Database connected successfully
[2025-06-08 14:11:32] [info] Database connected successfully
[2025-06-08 14:16:02] [info] Database connected successfully
[2025-06-08 14:29:59] [info] Database connected successfully
[2025-06-08 14:31:16] [info] Database connected successfully
[2025-06-08 14:31:24] [info] Database connected successfully
[2025-06-08 14:32:40] [info] Database connected successfully
[2025-06-08 14:32:43] [info] Database connected successfully
[2025-06-08 14:33:37] [info] Database connected successfully
[2025-06-08 14:35:51] [info] Database connected successfully
[2025-06-08 14:35:53] [info] Database connected successfully
[2025-06-08 14:40:30] [info] Database connected successfully
[2025-06-08 14:40:49] [info] Database connected successfully
[2025-06-08 14:41:56] [info] Database connected successfully
[2025-06-08 14:44:24] [info] Database connected successfully
[2025-06-08 14:44:48] [info] Database connected successfully
[2025-06-08 14:44:56] [info] Database connected successfully
[2025-06-08 14:45:02] [info] Database connected successfully
[2025-06-08 14:45:30] [info] Database connected successfully
[2025-06-08 14:45:32] [info] Database connected successfully
[2025-06-08 14:45:33] [info] Database connected successfully
[2025-06-08 14:45:38] [info] Database connected successfully
[2025-06-08 14:45:41] [info] Database connected successfully
[2025-06-08 14:45:42] [info] Database connected successfully
[2025-06-08 14:45:45] [info] Database connected successfully
[2025-06-08 14:45:45] [info] Database connected successfully
[2025-06-08 14:45:54] [info] Database connected successfully
[2025-06-08 14:45:58] [info] Database connected successfully
[2025-06-08 14:46:10] [info] Database connected successfully
[2025-06-08 14:46:16] [info] Database connected successfully
[2025-06-08 14:47:31] [info] Database connected successfully
[2025-06-08 15:13:32] [info] Database connected successfully
[2025-06-08 15:14:08] [info] Database connected successfully
[2025-06-08 15:14:14] [info] Database connected successfully
[2025-06-08 15:14:16] [info] Database connected successfully
[2025-06-08 15:14:54] [info] Database connected successfully
[2025-06-08 15:14:54] [info] Database connected successfully
[2025-06-08 15:15:03] [info] Database connected successfully
[2025-06-08 15:15:05] [info] Database connected successfully
[2025-06-08 15:15:06] [info] Database connected successfully
[2025-06-08 15:15:08] [info] Database connected successfully
[2025-06-08 15:15:10] [info] Database connected successfully
[2025-06-08 15:17:19] [info] Database connected successfully
[2025-06-08 15:17:27] [info] Database connected successfully
[2025-06-08 15:17:35] [info] Database connected successfully
[2025-06-08 15:17:59] [info] Database connected successfully
[2025-06-08 15:20:16] [info] Database connected successfully
[2025-06-08 15:20:27] [info] Database connected successfully
[2025-06-08 15:21:36] [info] Database connected successfully
[2025-06-08 15:21:47] [info] Database connected successfully
[2025-06-08 15:25:45] [info] Database connected successfully
[2025-06-08 15:26:06] [info] Database connected successfully
[2025-06-08 15:26:06] [info] Database connected successfully
[2025-06-08 15:27:06] [info] Database connected successfully
[2025-06-08 15:27:08] [info] Database connected successfully
[2025-06-08 15:27:10] [info] Database connected successfully
[2025-06-08 15:32:23] [info] Database connected successfully
[2025-06-08 15:32:23] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'bb.created_at' in 'field list' SQL: SELECT bun.*,
                    bb.position_in_box,
                    bb.created_at as added_at,
                    bb.added_by,
                    COUNT(DISTINCT d.id) as document_count,
                    SUM(d.file_size) as total_size,
                    u.first_name,
                    u.last_name,
                    u2.first_name as added_by_first_name,
                    u2.last_name as added_by_last_name
             FROM box_bundles bb
             JOIN bundles bun ON bb.bundle_id = bun.id
             LEFT JOIN documents d ON bun.id = d.bundle_id AND d.status != 'deleted'
             LEFT JOIN users u ON bun.created_by = u.id
             LEFT JOIN users u2 ON bb.added_by = u2.id
             WHERE bb.box_id = ? AND bun.status IN ('active', 'open')
             GROUP BY bun.id, bb.position_in_box, bb.created_at, bb.added_by
             ORDER BY bb.position_in_box ASC, bb.created_at ASC
[2025-06-08 15:32:23] [info] Database connected successfully
[2025-06-08 15:32:40] [info] Database connected successfully
[2025-06-08 15:32:40] [info] Database connected successfully
[2025-06-08 16:56:18] [info] Database connected successfully
[2025-06-08 16:56:22] [info] Database connected successfully
[2025-06-08 16:56:23] [info] Database connected successfully
[2025-06-08 16:56:26] [info] Database connected successfully
[2025-06-08 16:56:28] [info] Database connected successfully
[2025-06-08 16:56:30] [info] Database connected successfully
[2025-06-08 16:56:32] [info] Database connected successfully
[2025-06-08 19:30:08] [info] Database connected successfully
[2025-06-08 19:30:20] [info] Database connected successfully
[2025-06-08 19:30:30] [info] Database connected successfully
[2025-06-08 19:31:05] [info] Database connected successfully
[2025-06-08 19:31:23] [info] Database connected successfully
[2025-06-08 19:31:53] [info] Database connected successfully
[2025-06-08 19:33:02] [info] Database connected successfully
[2025-06-08 19:35:11] [info] Database connected successfully
[2025-06-08 19:35:50] [info] Database connected successfully
[2025-06-08 19:37:00] [info] Database connected successfully
[2025-06-08 19:37:16] [info] Database connected successfully
[2025-06-08 19:39:02] [info] Database connected successfully
[2025-06-08 19:41:26] [info] Database connected successfully
[2025-06-08 19:41:27] [error] Query failed: SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (`dms_system`.`box_movements`, CONSTRAINT `box_movements_ibfk_2` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`)) SQL: DELETE FROM users
[2025-06-08 19:41:50] [info] Database connected successfully
[2025-06-08 19:43:16] [info] Database connected successfully
[2025-06-08 19:43:16] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.activity_logs' doesn't exist SQL: SELECT COUNT(*) FROM activity_logs WHERE user_id IS NOT NULL
[2025-06-08 19:43:16] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'role' at row 1 SQL: INSERT INTO users (company_id, first_name, last_name, email, username, password_hash, role, status, email_verified, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
[2025-06-08 19:43:38] [info] Database connected successfully
[2025-06-08 19:44:01] [info] Database connected successfully
[2025-06-08 19:44:24] [info] Database connected successfully
[2025-06-08 19:46:57] [info] Database connected successfully
[2025-06-08 19:47:16] [info] Database connected successfully
[2025-06-08 19:47:34] [info] Database connected successfully
[2025-06-08 19:47:42] [info] Database connected successfully
[2025-06-08 19:48:20] [info] Database connected successfully
[2025-06-08 19:48:33] [info] Database connected successfully
[2025-06-08 19:48:57] [info] Database connected successfully
[2025-06-08 19:49:07] [info] Database connected successfully
[2025-06-08 19:54:07] [info] Database connected successfully
[2025-06-08 19:54:29] [info] Database connected successfully
[2025-06-08 19:54:45] [info] Database connected successfully
[2025-06-08 19:55:01] [info] Database connected successfully
[2025-06-08 19:55:15] [info] Database connected successfully
[2025-06-08 19:55:15] [info] Database connected successfully
[2025-06-08 19:55:58] [info] Database connected successfully
[2025-06-08 19:57:04] [info] Database connected successfully
[2025-06-08 19:57:08] [info] Database connected successfully
[2025-06-08 19:57:10] [info] Database connected successfully
[2025-06-08 19:57:13] [info] Database connected successfully
[2025-06-08 19:57:25] [info] Database connected successfully
[2025-06-08 19:57:25] [info] Database connected successfully
[2025-06-08 19:57:34] [info] Database connected successfully
[2025-06-08 19:57:34] [info] Database connected successfully
[2025-06-08 19:57:46] [info] Database connected successfully
[2025-06-08 19:59:55] [info] Database connected successfully
[2025-06-08 19:59:55] [info] Database connected successfully
[2025-06-08 20:00:45] [info] Database connected successfully
[2025-06-08 20:00:45] [info] Database connected successfully
[2025-06-08 20:00:58] [info] Database connected successfully
[2025-06-08 20:00:58] [info] Database connected successfully
[2025-06-08 20:01:07] [info] Database connected successfully
[2025-06-08 20:01:17] [info] Database connected successfully
[2025-06-08 20:01:23] [info] Database connected successfully
[2025-06-08 20:01:23] [info] Database connected successfully
[2025-06-08 20:01:40] [info] Database connected successfully
[2025-06-08 20:01:42] [info] Database connected successfully
[2025-06-08 20:01:53] [info] Database connected successfully
[2025-06-08 20:01:54] [info] Database connected successfully
[2025-06-08 20:01:57] [info] Database connected successfully
[2025-06-08 20:01:58] [info] Database connected successfully
[2025-06-08 20:02:17] [info] Database connected successfully
[2025-06-08 20:02:22] [info] Database connected successfully
[2025-06-08 20:02:28] [info] Database connected successfully
[2025-06-08 20:02:36] [info] Database connected successfully
[2025-06-08 20:02:37] [info] Database connected successfully
[2025-06-08 20:02:39] [info] Database connected successfully
[2025-06-08 20:02:43] [info] Database connected successfully
[2025-06-08 20:02:43] [info] Database connected successfully
[2025-06-08 20:02:45] [info] Database connected successfully
[2025-06-08 20:02:46] [info] Database connected successfully
[2025-06-08 20:02:47] [info] Database connected successfully
[2025-06-08 20:02:53] [info] Database connected successfully
[2025-06-08 20:02:53] [info] Database connected successfully
[2025-06-08 20:02:55] [info] Database connected successfully
[2025-06-08 20:02:57] [info] Database connected successfully
[2025-06-08 20:02:58] [info] Database connected successfully
[2025-06-08 20:02:58] [info] Database connected successfully
[2025-06-08 20:03:16] [info] Database connected successfully
[2025-06-08 20:03:17] [info] Database connected successfully
[2025-06-08 20:03:19] [info] Database connected successfully
[2025-06-08 20:07:19] [info] Database connected successfully
[2025-06-08 20:08:23] [info] Database connected successfully
[2025-06-08 20:08:32] [info] Database connected successfully
[2025-06-08 20:08:41] [info] Database connected successfully
[2025-06-08 20:11:11] [info] Database connected successfully
[2025-06-08 20:11:21] [info] Database connected successfully
[2025-06-08 20:11:50] [info] Database connected successfully
[2025-06-08 20:12:20] [info] Database connected successfully
[2025-06-08 20:12:27] [info] Database connected successfully
[2025-06-08 20:14:48] [info] Database connected successfully
[2025-06-08 20:14:48] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'identifier' in 'field list' SQL: SELECT id, name, identifier FROM boxes LIMIT 5
[2025-06-08 20:16:46] [info] Database connected successfully
[2025-06-08 20:17:16] [info] Database connected successfully
[2025-06-08 20:17:21] [info] Database connected successfully
[2025-06-08 20:19:39] [info] Database connected successfully
[2025-06-08 20:22:24] [info] Database connected successfully
[2025-06-08 20:23:03] [info] Database connected successfully
[2025-06-08 20:23:10] [info] Database connected successfully
[2025-06-08 20:23:16] [info] Database connected successfully
[2025-06-08 20:23:33] [info] Database connected successfully
[2025-06-08 20:23:39] [info] Database connected successfully
[2025-06-08 20:23:42] [info] Database connected successfully
[2025-06-08 20:23:46] [info] Database connected successfully
[2025-06-08 20:23:47] [info] Database connected successfully
[2025-06-08 20:25:53] [info] Database connected successfully
[2025-06-08 20:25:53] [info] Database connected successfully
[2025-06-08 20:26:46] [info] Database connected successfully
[2025-06-08 20:26:49] [info] Database connected successfully
[2025-06-08 20:27:22] [info] Database connected successfully
[2025-06-08 20:27:25] [info] Database connected successfully
[2025-06-08 20:32:27] [info] Database connected successfully
[2025-06-08 20:33:02] [info] Database connected successfully
[2025-06-08 21:38:14] [info] Database connected successfully
[2025-06-08 21:38:17] [info] Database connected successfully
[2025-06-08 21:38:30] [info] Database connected successfully
[2025-06-08 21:38:41] [info] Database connected successfully
[2025-06-08 21:38:42] [info] Database connected successfully
[2025-06-08 21:39:27] [info] Database connected successfully
[2025-06-08 21:39:29] [info] Database connected successfully
[2025-06-08 21:39:31] [info] Database connected successfully
[2025-06-08 21:39:33] [info] Database connected successfully
[2025-06-08 21:39:36] [info] Database connected successfully
[2025-06-08 21:45:06] [info] Database connected successfully
[2025-06-08 21:46:34] [info] Database connected successfully
[2025-06-08 21:48:25] [info] Database connected successfully
[2025-06-08 21:51:56] [info] Database connected successfully
[2025-06-08 21:52:42] [info] Database connected successfully
[2025-06-08 21:52:52] [info] Database connected successfully
[2025-06-08 21:54:39] [info] Database connected successfully
[2025-06-08 21:57:10] [info] Database connected successfully
[2025-06-08 21:57:10] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list' SQL: INSERT INTO box_bundles (box_id, bundle_id, position_in_box, added_by, created_at) VALUES (?, ?, ?, ?, NOW())
[2025-06-08 21:57:11] [info] Database connected successfully
[2025-06-08 22:00:19] [info] Database connected successfully
[2025-06-08 22:00:22] [info] Database connected successfully
[2025-06-08 22:01:01] [info] Database connected successfully
[2025-06-08 22:01:05] [info] Database connected successfully
[2025-06-08 22:01:12] [info] Database connected successfully
[2025-06-08 22:01:34] [info] Database connected successfully
[2025-06-08 22:01:45] [info] Database connected successfully
[2025-06-08 22:01:52] [info] Database connected successfully
[2025-06-08 22:04:40] [info] Database connected successfully
[2025-06-08 22:04:47] [info] Database connected successfully
[2025-06-08 22:04:47] [info] Database connected successfully
[2025-06-08 22:04:49] [info] Database connected successfully
[2025-06-08 22:04:50] [info] Database connected successfully
[2025-06-08 22:04:54] [info] Database connected successfully
[2025-06-08 22:04:54] [info] Database connected successfully
[2025-06-08 22:04:57] [info] Database connected successfully
[2025-06-08 22:05:03] [info] Database connected successfully
[2025-06-08 22:05:04] [info] Database connected successfully
[2025-06-08 22:05:06] [info] Database connected successfully
[2025-06-08 22:08:49] [info] Database connected successfully
[2025-06-08 22:13:02] [info] Database connected successfully
[2025-06-08 22:16:54] [info] Database connected successfully
[2025-06-08 22:49:36] [info] Database connected successfully
[2025-06-08 22:53:20] [info] Database connected successfully
[2025-06-08 22:53:20] [info] Database connected successfully
[2025-06-08 22:54:34] [info] Database connected successfully
[2025-06-08 22:55:12] [info] Database connected successfully
[2025-06-08 22:59:07] [info] Database connected successfully
[2025-06-08 22:59:07] [info] Database connected successfully
[2025-06-08 22:59:27] [info] Database connected successfully
[2025-06-08 22:59:27] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'bb.created_at' in 'field list' SQL: SELECT b.*,
                    bb.position_in_box,
                    bb.created_at as added_at,
                    bb.added_by,
                    w.name as warehouse_name,
                    w.address as warehouse_address,
                    w.city as warehouse_city,
                    w.state as warehouse_state,
                    COUNT(DISTINCT d.id) as document_count_in_bundle,
                    SUM(d.file_size) as bundle_size_in_box,
                    u.first_name as added_by_first_name,
                    u.last_name as added_by_last_name
             FROM box_bundles bb
             JOIN boxes b ON bb.box_id = b.id
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN documents d ON d.bundle_id = ? AND d.status != 'deleted'
             LEFT JOIN users u ON bb.added_by = u.id
             WHERE bb.bundle_id = ?
             GROUP BY b.id, bb.position_in_box, bb.created_at, bb.added_by
             ORDER BY bb.created_at DESC
[2025-06-08 22:59:27] [info] Database connected successfully
[2025-06-08 22:59:38] [info] Database connected successfully
[2025-06-08 22:59:38] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'bb.created_at' in 'field list' SQL: SELECT b.*,
                    bb.position_in_box,
                    bb.created_at as added_at,
                    bb.added_by,
                    w.name as warehouse_name,
                    w.address as warehouse_address,
                    w.city as warehouse_city,
                    w.state as warehouse_state,
                    COUNT(DISTINCT d.id) as document_count_in_bundle,
                    SUM(d.file_size) as bundle_size_in_box,
                    u.first_name as added_by_first_name,
                    u.last_name as added_by_last_name
             FROM box_bundles bb
             JOIN boxes b ON bb.box_id = b.id
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN documents d ON d.bundle_id = ? AND d.status != 'deleted'
             LEFT JOIN users u ON bb.added_by = u.id
             WHERE bb.bundle_id = ?
             GROUP BY b.id, bb.position_in_box, bb.created_at, bb.added_by
             ORDER BY bb.created_at DESC
[2025-06-08 22:59:38] [info] Database connected successfully
[2025-06-08 23:04:12] [info] Database connected successfully
[2025-06-08 23:04:32] [info] Database connected successfully
[2025-06-08 23:04:32] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'bb.created_at' in 'order clause' SQL: SELECT b.*,
                    bb.position_in_box,
                    bb.added_at,
                    bb.added_by,
                    w.name as warehouse_name,
                    w.address as warehouse_address,
                    w.city as warehouse_city,
                    w.state as warehouse_state,
                    COUNT(DISTINCT d.id) as document_count_in_bundle,
                    SUM(d.file_size) as bundle_size_in_box,
                    u.first_name as added_by_first_name,
                    u.last_name as added_by_last_name
             FROM box_bundles bb
             JOIN boxes b ON bb.box_id = b.id
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN documents d ON d.bundle_id = ? AND d.status != 'deleted'
             LEFT JOIN users u ON bb.added_by = u.id
             WHERE bb.bundle_id = ?
             GROUP BY b.id, bb.position_in_box, bb.created_at, bb.added_by
             ORDER BY bb.created_at DESC
[2025-06-08 23:04:32] [info] Database connected successfully
[2025-06-08 23:04:46] [info] Database connected successfully
[2025-06-08 23:04:46] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'bb.created_at' in 'order clause' SQL: SELECT b.*,
                    bb.position_in_box,
                    bb.added_at,
                    bb.added_by,
                    w.name as warehouse_name,
                    w.address as warehouse_address,
                    w.city as warehouse_city,
                    w.state as warehouse_state,
                    COUNT(DISTINCT d.id) as document_count_in_bundle,
                    SUM(d.file_size) as bundle_size_in_box,
                    u.first_name as added_by_first_name,
                    u.last_name as added_by_last_name
             FROM box_bundles bb
             JOIN boxes b ON bb.box_id = b.id
             LEFT JOIN warehouses w ON b.warehouse_id = w.id
             LEFT JOIN documents d ON d.bundle_id = ? AND d.status != 'deleted'
             LEFT JOIN users u ON bb.added_by = u.id
             WHERE bb.bundle_id = ?
             GROUP BY b.id, bb.position_in_box, bb.created_at, bb.added_by
             ORDER BY bb.created_at DESC
[2025-06-08 23:04:46] [info] Database connected successfully
[2025-06-08 23:06:01] [info] Database connected successfully
[2025-06-08 23:10:27] [info] Database connected successfully
[2025-06-08 23:10:30] [info] Database connected successfully
[2025-06-08 23:14:31] [info] Database connected successfully
[2025-06-08 23:17:44] [info] Database connected successfully
[2025-06-08 23:20:05] [info] Database connected successfully
[2025-06-08 23:20:08] [info] Database connected successfully
[2025-06-08 23:20:14] [info] Database connected successfully
[2025-06-08 23:20:17] [info] Database connected successfully
[2025-06-08 23:20:19] [info] Database connected successfully
[2025-06-08 23:20:28] [info] Database connected successfully
[2025-06-08 23:22:34] [info] Database connected successfully
[2025-06-08 23:22:41] [info] Database connected successfully
[2025-06-08 23:23:39] [info] Database connected successfully
[2025-06-08 23:24:08] [info] Database connected successfully
[2025-06-08 23:28:43] [info] Database connected successfully
[2025-06-08 23:28:47] [info] Database connected successfully
[2025-06-08 23:29:03] [info] Database connected successfully
[2025-06-08 23:30:26] [info] Database connected successfully
[2025-06-08 23:30:30] [info] Database connected successfully
[2025-06-08 23:31:55] [info] Database connected successfully
[2025-06-08 23:33:02] [info] Database connected successfully
[2025-06-08 23:34:43] [info] Database connected successfully
[2025-06-08 23:34:52] [info] Database connected successfully
[2025-06-08 23:34:54] [info] Database connected successfully
[2025-06-08 23:35:26] [info] Database connected successfully
[2025-06-08 23:36:02] [info] Database connected successfully
[2025-06-08 23:36:09] [info] Database connected successfully
[2025-06-08 23:39:09] [info] Database connected successfully
[2025-06-08 23:39:24] [info] Database connected successfully
[2025-06-08 23:39:26] [info] Database connected successfully
[2025-06-08 23:39:28] [info] Database connected successfully
[2025-06-08 23:40:11] [info] Database connected successfully
[2025-06-08 23:40:31] [info] Database connected successfully
[2025-06-08 23:40:32] [info] Database connected successfully
[2025-06-08 23:40:51] [info] Database connected successfully
[2025-06-08 23:45:06] [info] Database connected successfully
[2025-06-08 23:46:26] [info] Database connected successfully
[2025-06-08 23:46:42] [info] Database connected successfully
[2025-06-08 23:49:06] [info] Database connected successfully
[2025-06-08 23:49:34] [info] Database connected successfully
[2025-06-08 23:50:04] [info] Database connected successfully
[2025-06-08 23:50:54] [info] Database connected successfully
[2025-06-08 23:51:02] [info] Database connected successfully
[2025-06-08 23:51:03] [info] Database connected successfully
[2025-06-08 23:51:05] [info] Database connected successfully
[2025-06-08 23:51:06] [info] Database connected successfully
[2025-06-08 23:51:36] [info] Database connected successfully
[2025-06-08 23:51:46] [info] Database connected successfully
[2025-06-08 23:53:19] [info] Database connected successfully
[2025-06-09 00:10:03] [info] Database connected successfully
[2025-06-09 00:16:18] [info] Database connected successfully
[2025-06-09 00:16:44] [info] Database connected successfully
[2025-06-09 00:16:48] [info] Database connected successfully
[2025-06-09 00:17:14] [info] Database connected successfully
[2025-06-09 00:19:11] [info] Database connected successfully
[2025-06-09 00:20:25] [info] Database connected successfully
[2025-06-09 00:28:56] [info] Database connected successfully
[2025-06-09 00:28:56] [info] Database connected successfully
[2025-06-09 00:29:14] [info] Database connected successfully
[2025-06-09 00:30:32] [info] Database connected successfully
[2025-06-09 00:30:55] [info] Database connected successfully
[2025-06-09 00:31:06] [info] Database connected successfully
[2025-06-09 00:31:46] [info] Database connected successfully
[2025-06-09 00:31:51] [info] Database connected successfully
[2025-06-09 00:31:52] [info] Database connected successfully
[2025-06-09 00:31:54] [info] Database connected successfully
[2025-06-09 00:31:59] [info] Database connected successfully
[2025-06-09 00:32:00] [info] Database connected successfully
[2025-06-09 00:32:00] [info] Database connected successfully
[2025-06-09 00:32:05] [info] Database connected successfully
[2025-06-09 00:32:09] [info] Database connected successfully
[2025-06-09 00:32:11] [info] Database connected successfully
[2025-06-09 00:32:25] [info] Database connected successfully
[2025-06-09 00:32:32] [info] Database connected successfully
[2025-06-09 00:32:37] [info] Database connected successfully
[2025-06-09 00:45:40] [info] Database connected successfully
[2025-06-09 00:45:56] [info] Database connected successfully
[2025-06-09 00:46:04] [info] Database connected successfully
[2025-06-09 00:46:31] [info] Database connected successfully
[2025-06-09 00:47:17] [info] Database connected successfully
[2025-06-09 00:47:23] [info] Database connected successfully
[2025-06-09 00:47:25] [info] Database connected successfully
[2025-06-09 00:47:37] [info] Database connected successfully
[2025-06-09 00:49:04] [info] Database connected successfully
[2025-06-09 00:49:08] [info] Database connected successfully
[2025-06-09 00:49:10] [info] Database connected successfully
[2025-06-09 00:49:29] [info] Database connected successfully
[2025-06-09 00:49:32] [info] Database connected successfully
[2025-06-09 00:59:42] [info] Database connected successfully
[2025-06-09 01:00:55] [info] Database connected successfully
[2025-06-09 01:00:58] [info] Database connected successfully
[2025-06-09 01:00:58] [info] Database connected successfully
[2025-06-09 01:01:03] [info] Database connected successfully
[2025-06-09 01:01:05] [info] Database connected successfully
[2025-06-09 01:01:10] [info] Database connected successfully
[2025-06-09 01:01:13] [info] Database connected successfully
[2025-06-09 01:01:50] [info] Database connected successfully
[2025-06-09 01:01:56] [info] Database connected successfully
[2025-06-09 01:02:00] [info] Database connected successfully
[2025-06-09 01:02:02] [info] Database connected successfully
[2025-06-09 01:02:12] [info] Database connected successfully
[2025-06-09 01:02:13] [info] Database connected successfully
[2025-06-09 01:02:15] [info] Database connected successfully
[2025-06-09 01:02:17] [info] Database connected successfully
[2025-06-09 01:06:28] [info] Database connected successfully
[2025-06-09 01:07:10] [info] Database connected successfully
[2025-06-09 01:08:08] [info] Database connected successfully
[2025-06-09 01:08:12] [info] Database connected successfully
[2025-06-09 01:08:49] [info] Database connected successfully
[2025-06-09 01:09:04] [info] Database connected successfully
[2025-06-09 01:09:37] [info] Database connected successfully
[2025-06-09 01:10:13] [info] Database connected successfully
[2025-06-09 01:10:19] [info] Database connected successfully
[2025-06-09 01:10:31] [info] Database connected successfully
[2025-06-09 01:10:33] [info] Database connected successfully
[2025-06-09 01:11:28] [info] Database connected successfully
[2025-06-09 01:12:13] [info] Database connected successfully
[2025-06-09 01:12:18] [info] Database connected successfully
[2025-06-09 01:19:35] [info] Database connected successfully
[2025-06-09 01:25:27] [info] Database connected successfully
[2025-06-09 01:28:05] [info] Database connected successfully
[2025-06-09 01:28:08] [info] Database connected successfully
[2025-06-09 01:28:19] [info] Database connected successfully
[2025-06-09 01:33:01] [info] Database connected successfully
[2025-06-09 01:35:04] [info] Database connected successfully
[2025-06-09 01:35:17] [info] Database connected successfully
[2025-06-09 01:35:52] [info] Database connected successfully
[2025-06-09 01:36:11] [info] Database connected successfully
[2025-06-09 01:39:48] [info] Database connected successfully
[2025-06-09 01:39:57] [info] Database connected successfully
[2025-06-09 01:40:01] [info] Database connected successfully
[2025-06-09 01:40:13] [info] Database connected successfully
[2025-06-09 01:58:04] [info] Database connected successfully
[2025-06-09 01:58:14] [info] Database connected successfully
[2025-06-09 01:58:17] [info] Database connected successfully
[2025-06-09 01:58:17] [info] Database connected successfully
[2025-06-09 01:58:20] [info] Database connected successfully
[2025-06-09 01:58:23] [info] Database connected successfully
[2025-06-09 01:58:26] [info] Database connected successfully
[2025-06-09 01:58:39] [info] Database connected successfully
[2025-06-09 02:09:10] [info] Database connected successfully
[2025-06-09 02:18:39] [info] Database connected successfully
[2025-06-09 02:23:30] [info] Database connected successfully
[2025-06-09 02:24:01] [info] Database connected successfully
[2025-06-09 02:24:03] [info] Database connected successfully
[2025-06-09 02:24:06] [info] Database connected successfully
[2025-06-09 02:24:10] [info] Database connected successfully
[2025-06-09 02:25:08] [info] Database connected successfully
[2025-06-09 02:25:59] [info] Database connected successfully
[2025-06-09 02:26:11] [info] Database connected successfully
[2025-06-09 02:26:11] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'tags' in 'field list' SQL: SELECT DISTINCT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) as suggestion, 'tag' as type
                 FROM documents
                 CROSS JOIN (SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers
                 WHERE company_id = ? AND status != 'deleted' 
                 AND CHAR_LENGTH(tags) - CHAR_LENGTH(REPLACE(tags, ',', '')) >= numbers.n - 1
                 AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) LIKE ?
                 ORDER BY suggestion
                 LIMIT 5
[2025-06-09 02:26:14] [info] Database connected successfully
[2025-06-09 02:26:14] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'tags' in 'field list' SQL: SELECT DISTINCT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) as suggestion, 'tag' as type
                 FROM documents
                 CROSS JOIN (SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers
                 WHERE company_id = ? AND status != 'deleted' 
                 AND CHAR_LENGTH(tags) - CHAR_LENGTH(REPLACE(tags, ',', '')) >= numbers.n - 1
                 AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) LIKE ?
                 ORDER BY suggestion
                 LIMIT 5
[2025-06-09 02:26:15] [info] Database connected successfully
[2025-06-09 02:26:15] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'tags' in 'field list' SQL: SELECT DISTINCT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) as suggestion, 'tag' as type
                 FROM documents
                 CROSS JOIN (SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers
                 WHERE company_id = ? AND status != 'deleted' 
                 AND CHAR_LENGTH(tags) - CHAR_LENGTH(REPLACE(tags, ',', '')) >= numbers.n - 1
                 AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) LIKE ?
                 ORDER BY suggestion
                 LIMIT 5
[2025-06-09 02:26:15] [info] Database connected successfully
[2025-06-09 02:26:15] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'tags' in 'field list' SQL: SELECT DISTINCT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) as suggestion, 'tag' as type
                 FROM documents
                 CROSS JOIN (SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers
                 WHERE company_id = ? AND status != 'deleted' 
                 AND CHAR_LENGTH(tags) - CHAR_LENGTH(REPLACE(tags, ',', '')) >= numbers.n - 1
                 AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags, ',', numbers.n), ',', -1)) LIKE ?
                 ORDER BY suggestion
                 LIMIT 5
[2025-06-09 02:26:17] [info] Database connected successfully
[2025-06-09 02:26:17] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.tags' in 'field list' SQL: SELECT d.*, u.first_name, u.last_name, c.name as category_name, (CASE WHEN d.title LIKE '%lega%' THEN 10 ELSE 0 END + CASE WHEN d.description LIKE '%lega%' THEN 5 ELSE 0 END + CASE WHEN d.tags LIKE '%lega%' THEN 3 ELSE 0 END + CASE WHEN d.file_name LIKE '%lega%' THEN 2 ELSE 0 END) as relevance_score FROM documents d 
                 LEFT JOIN users u ON d.created_by = u.id 
                 LEFT JOIN categories c ON d.category_id = c.id 
                 
                 WHERE d.company_id = ? AND d.status != 'deleted' AND ((d.title LIKE ? OR d.description LIKE ? OR d.tags LIKE ? OR d.file_name LIKE ?)) 
                 ORDER BY relevance_score DESC, d.created_at DESC 
                 LIMIT 50
[2025-06-09 02:26:19] [info] Database connected successfully
[2025-06-09 02:26:36] [info] Database connected successfully
[2025-06-09 02:26:47] [info] Database connected successfully
[2025-06-09 02:26:49] [info] Database connected successfully
[2025-06-09 02:26:54] [info] Database connected successfully
[2025-06-09 02:26:56] [info] Database connected successfully
[2025-06-09 02:26:57] [info] Database connected successfully
[2025-06-09 02:27:03] [info] Database connected successfully
[2025-06-09 02:27:04] [info] Database connected successfully
[2025-06-09 02:27:06] [info] Database connected successfully
[2025-06-09 02:33:36] [info] Database connected successfully
[2025-06-09 02:34:26] [info] Database connected successfully
[2025-06-09 02:34:42] [info] Database connected successfully
[2025-06-09 02:35:10] [info] Database connected successfully
[2025-06-09 02:35:18] [info] Database connected successfully
[2025-06-09 02:35:23] [info] Database connected successfully
[2025-06-09 02:35:27] [info] Database connected successfully
[2025-06-09 02:35:27] [info] Database connected successfully
[2025-06-09 02:35:33] [info] Database connected successfully
[2025-06-09 02:35:37] [info] Database connected successfully
[2025-06-09 02:35:37] [info] Database connected successfully
[2025-06-09 02:35:41] [info] Database connected successfully
[2025-06-09 02:35:43] [info] Database connected successfully
[2025-06-09 02:35:47] [info] Database connected successfully
[2025-06-09 02:35:58] [info] Database connected successfully
[2025-06-09 02:36:05] [info] Database connected successfully
[2025-06-09 02:36:35] [info] Database connected successfully
[2025-06-09 02:36:35] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.tags' in 'field list' SQL: SELECT d.*, u.first_name, u.last_name, c.name as category_name, (CASE WHEN d.title LIKE '%boxwe%' THEN 10 ELSE 0 END + CASE WHEN d.description LIKE '%boxwe%' THEN 5 ELSE 0 END + CASE WHEN d.tags LIKE '%boxwe%' THEN 3 ELSE 0 END + CASE WHEN d.file_name LIKE '%boxwe%' THEN 2 ELSE 0 END) as relevance_score FROM documents d 
                 LEFT JOIN users u ON d.created_by = u.id 
                 LEFT JOIN categories c ON d.category_id = c.id 
                 
                 WHERE d.company_id = ? AND d.status != 'deleted' AND ((d.title LIKE ? OR d.description LIKE ? OR d.tags LIKE ? OR d.file_name LIKE ?)) 
                 ORDER BY relevance_score DESC, d.created_at DESC 
                 LIMIT 50
[2025-06-09 02:37:45] [info] Database connected successfully
[2025-06-09 02:37:55] [info] Database connected successfully
[2025-06-09 02:38:15] [info] Database connected successfully
[2025-06-09 02:38:15] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.tags' in 'field list' SQL: SELECT d.*, u.first_name, u.last_name, c.name as category_name, (CASE WHEN d.title LIKE '%box213%' THEN 10 ELSE 0 END + CASE WHEN d.description LIKE '%box213%' THEN 5 ELSE 0 END + CASE WHEN d.tags LIKE '%box213%' THEN 3 ELSE 0 END + CASE WHEN d.file_name LIKE '%box213%' THEN 2 ELSE 0 END) as relevance_score FROM documents d 
                 LEFT JOIN users u ON d.created_by = u.id 
                 LEFT JOIN categories c ON d.category_id = c.id 
                 
                 WHERE d.company_id = ? AND d.status != 'deleted' AND ((d.title LIKE ? OR d.description LIKE ? OR d.tags LIKE ? OR d.file_name LIKE ?)) 
                 ORDER BY relevance_score DESC, d.created_at DESC 
                 LIMIT 50
[2025-06-09 02:43:41] [info] Database connected successfully
[2025-06-09 02:43:41] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.code' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, sl.code as storage_location_code,
                u.first_name, u.last_name, (CASE WHEN b.name LIKE '%box213%' THEN 10 ELSE 0 END + CASE WHEN b.box_id LIKE '%box213%' THEN 8 ELSE 0 END + CASE WHEN b.description LIKE '%box213%' THEN 5 ELSE 0 END) as relevance_score
                FROM boxes b
                LEFT JOIN users u ON b.created_by = u.id
                LEFT JOIN storage_locations sl ON b.storage_location_id = sl.id
                LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                WHERE b.company_id = ? AND ((b.name LIKE ? OR b.box_id LIKE ? OR b.description LIKE ?))
                ORDER BY relevance_score DESC, b.created_at DESC
                LIMIT 20
[2025-06-09 02:43:52] [info] Database connected successfully
[2025-06-09 02:44:17] [info] Database connected successfully
[2025-06-09 02:44:17] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sl.code' in 'field list' SQL: SELECT b.*, w.name as warehouse_name, sl.code as storage_location_code,
                u.first_name, u.last_name, (CASE WHEN b.name LIKE '%box213%' THEN 10 ELSE 0 END + CASE WHEN b.box_id LIKE '%box213%' THEN 8 ELSE 0 END + CASE WHEN b.description LIKE '%box213%' THEN 5 ELSE 0 END) as relevance_score
                FROM boxes b
                LEFT JOIN users u ON b.created_by = u.id
                LEFT JOIN storage_locations sl ON b.storage_location_id = sl.id
                LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                WHERE b.company_id = ? AND ((b.name LIKE ? OR b.box_id LIKE ? OR b.description LIKE ?))
                ORDER BY relevance_score DESC, b.created_at DESC
                LIMIT 20
[2025-06-09 02:45:00] [info] Database connected successfully
[2025-06-09 02:45:06] [info] Database connected successfully
[2025-06-09 02:45:10] [info] Database connected successfully
[2025-06-09 02:45:11] [info] Database connected successfully
[2025-06-09 02:45:12] [info] Database connected successfully
[2025-06-09 02:45:20] [info] Database connected successfully
[2025-06-09 02:45:23] [info] Database connected successfully
[2025-06-09 02:46:15] [info] Database connected successfully
[2025-06-09 02:46:15] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.storage_location_id' in 'on clause' SQL: SELECT b.*, w.name as warehouse_name, sl.identifier as storage_location_code,
                u.first_name, u.last_name, (CASE WHEN b.name LIKE '%box213%' THEN 10 ELSE 0 END + CASE WHEN b.box_id LIKE '%box213%' THEN 8 ELSE 0 END + CASE WHEN b.description LIKE '%box213%' THEN 5 ELSE 0 END) as relevance_score
                FROM boxes b
                LEFT JOIN users u ON b.created_by = u.id
                LEFT JOIN storage_locations sl ON b.storage_location_id = sl.id
                LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                WHERE b.company_id = ? AND ((b.name LIKE ? OR b.box_id LIKE ? OR b.description LIKE ?))
                ORDER BY relevance_score DESC, b.created_at DESC
                LIMIT 20
[2025-06-09 02:46:25] [info] Database connected successfully
[2025-06-09 02:46:35] [info] Database connected successfully
[2025-06-09 02:46:35] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.storage_location_id' in 'on clause' SQL: SELECT b.*, w.name as warehouse_name, sl.identifier as storage_location_code,
                u.first_name, u.last_name, (CASE WHEN b.name LIKE '%box213%' THEN 10 ELSE 0 END + CASE WHEN b.box_id LIKE '%box213%' THEN 8 ELSE 0 END + CASE WHEN b.description LIKE '%box213%' THEN 5 ELSE 0 END) as relevance_score
                FROM boxes b
                LEFT JOIN users u ON b.created_by = u.id
                LEFT JOIN storage_locations sl ON b.storage_location_id = sl.id
                LEFT JOIN warehouses w ON sl.warehouse_id = w.id
                WHERE b.company_id = ? AND ((b.name LIKE ? OR b.box_id LIKE ? OR b.description LIKE ?))
                ORDER BY relevance_score DESC, b.created_at DESC
                LIMIT 20
[2025-06-09 02:47:39] [info] Database connected successfully
[2025-06-09 02:47:53] [info] Database connected successfully
[2025-06-09 02:48:04] [info] Database connected successfully
[2025-06-09 02:48:23] [info] Database connected successfully
[2025-06-09 02:48:23] [info] Database connected successfully
[2025-06-09 02:48:23] [info] Database connected successfully
[2025-06-09 02:48:34] [info] Database connected successfully
[2025-06-09 02:48:35] [info] Database connected successfully
[2025-06-09 02:48:45] [info] Database connected successfully
[2025-06-09 02:50:15] [info] Database connected successfully
[2025-06-09 02:55:13] [info] Database connected successfully
[2025-06-09 02:55:33] [info] Database connected successfully
[2025-06-09 02:55:48] [info] Database connected successfully
[2025-06-09 02:56:15] [info] Database connected successfully
[2025-06-09 02:56:31] [info] Database connected successfully
[2025-06-09 02:56:36] [info] Database connected successfully
[2025-06-09 02:56:43] [info] Database connected successfully
[2025-06-09 02:57:09] [info] Database connected successfully
[2025-06-09 02:57:12] [info] Database connected successfully
[2025-06-09 02:57:19] [info] Database connected successfully
[2025-06-09 02:57:39] [info] Database connected successfully
[2025-06-09 02:57:48] [info] Database connected successfully
[2025-06-09 02:58:02] [info] Database connected successfully
[2025-06-09 03:00:59] [info] Database connected successfully
[2025-06-09 03:01:15] [info] Database connected successfully
[2025-06-09 03:01:23] [info] Database connected successfully
[2025-06-09 03:01:28] [info] Database connected successfully
[2025-06-09 03:01:29] [info] Database connected successfully
[2025-06-09 03:01:30] [info] Database connected successfully
[2025-06-09 03:01:35] [info] Database connected successfully
[2025-06-09 03:01:38] [info] Database connected successfully
[2025-06-09 03:01:40] [info] Database connected successfully
[2025-06-09 03:01:52] [info] Database connected successfully
[2025-06-09 03:02:08] [info] Database connected successfully
[2025-06-09 03:02:28] [info] Database connected successfully
[2025-06-09 03:02:31] [info] Database connected successfully
[2025-06-09 03:02:33] [info] Database connected successfully
[2025-06-09 03:02:36] [info] Database connected successfully
[2025-06-09 03:03:00] [info] Database connected successfully
[2025-06-09 03:04:04] [info] Database connected successfully
[2025-06-09 03:04:23] [info] Database connected successfully
[2025-06-09 03:04:25] [info] Database connected successfully
[2025-06-09 03:04:31] [info] Database connected successfully
[2025-06-09 03:04:39] [info] Database connected successfully
[2025-06-09 03:08:24] [info] Database connected successfully
[2025-06-09 03:08:28] [info] Database connected successfully
[2025-06-09 03:09:04] [info] Database connected successfully
[2025-06-09 03:09:06] [info] Database connected successfully
[2025-06-09 03:09:08] [info] Database connected successfully
[2025-06-09 03:09:10] [info] Database connected successfully
[2025-06-09 03:10:10] [info] Database connected successfully
[2025-06-09 03:10:21] [info] Database connected successfully
[2025-06-09 03:10:24] [info] Database connected successfully
[2025-06-09 03:10:38] [info] Database connected successfully
[2025-06-09 03:10:49] [info] Database connected successfully
[2025-06-09 03:10:55] [info] Database connected successfully
[2025-06-09 03:12:59] [info] Database connected successfully
[2025-06-09 03:13:06] [info] Database connected successfully
[2025-06-09 03:14:34] [info] Database connected successfully
[2025-06-09 03:17:30] [info] Database connected successfully
[2025-06-09 03:17:32] [info] Database connected successfully
[2025-06-09 03:20:59] [info] Database connected successfully
[2025-06-09 03:23:52] [info] Database connected successfully
[2025-06-09 11:36:54] [info] Database connected successfully
[2025-06-09 11:37:29] [info] Database connected successfully
[2025-06-09 11:38:10] [info] Database connected successfully
[2025-06-09 11:38:13] [info] Database connected successfully
[2025-06-09 11:39:40] [info] Database connected successfully
[2025-06-09 11:39:57] [info] Database connected successfully
[2025-06-09 11:39:57] [info] Database connected successfully
[2025-06-09 11:40:01] [info] Database connected successfully
[2025-06-09 11:45:04] [info] Database connected successfully
[2025-06-09 11:45:39] [info] Database connected successfully
[2025-06-09 11:45:49] [info] Database connected successfully
[2025-06-09 11:46:03] [info] Database connected successfully
[2025-06-09 11:46:10] [info] Database connected successfully
[2025-06-09 11:46:13] [info] Database connected successfully
[2025-06-09 11:46:27] [info] Database connected successfully
[2025-06-09 11:47:35] [info] Database connected successfully
[2025-06-09 11:47:37] [info] Database connected successfully
[2025-06-09 11:47:38] [info] Database connected successfully
[2025-06-09 11:48:00] [info] Database connected successfully
[2025-06-09 11:48:11] [info] Database connected successfully
[2025-06-09 11:49:04] [info] Database connected successfully
[2025-06-09 11:49:09] [info] Database connected successfully
[2025-06-09 11:49:11] [info] Database connected successfully
[2025-06-09 11:49:13] [info] Database connected successfully
[2025-06-09 11:49:15] [info] Database connected successfully
[2025-06-09 11:49:18] [info] Database connected successfully
[2025-06-09 11:49:34] [info] Database connected successfully
[2025-06-09 11:49:46] [info] Database connected successfully
[2025-06-09 11:50:10] [info] Database connected successfully
[2025-06-09 11:51:03] [info] Database connected successfully
[2025-06-09 11:55:12] [info] Database connected successfully
[2025-06-09 12:05:58] [info] Database connected successfully
[2025-06-09 12:07:33] [info] Database connected successfully
[2025-06-09 12:07:51] [info] Database connected successfully
[2025-06-09 12:08:09] [info] Database connected successfully
[2025-06-09 12:08:11] [info] Database connected successfully
[2025-06-09 12:10:22] [info] Database connected successfully
[2025-06-09 12:14:04] [info] Database connected successfully
[2025-06-09 12:14:06] [info] Database connected successfully
[2025-06-09 12:14:31] [info] Database connected successfully
[2025-06-09 12:15:49] [info] Database connected successfully
[2025-06-09 12:21:41] [info] Database connected successfully
[2025-06-09 12:27:52] [info] Database connected successfully
[2025-06-09 12:29:39] [info] Database connected successfully
[2025-06-09 12:33:05] [info] Database connected successfully
[2025-06-09 12:33:07] [info] Database connected successfully
[2025-06-09 12:34:00] [info] Database connected successfully
[2025-06-09 12:34:05] [info] Database connected successfully
[2025-06-09 12:34:10] [info] Database connected successfully
[2025-06-09 12:34:15] [info] Database connected successfully
[2025-06-09 12:34:39] [info] Database connected successfully
[2025-06-09 12:38:26] [info] Database connected successfully
[2025-06-09 12:38:30] [info] Database connected successfully
[2025-06-09 12:38:33] [info] Database connected successfully
[2025-06-09 12:41:04] [info] Database connected successfully
[2025-06-09 12:41:08] [info] Database connected successfully
[2025-06-09 12:41:12] [info] Database connected successfully
[2025-06-09 12:41:22] [info] Database connected successfully
[2025-06-09 12:45:43] [info] Database connected successfully
[2025-06-09 12:46:11] [info] Database connected successfully
[2025-06-09 12:48:50] [info] Database connected successfully
[2025-06-09 12:49:35] [info] Database connected successfully
[2025-06-09 12:49:35] [info] Database connected successfully
[2025-06-09 12:49:46] [info] Database connected successfully
[2025-06-09 12:49:48] [info] Database connected successfully
[2025-06-09 12:50:48] [info] Database connected successfully
[2025-06-09 12:50:50] [info] Database connected successfully
[2025-06-09 12:51:14] [info] Database connected successfully
[2025-06-09 12:51:16] [info] Database connected successfully
[2025-06-09 12:51:40] [info] Database connected successfully
[2025-06-09 12:51:43] [info] Database connected successfully
[2025-06-09 12:55:57] [info] Database connected successfully
[2025-06-09 12:56:17] [info] Database connected successfully
[2025-06-09 12:56:32] [info] Database connected successfully
[2025-06-09 12:59:13] [info] Database connected successfully
[2025-06-09 12:59:42] [info] Database connected successfully
[2025-06-09 12:59:42] [error] Query failed: SQLSTATE[HY000]: General error: 1364 Field 'file_name' doesn't have a default value SQL: INSERT INTO documents (
            company_id, bundle_id, title, description, document_type, 
            file_size, status, created_by, created_at, updated_at
        ) VALUES (2, 14, ?, ?, ?, ?, 'active', 5, NOW(), NOW())
[2025-06-09 13:00:12] [info] Database connected successfully
[2025-06-09 13:00:12] [error] Query failed: SQLSTATE[HY000]: General error: 1364 Field 'file_path' doesn't have a default value SQL: INSERT INTO documents (
            company_id, bundle_id, title, file_name, description, document_type,
            file_size, status, created_by, created_at, updated_at
        ) VALUES (2, 14, ?, ?, ?, ?, ?, 'active', 5, NOW(), NOW())
[2025-06-09 13:00:41] [info] Database connected successfully
[2025-06-09 13:00:41] [error] Query failed: SQLSTATE[HY000]: General error: 1364 Field 'mime_type' doesn't have a default value SQL: INSERT INTO documents (
                company_id, bundle_id, title, file_name, file_path, description, 
                document_type, file_size, status, created_by, created_at, updated_at
            ) VALUES (2, 14, ?, ?, ?, ?, ?, ?, 'active', 5, NOW(), NOW())
[2025-06-09 13:00:41] [error] Query failed: SQLSTATE[HY000]: General error: 1364 Field 'mime_type' doesn't have a default value SQL: INSERT INTO documents (
                company_id, bundle_id, title, file_name, file_path, description, 
                document_type, file_size, status, created_by, created_at, updated_at
            ) VALUES (2, 14, ?, ?, ?, ?, ?, ?, 'active', 5, NOW(), NOW())
[2025-06-09 13:00:41] [error] Query failed: SQLSTATE[HY000]: General error: 1364 Field 'mime_type' doesn't have a default value SQL: INSERT INTO documents (
                company_id, bundle_id, title, file_name, file_path, description, 
                document_type, file_size, status, created_by, created_at, updated_at
            ) VALUES (2, 14, ?, ?, ?, ?, ?, ?, 'active', 5, NOW(), NOW())
[2025-06-09 13:01:10] [info] Database connected successfully
[2025-06-09 13:01:10] [error] Query failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'document_type' at row 1 SQL: INSERT INTO documents (
                company_id, bundle_id, title, file_name, file_path, description,
                document_type, file_size, mime_type, file_hash, status, created_by, created_at, updated_at
            ) VALUES (2, 14, ?, ?, ?, ?, ?, ?, 'application/pdf', ?, 'approved', 5, NOW(), NOW())
[2025-06-09 13:01:36] [info] Database connected successfully
[2025-06-09 13:01:49] [info] Database connected successfully
[2025-06-09 13:02:31] [info] Database connected successfully
[2025-06-09 13:02:31] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.parent_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
             FROM documents d
             LEFT JOIN users u ON d.created_by = u.id
             WHERE (d.id = ? OR d.parent_id = ?) AND d.company_id = ?
             ORDER BY d.version DESC
[2025-06-09 13:02:33] [info] Database connected successfully
[2025-06-09 13:02:58] [info] Database connected successfully
[2025-06-09 13:03:15] [info] Database connected successfully
[2025-06-09 13:03:15] [info] Database connected successfully
[2025-06-09 13:03:26] [info] Database connected successfully
[2025-06-09 13:03:47] [info] Database connected successfully
[2025-06-09 13:03:57] [info] Database connected successfully
[2025-06-09 13:08:04] [info] Database connected successfully
[2025-06-09 13:10:14] [info] Database connected successfully
[2025-06-09 13:10:50] [info] Database connected successfully
[2025-06-09 13:11:33] [info] Database connected successfully
[2025-06-09 13:12:05] [info] Database connected successfully
[2025-06-09 13:12:42] [info] Database connected successfully
[2025-06-09 13:13:41] [info] Database connected successfully
[2025-06-09 13:14:54] [info] Database connected successfully
[2025-06-09 13:18:46] [info] Database connected successfully
[2025-06-09 13:19:32] [info] Database connected successfully
[2025-06-09 13:19:54] [info] Database connected successfully
[2025-06-09 13:23:28] [info] Database connected successfully
[2025-06-09 13:23:49] [info] Database connected successfully
[2025-06-09 13:25:12] [info] Database connected successfully
[2025-06-09 13:27:56] [info] Database connected successfully
[2025-06-09 13:27:56] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'b.reference_number' in 'field list' SQL: SELECT b.id, b.name, b.reference_number FROM box_bundles bb 
         JOIN boxes b ON bb.box_id = b.id 
         WHERE bb.bundle_id = ?
[2025-06-09 13:28:41] [info] Database connected successfully
[2025-06-09 13:29:55] [info] Database connected successfully
[2025-06-09 13:30:46] [info] Database connected successfully
[2025-06-09 13:32:08] [info] Database connected successfully
[2025-06-09 13:32:27] [info] Database connected successfully
[2025-06-09 13:32:31] [info] Database connected successfully
[2025-06-09 13:32:34] [info] Database connected successfully
[2025-06-09 13:32:45] [info] Database connected successfully
[2025-06-09 13:33:08] [info] Database connected successfully
[2025-06-09 13:33:13] [info] Database connected successfully
[2025-06-09 13:33:41] [info] Database connected successfully
[2025-06-09 13:33:41] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'd.parent_id' in 'where clause' SQL: SELECT d.*, u.first_name, u.last_name
             FROM documents d
             LEFT JOIN users u ON d.created_by = u.id
             WHERE (d.id = ? OR d.parent_id = ?) AND d.company_id = ?
             ORDER BY d.version DESC
[2025-06-09 13:34:42] [info] Database connected successfully
[2025-06-09 13:35:24] [info] Database connected successfully
[2025-06-09 13:35:24] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.document_locations' doesn't exist SQL: SELECT dl.*, sl.name as location_name, sl.identifier, w.name as warehouse_name
             FROM document_locations dl
             LEFT JOIN storage_locations sl ON dl.location_id = sl.id
             LEFT JOIN warehouses w ON sl.warehouse_id = w.id
             WHERE dl.document_id = ? AND dl.is_current = 1
[2025-06-09 13:36:42] [info] Database connected successfully
[2025-06-09 13:37:29] [info] Database connected successfully
[2025-06-09 13:38:02] [info] Database connected successfully
[2025-06-09 13:38:13] [info] Database connected successfully
[2025-06-09 13:40:23] [info] Database connected successfully
[2025-06-09 13:40:27] [info] Database connected successfully
[2025-06-09 13:41:03] [info] Database connected successfully
[2025-06-09 13:45:15] [info] Database connected successfully
[2025-06-09 13:46:05] [info] Database connected successfully
[2025-06-09 13:47:50] [info] Database connected successfully
[2025-06-09 13:49:02] [info] Database connected successfully
[2025-06-09 13:49:40] [info] Database connected successfully
[2025-06-09 13:49:42] [info] Database connected successfully
[2025-06-09 13:49:47] [info] Database connected successfully
[2025-06-09 13:49:47] [info] Database connected successfully
[2025-06-09 13:49:52] [info] Database connected successfully
[2025-06-09 13:50:11] [info] Database connected successfully
[2025-06-09 13:54:03] [info] Database connected successfully
[2025-06-09 13:54:48] [info] Database connected successfully
[2025-06-09 13:55:06] [info] Database connected successfully
[2025-06-09 13:55:25] [info] Database connected successfully
[2025-06-09 13:55:46] [info] Database connected successfully
[2025-06-09 14:00:45] [info] Database connected successfully
[2025-06-09 14:04:13] [info] Database connected successfully
[2025-06-09 14:07:04] [info] Database connected successfully
[2025-06-09 14:08:48] [info] Database connected successfully
[2025-06-09 14:11:52] [info] Database connected successfully
[2025-06-09 14:11:59] [info] Database connected successfully
[2025-06-09 14:13:13] [info] Database connected successfully
[2025-06-09 14:13:30] [info] Database connected successfully
[2025-06-09 14:13:39] [info] Database connected successfully
[2025-06-09 14:13:43] [info] Database connected successfully
[2025-06-09 14:13:48] [info] Database connected successfully
[2025-06-09 14:13:53] [info] Database connected successfully
[2025-06-09 14:14:16] [info] Database connected successfully
[2025-06-09 14:14:20] [info] Database connected successfully
[2025-06-09 14:14:27] [info] Database connected successfully
[2025-06-09 14:14:51] [info] Database connected successfully
[2025-06-09 14:14:56] [info] Database connected successfully
[2025-06-09 14:15:01] [info] Database connected successfully
[2025-06-09 14:15:07] [info] Database connected successfully
[2025-06-09 14:15:09] [info] Database connected successfully
[2025-06-09 14:16:09] [info] Database connected successfully
[2025-06-09 14:16:16] [info] Database connected successfully
[2025-06-09 14:16:32] [info] Database connected successfully
[2025-06-09 14:16:48] [info] Database connected successfully
[2025-06-09 14:17:15] [info] Database connected successfully
[2025-06-09 14:19:23] [info] Database connected successfully
[2025-06-09 14:23:35] [info] Database connected successfully
[2025-06-09 14:23:46] [info] Database connected successfully
[2025-06-09 14:24:11] [info] Database connected successfully
[2025-06-09 14:24:37] [info] Database connected successfully
[2025-06-09 14:25:45] [info] Database connected successfully
[2025-06-09 14:25:49] [info] Database connected successfully
[2025-06-09 14:26:20] [info] Database connected successfully
[2025-06-09 14:26:23] [info] Database connected successfully
[2025-06-09 14:26:45] [info] Database connected successfully
[2025-06-09 14:26:55] [info] Database connected successfully
[2025-06-09 14:26:58] [info] Database connected successfully
[2025-06-09 14:27:20] [info] Database connected successfully
[2025-06-09 14:27:24] [info] Database connected successfully
[2025-06-09 14:27:55] [info] Database connected successfully
[2025-06-09 14:28:02] [info] Database connected successfully
[2025-06-09 14:28:07] [info] Database connected successfully
[2025-06-09 14:28:33] [info] Database connected successfully
[2025-06-09 14:28:35] [info] Database connected successfully
[2025-06-09 14:35:17] [info] Database connected successfully
[2025-06-09 14:37:08] [info] Database connected successfully
[2025-06-09 14:37:23] [info] Database connected successfully
[2025-06-09 14:38:38] [info] Database connected successfully
[2025-06-09 14:39:22] [info] Database connected successfully
[2025-06-09 14:39:24] [info] Database connected successfully
[2025-06-09 14:39:35] [info] Database connected successfully
[2025-06-09 14:39:38] [info] Database connected successfully
[2025-06-09 14:39:50] [info] Database connected successfully
[2025-06-09 14:40:57] [info] Database connected successfully
[2025-06-09 14:41:20] [info] Database connected successfully
[2025-06-09 14:41:22] [info] Database connected successfully
[2025-06-09 14:41:27] [info] Database connected successfully
[2025-06-09 14:41:28] [info] Database connected successfully
[2025-06-09 14:49:45] [info] Database connected successfully
[2025-06-09 14:50:26] [info] Database connected successfully
[2025-06-09 14:50:30] [info] Database connected successfully
[2025-06-09 14:51:07] [info] Database connected successfully
[2025-06-09 14:51:09] [info] Database connected successfully
[2025-06-09 14:52:03] [info] Database connected successfully
[2025-06-09 14:56:20] [info] Database connected successfully
[2025-06-09 15:05:13] [info] Database connected successfully
[2025-06-09 15:05:15] [info] Database connected successfully
[2025-06-09 15:05:23] [info] Database connected successfully
[2025-06-09 15:06:16] [info] Database connected successfully
[2025-06-09 15:10:28] [info] Database connected successfully
[2025-06-09 15:10:43] [info] Database connected successfully
[2025-06-09 15:12:58] [info] Database connected successfully
[2025-06-09 15:13:00] [info] Database connected successfully
[2025-06-09 15:13:10] [info] Database connected successfully
[2025-06-09 15:13:21] [info] Database connected successfully
[2025-06-09 15:24:18] [info] Database connected successfully
[2025-06-09 15:30:15] [info] Database connected successfully
[2025-06-09 15:30:30] [info] Database connected successfully
[2025-06-09 15:30:33] [info] Database connected successfully
[2025-06-09 15:30:34] [info] Database connected successfully
[2025-06-09 15:30:37] [info] Database connected successfully
[2025-06-09 15:30:39] [info] Database connected successfully
[2025-06-09 15:30:40] [info] Database connected successfully
[2025-06-09 15:30:42] [info] Database connected successfully
[2025-06-09 15:30:43] [info] Database connected successfully
[2025-06-09 15:30:51] [info] Database connected successfully
[2025-06-09 15:31:02] [info] Database connected successfully
[2025-06-09 15:31:04] [info] Database connected successfully
[2025-06-09 15:31:06] [info] Database connected successfully
[2025-06-09 15:31:07] [info] Database connected successfully
[2025-06-09 15:31:09] [info] Database connected successfully
[2025-06-09 15:31:12] [info] Database connected successfully
[2025-06-09 15:31:15] [info] Database connected successfully
[2025-06-09 15:31:41] [info] Database connected successfully
[2025-06-09 15:31:48] [info] Database connected successfully
[2025-06-09 15:32:14] [info] Database connected successfully
[2025-06-09 15:32:18] [info] Database connected successfully
[2025-06-09 15:32:25] [info] Database connected successfully
[2025-06-09 15:34:26] [info] Database connected successfully
[2025-06-09 15:43:35] [info] Database connected successfully
[2025-06-09 15:43:43] [info] Database connected successfully
[2025-06-09 15:43:45] [info] Database connected successfully
[2025-06-09 15:43:48] [info] Database connected successfully
[2025-06-09 15:43:50] [info] Database connected successfully
[2025-06-09 15:43:53] [info] Database connected successfully
[2025-06-09 15:43:59] [info] Database connected successfully
[2025-06-09 15:44:00] [info] Database connected successfully
[2025-06-09 15:46:01] [info] Database connected successfully
[2025-06-09 15:46:03] [info] Database connected successfully
[2025-06-09 15:46:05] [info] Database connected successfully
[2025-06-09 15:46:19] [info] Database connected successfully
[2025-06-09 15:59:15] [info] Database connected successfully
[2025-06-09 15:59:38] [info] Database connected successfully
[2025-06-09 16:06:13] [info] Database connected successfully
[2025-06-09 16:06:36] [info] Database connected successfully
[2025-06-09 16:06:36] [info] Database connected successfully
[2025-06-09 16:06:39] [info] Database connected successfully
[2025-06-09 16:06:40] [info] Database connected successfully
[2025-06-09 16:06:41] [info] Database connected successfully
[2025-06-09 16:06:41] [info] Database connected successfully
[2025-06-09 16:06:41] [info] Database connected successfully
[2025-06-09 16:06:43] [info] Database connected successfully
[2025-06-09 16:07:13] [info] Database connected successfully
[2025-06-09 16:07:15] [info] Database connected successfully
[2025-06-09 16:07:15] [info] Database connected successfully
[2025-06-09 16:07:15] [info] Database connected successfully
[2025-06-09 16:07:19] [info] Database connected successfully
[2025-06-09 16:07:28] [info] Database connected successfully
[2025-06-09 16:08:41] [info] Database connected successfully
[2025-06-09 16:08:41] [info] Database connected successfully
[2025-06-09 16:08:42] [info] Database connected successfully
[2025-06-09 16:08:45] [info] Database connected successfully
[2025-06-09 16:08:45] [info] Database connected successfully
[2025-06-09 16:08:47] [info] Database connected successfully
[2025-06-09 16:13:00] [info] Database connected successfully
[2025-06-09 16:14:11] [info] Database connected successfully
[2025-06-09 16:14:23] [info] Database connected successfully
[2025-06-09 16:14:25] [info] Database connected successfully
[2025-06-09 16:14:37] [info] Database connected successfully
[2025-06-09 16:14:41] [info] Database connected successfully
[2025-06-09 16:14:41] [info] Database connected successfully
[2025-06-09 16:14:45] [info] Database connected successfully
[2025-06-09 16:14:46] [info] Database connected successfully
[2025-06-09 16:14:47] [info] Database connected successfully
[2025-06-09 16:14:47] [info] Database connected successfully
[2025-06-09 16:14:48] [info] Database connected successfully
[2025-06-09 16:14:53] [info] Database connected successfully
[2025-06-09 16:14:53] [info] Database connected successfully
[2025-06-09 16:14:55] [info] Database connected successfully
[2025-06-09 16:15:02] [info] Database connected successfully
[2025-06-09 16:15:02] [info] Database connected successfully
[2025-06-09 16:15:03] [info] Database connected successfully
[2025-06-09 16:15:05] [info] Database connected successfully
[2025-06-09 16:15:05] [info] Database connected successfully
[2025-06-09 16:15:06] [info] Database connected successfully
[2025-06-09 16:15:17] [info] Database connected successfully
[2025-06-09 16:15:17] [info] Database connected successfully
[2025-06-09 16:15:17] [info] Database connected successfully
[2025-06-09 16:15:23] [info] Database connected successfully
[2025-06-09 16:15:23] [info] Database connected successfully
[2025-06-09 16:19:21] [info] Database connected successfully
[2025-06-09 16:20:11] [info] Database connected successfully
[2025-06-09 16:20:13] [info] Database connected successfully
[2025-06-09 16:20:19] [info] Database connected successfully
[2025-06-09 16:20:27] [info] Database connected successfully
[2025-06-09 16:20:39] [info] Database connected successfully
[2025-06-09 16:28:46] [info] Database connected successfully
[2025-06-09 16:29:08] [info] Database connected successfully
[2025-06-09 16:29:09] [info] Database connected successfully
[2025-06-09 16:29:10] [info] Database connected successfully
[2025-06-09 16:29:10] [info] Database connected successfully
[2025-06-09 16:29:11] [info] Database connected successfully
[2025-06-09 16:29:14] [info] Database connected successfully
[2025-06-09 16:29:14] [info] Database connected successfully
[2025-06-09 16:29:14] [info] Database connected successfully
[2025-06-09 16:29:14] [info] Database connected successfully
[2025-06-09 16:29:22] [info] Database connected successfully
[2025-06-09 16:29:42] [info] Database connected successfully
[2025-06-09 16:29:42] [info] Database connected successfully
[2025-06-09 16:30:01] [info] Database connected successfully
[2025-06-09 16:30:02] [info] Database connected successfully
[2025-06-09 16:30:02] [info] Database connected successfully
[2025-06-09 16:30:02] [info] Database connected successfully
[2025-06-09 16:30:03] [info] Database connected successfully
[2025-06-09 16:30:09] [info] Database connected successfully
[2025-06-09 16:30:09] [info] Database connected successfully
[2025-06-09 16:30:09] [info] Database connected successfully
[2025-06-09 16:30:12] [info] Database connected successfully
[2025-06-09 16:30:12] [info] Database connected successfully
[2025-06-09 16:30:13] [info] Database connected successfully
[2025-06-09 16:30:24] [info] Database connected successfully
[2025-06-09 16:30:24] [info] Database connected successfully
[2025-06-09 16:30:25] [info] Database connected successfully
[2025-06-09 16:30:26] [info] Database connected successfully
[2025-06-09 16:30:27] [info] Database connected successfully
[2025-06-09 16:30:29] [info] Database connected successfully
[2025-06-09 16:30:31] [info] Database connected successfully
[2025-06-09 16:30:31] [info] Database connected successfully
[2025-06-09 16:30:32] [info] Database connected successfully
[2025-06-09 16:30:32] [info] Database connected successfully
[2025-06-09 16:30:33] [info] Database connected successfully
[2025-06-09 16:30:38] [info] Database connected successfully
[2025-06-09 16:30:46] [info] Database connected successfully
[2025-06-09 16:30:53] [info] Database connected successfully
[2025-06-09 16:30:59] [info] Database connected successfully
[2025-06-09 16:41:06] [info] Database connected successfully
[2025-06-09 16:42:16] [info] Database connected successfully
[2025-06-09 16:47:09] [info] Database connected successfully
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_name' SQL: ALTER TABLE document_intake ADD COLUMN client_name VARCHAR(255) NOT NULL DEFAULT 'Unknown Client' AFTER reference_number
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_id' SQL: ALTER TABLE document_intake ADD COLUMN client_id VARCHAR(50) NULL AFTER client_name
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_start' SQL: ALTER TABLE document_intake ADD COLUMN date_range_start DATE NULL AFTER expected_count
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'date_range_end' SQL: ALTER TABLE document_intake ADD COLUMN date_range_end DATE NULL AFTER date_range_start
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level ENUM('public', 'internal', 'confidential', 'restricted') DEFAULT 'internal' AFTER date_range_end
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(100) NULL AFTER sensitivity_level
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_type' SQL: ALTER TABLE bundles ADD COLUMN document_type VARCHAR(100) NULL COMMENT 'Document type as per documentation' AFTER box_id
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'year' SQL: ALTER TABLE bundles ADD COLUMN year INT NULL COMMENT 'Year as per documentation' AFTER document_type
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE bundles ADD COLUMN department VARCHAR(100) NULL COMMENT 'Department as per documentation' AFTER year
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'confidentiality_flag' SQL: ALTER TABLE bundles ADD COLUMN confidentiality_flag BOOLEAN DEFAULT FALSE COMMENT 'Confidentiality flag as per documentation' AFTER department
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'pages_volume' SQL: ALTER TABLE bundles ADD COLUMN pages_volume INT NULL COMMENT 'Pages/volume as per documentation' AFTER confidentiality_flag
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'scan_digitization_status' SQL: ALTER TABLE bundles ADD COLUMN scan_digitization_status ENUM('not_scanned', 'scanned', 'digitized', 'both') DEFAULT 'not_scanned' COMMENT 'Scan/digitization status as per documentation' AFTER pages_volume
[2025-06-09 16:47:09] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'contents_summary' SQL: ALTER TABLE bundles ADD COLUMN contents_summary TEXT NULL COMMENT 'Contents summary as per documentation' AFTER scan_digitization_status
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_document_type' SQL: CREATE INDEX idx_bundles_document_type ON bundles(document_type)
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_year' SQL: CREATE INDEX idx_bundles_year ON bundles(year)
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_bundles_department' SQL: CREATE INDEX idx_bundles_department ON bundles(department)
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity_percentage' SQL: ALTER TABLE warehouses ADD COLUMN capacity_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Current capacity utilization percentage'
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'efficiency_score' SQL: ALTER TABLE warehouses ADD COLUMN efficiency_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Calculated efficiency score'
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_capacity_update' SQL: ALTER TABLE warehouses ADD COLUMN last_capacity_update TIMESTAMP NULL COMMENT 'Last time capacity was calculated'
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_scanned_at' SQL: ALTER TABLE barcodes ADD COLUMN last_scanned_at TIMESTAMP NULL COMMENT 'Last time this barcode was scanned'
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_image_path' SQL: ALTER TABLE barcodes ADD COLUMN barcode_image_path VARCHAR(500) NULL COMMENT 'Path to generated barcode image'
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE documents ADD COLUMN reference_number VARCHAR(100) NULL COMMENT 'Document reference number for tracking'
[2025-06-09 16:47:10] [error] SQL execution failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'warehouse_id' SQL: ALTER TABLE documents ADD COLUMN warehouse_id INT NULL COMMENT 'Warehouse where document is stored'
[2025-06-09 16:49:04] [info] Database connected successfully
[2025-06-09 16:49:45] [info] Database connected successfully
[2025-06-09 16:49:45] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'timezone' in 'field list' SQL: UPDATE users SET 
                     first_name = ?, last_name = ?, email = ?, phone = ?, 
                     timezone = ?, language = ?, updated_at = NOW()
                     WHERE id = ?
[2025-06-09 16:49:45] [info] Database connected successfully
[2025-06-09 16:49:57] [info] Database connected successfully
[2025-06-09 16:49:57] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'timezone' in 'field list' SQL: UPDATE users SET 
                     first_name = ?, last_name = ?, email = ?, phone = ?, 
                     timezone = ?, language = ?, updated_at = NOW()
                     WHERE id = ?
[2025-06-09 16:49:57] [info] Database connected successfully
[2025-06-09 16:53:35] [info] Database connected successfully
[2025-06-09 16:53:47] [info] Database connected successfully
[2025-06-09 16:53:47] [info] Database connected successfully
[2025-06-09 16:54:11] [info] Database connected successfully
[2025-06-09 16:54:14] [info] Database connected successfully
[2025-06-09 16:54:18] [info] Database connected successfully
[2025-06-09 17:00:13] [info] Database connected successfully
[2025-06-09 17:00:22] [info] Database connected successfully
[2025-06-09 17:00:25] [info] Database connected successfully
[2025-06-09 17:00:27] [info] Database connected successfully
[2025-06-09 17:00:29] [info] Database connected successfully
[2025-06-09 17:00:33] [info] Database connected successfully
[2025-06-09 17:00:37] [info] Database connected successfully
[2025-06-09 17:00:39] [info] Database connected successfully
[2025-06-09 17:00:52] [info] Database connected successfully
[2025-06-09 17:00:55] [info] Database connected successfully
[2025-06-09 17:01:07] [info] Database connected successfully
[2025-06-09 17:01:09] [info] Database connected successfully
[2025-06-09 17:01:50] [info] Database connected successfully
[2025-06-09 17:01:54] [info] Database connected successfully
[2025-06-09 17:01:55] [info] Database connected successfully
[2025-06-09 17:01:56] [info] Database connected successfully
[2025-06-09 17:02:13] [info] Database connected successfully
[2025-06-09 17:02:15] [info] Database connected successfully
[2025-06-09 17:02:16] [info] Database connected successfully
[2025-06-09 17:02:21] [info] Database connected successfully
[2025-06-09 17:02:27] [info] Database connected successfully
[2025-06-09 17:09:18] [info] Database connected successfully
[2025-06-09 17:09:37] [info] Database connected successfully
[2025-06-09 17:09:55] [info] Database connected successfully
[2025-06-09 17:10:20] [info] Database connected successfully
[2025-06-09 17:10:22] [info] Database connected successfully
[2025-06-09 17:10:24] [info] Database connected successfully
[2025-06-09 17:10:26] [info] Database connected successfully
[2025-06-09 17:10:28] [info] Database connected successfully
[2025-06-09 17:10:30] [info] Database connected successfully
[2025-06-09 17:10:31] [info] Database connected successfully
[2025-06-09 17:10:36] [info] Database connected successfully
[2025-06-09 17:10:39] [info] Database connected successfully
[2025-06-09 17:10:41] [info] Database connected successfully
[2025-06-09 17:11:23] [info] Database connected successfully
[2025-06-09 17:11:33] [info] Database connected successfully
[2025-06-09 17:11:35] [info] Database connected successfully
[2025-06-09 17:11:37] [info] Database connected successfully
[2025-06-09 17:11:39] [info] Database connected successfully
[2025-06-09 17:11:41] [info] Database connected successfully
[2025-06-09 17:11:42] [info] Database connected successfully
[2025-06-09 17:11:43] [info] Database connected successfully
[2025-06-09 17:11:44] [info] Database connected successfully
[2025-06-09 17:11:46] [info] Database connected successfully
[2025-06-09 17:11:48] [info] Database connected successfully
[2025-06-09 17:11:51] [info] Database connected successfully
[2025-06-09 17:11:53] [info] Database connected successfully
[2025-06-09 17:12:03] [info] Database connected successfully
[2025-06-09 17:14:50] [info] Database connected successfully
[2025-06-09 17:14:57] [info] Database connected successfully
[2025-06-09 17:15:00] [info] Database connected successfully
[2025-06-09 17:15:01] [info] Database connected successfully
[2025-06-09 17:15:07] [info] Database connected successfully
[2025-06-09 17:15:09] [info] Database connected successfully
[2025-06-09 17:15:10] [info] Database connected successfully
[2025-06-09 17:15:15] [info] Database connected successfully
[2025-06-09 17:15:18] [info] Database connected successfully
[2025-06-09 17:15:33] [info] Database connected successfully
[2025-06-09 17:16:10] [info] Database connected successfully
[2025-06-09 17:16:25] [info] Database connected successfully
[2025-06-09 17:16:29] [info] Database connected successfully
[2025-06-09 17:16:31] [info] Database connected successfully
[2025-06-09 17:16:34] [info] Database connected successfully
[2025-06-09 17:20:41] [info] Database connected successfully
[2025-06-09 17:24:21] [info] Database connected successfully
[2025-06-09 17:24:22] [info] Database connected successfully
[2025-06-09 17:24:24] [info] Database connected successfully
[2025-06-09 17:24:25] [info] Database connected successfully
[2025-06-09 17:47:19] [info] Database connected successfully
[2025-06-09 17:47:19] [error] Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'dms_system.delivery_requests' doesn't exist SQL: SELECT dr.*, c.name as client_name, u.first_name, u.last_name,
                       COUNT(di.id) as item_count
                FROM delivery_requests dr
                LEFT JOIN companies c ON dr.client_id = c.id
                LEFT JOIN users u ON dr.created_by = u.id
                LEFT JOIN delivery_items di ON dr.id = di.delivery_id
                WHERE dr.company_id = ? AND (dr.delivery_reference LIKE ? OR c.name LIKE ?)
                GROUP BY dr.id
                ORDER BY dr.created_at DESC
[2025-06-09 17:50:01] [info] Database connected successfully
[2025-06-09 18:02:25] [info] Database connected successfully
[2025-06-09 18:02:25] [info] Database connected successfully
[2025-06-09 18:02:39] [info] Database connected successfully
[2025-06-09 18:02:40] [info] Database connected successfully
[2025-06-09 18:02:41] [info] Database connected successfully
[2025-06-09 18:02:46] [info] Database connected successfully
[2025-06-09 18:02:53] [info] Database connected successfully
[2025-06-09 18:02:55] [info] Database connected successfully
[2025-06-09 18:02:59] [info] Database connected successfully
[2025-06-09 18:03:05] [info] Database connected successfully
[2025-06-09 18:03:07] [info] Database connected successfully
[2025-06-09 18:03:07] [info] Database connected successfully
[2025-06-09 18:03:07] [info] Database connected successfully
[2025-06-09 18:03:07] [info] Database connected successfully
[2025-06-09 18:03:07] [info] Database connected successfully
[2025-06-09 18:03:51] [info] Database connected successfully
[2025-06-09 18:10:50] [info] Database connected successfully
[2025-06-09 18:11:36] [info] Database connected successfully
[2025-06-09 18:11:37] [info] Database connected successfully
[2025-06-09 18:11:42] [info] Database connected successfully
[2025-06-09 18:11:46] [info] Database connected successfully
[2025-06-09 18:11:50] [info] Database connected successfully
[2025-06-09 18:11:52] [info] Database connected successfully
[2025-06-09 18:11:57] [info] Database connected successfully
[2025-06-09 18:11:58] [info] Database connected successfully
[2025-06-09 18:11:58] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'reference_number' in 'field list' SQL: SELECT reference_number FROM delivery_requests 
             WHERE company_id = ? AND reference_number LIKE ? 
             ORDER BY reference_number DESC LIMIT 1
[2025-06-09 18:12:01] [info] Database connected successfully
[2025-06-09 18:12:01] [info] Database connected successfully
[2025-06-09 18:12:01] [info] Database connected successfully
[2025-06-09 18:12:01] [info] Database connected successfully
[2025-06-09 18:12:01] [info] Database connected successfully
[2025-06-09 18:18:04] [info] Database connected successfully
[2025-06-09 18:18:04] [info] Database connected successfully
[2025-06-09 18:18:08] [info] Database connected successfully
[2025-06-09 18:18:08] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'reference_number' in 'field list' SQL: SELECT reference_number FROM delivery_requests 
             WHERE company_id = ? AND reference_number LIKE ? 
             ORDER BY reference_number DESC LIMIT 1
[2025-06-09 18:18:10] [info] Database connected successfully
[2025-06-09 18:18:11] [info] Database connected successfully
[2025-06-09 18:18:13] [info] Database connected successfully
[2025-06-09 18:18:25] [info] Database connected successfully
[2025-06-09 18:18:25] [info] Database connected successfully
[2025-06-09 18:18:25] [info] Database connected successfully
[2025-06-09 18:18:25] [info] Database connected successfully
[2025-06-09 18:18:25] [info] Database connected successfully
[2025-06-09 18:18:29] [info] Database connected successfully
[2025-06-09 18:18:31] [info] Database connected successfully
[2025-06-09 18:18:33] [info] Database connected successfully
[2025-06-09 18:18:40] [info] Database connected successfully
[2025-06-09 18:18:40] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'reference_number' in 'field list' SQL: SELECT reference_number FROM delivery_requests 
             WHERE company_id = ? AND reference_number LIKE ? 
             ORDER BY reference_number DESC LIMIT 1
[2025-06-09 18:18:56] [info] Database connected successfully
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE bundles ADD COLUMN reference_number VARCHAR(50) UNIQUE
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'bundle_type' SQL: ALTER TABLE bundles ADD COLUMN bundle_type VARCHAR(50) DEFAULT 'custom'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'category' SQL: ALTER TABLE bundles ADD COLUMN category VARCHAR(100) DEFAULT 'general'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'priority' SQL: ALTER TABLE bundles ADD COLUMN priority VARCHAR(20) DEFAULT 'medium'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'retention_period' SQL: ALTER TABLE bundles ADD COLUMN retention_period INT DEFAULT 7
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'access_level' SQL: ALTER TABLE bundles ADD COLUMN access_level VARCHAR(20) DEFAULT 'private'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_count' SQL: ALTER TABLE bundles ADD COLUMN document_count INT DEFAULT 0
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'total_size' SQL: ALTER TABLE bundles ADD COLUMN total_size BIGINT DEFAULT 0
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'closed_at' SQL: ALTER TABLE bundles ADD COLUMN closed_at TIMESTAMP NULL
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'closed_by' SQL: ALTER TABLE bundles ADD COLUMN closed_by INT NULL
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE document_intake ADD COLUMN reference_number VARCHAR(50) UNIQUE
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'priority' SQL: ALTER TABLE document_intake ADD COLUMN priority VARCHAR(20) DEFAULT 'medium'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'expected_count' SQL: ALTER TABLE document_intake ADD COLUMN expected_count INT DEFAULT 0
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'actual_count' SQL: ALTER TABLE document_intake ADD COLUMN actual_count INT DEFAULT 0
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level VARCHAR(20) DEFAULT 'internal'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(255)
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'notes' SQL: ALTER TABLE document_intake ADD COLUMN notes TEXT
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'bundle_id' SQL: ALTER TABLE document_intake ADD COLUMN bundle_id INT NULL
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_prefix' SQL: ALTER TABLE boxes ADD COLUMN client_prefix VARCHAR(20)
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_number' SQL: ALTER TABLE boxes ADD COLUMN box_number INT
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_location_code' SQL: ALTER TABLE boxes ADD COLUMN storage_location_code VARCHAR(100)
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'row_number' SQL: ALTER TABLE boxes ADD COLUMN row_number VARCHAR(10) DEFAULT 'R1'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'shelf_number' SQL: ALTER TABLE boxes ADD COLUMN shelf_number VARCHAR(10) DEFAULT 'S1'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'position_number' SQL: ALTER TABLE boxes ADD COLUMN position_number VARCHAR(10)
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity' SQL: ALTER TABLE boxes ADD COLUMN capacity INT DEFAULT 100
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'current_count' SQL: ALTER TABLE boxes ADD COLUMN current_count INT DEFAULT 0
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_value' SQL: ALTER TABLE boxes ADD COLUMN barcode_value VARCHAR(255)
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_generated_at' SQL: ALTER TABLE boxes ADD COLUMN barcode_generated_at TIMESTAMP NULL
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_type' SQL: ALTER TABLE documents ADD COLUMN storage_type VARCHAR(20) DEFAULT 'physical'
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'tags' SQL: ALTER TABLE documents ADD COLUMN tags JSON
[2025-06-09 18:18:56] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'ocr_text' SQL: ALTER TABLE documents ADD COLUMN ocr_text TEXT
[2025-06-09 18:19:03] [info] Database connected successfully
[2025-06-09 18:19:03] [info] Database connected successfully
[2025-06-09 18:19:08] [info] Database connected successfully
[2025-06-09 18:19:08] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'reference_number' in 'field list' SQL: SELECT reference_number FROM delivery_requests 
             WHERE company_id = ? AND reference_number LIKE ? 
             ORDER BY reference_number DESC LIMIT 1
[2025-06-09 18:22:00] [info] Database connected successfully
[2025-06-09 18:22:01] [info] Database connected successfully
[2025-06-09 18:22:04] [info] Database connected successfully
[2025-06-09 18:22:04] [info] Database connected successfully
[2025-06-09 18:22:09] [info] Database connected successfully
[2025-06-09 18:22:09] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'reference_number' in 'field list' SQL: SELECT reference_number FROM delivery_requests 
             WHERE company_id = ? AND reference_number LIKE ? 
             ORDER BY reference_number DESC LIMIT 1
[2025-06-09 18:22:34] [info] Database connected successfully
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE bundles ADD COLUMN reference_number VARCHAR(50) UNIQUE
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'bundle_type' SQL: ALTER TABLE bundles ADD COLUMN bundle_type VARCHAR(50) DEFAULT 'custom'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'category' SQL: ALTER TABLE bundles ADD COLUMN category VARCHAR(100) DEFAULT 'general'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'priority' SQL: ALTER TABLE bundles ADD COLUMN priority VARCHAR(20) DEFAULT 'medium'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'retention_period' SQL: ALTER TABLE bundles ADD COLUMN retention_period INT DEFAULT 7
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'access_level' SQL: ALTER TABLE bundles ADD COLUMN access_level VARCHAR(20) DEFAULT 'private'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_count' SQL: ALTER TABLE bundles ADD COLUMN document_count INT DEFAULT 0
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'total_size' SQL: ALTER TABLE bundles ADD COLUMN total_size BIGINT DEFAULT 0
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'closed_at' SQL: ALTER TABLE bundles ADD COLUMN closed_at TIMESTAMP NULL
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'closed_by' SQL: ALTER TABLE bundles ADD COLUMN closed_by INT NULL
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE document_intake ADD COLUMN reference_number VARCHAR(50) UNIQUE
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'priority' SQL: ALTER TABLE document_intake ADD COLUMN priority VARCHAR(20) DEFAULT 'medium'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'expected_count' SQL: ALTER TABLE document_intake ADD COLUMN expected_count INT DEFAULT 0
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'actual_count' SQL: ALTER TABLE document_intake ADD COLUMN actual_count INT DEFAULT 0
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level VARCHAR(20) DEFAULT 'internal'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(255)
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'notes' SQL: ALTER TABLE document_intake ADD COLUMN notes TEXT
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'bundle_id' SQL: ALTER TABLE document_intake ADD COLUMN bundle_id INT NULL
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_prefix' SQL: ALTER TABLE boxes ADD COLUMN client_prefix VARCHAR(20)
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_number' SQL: ALTER TABLE boxes ADD COLUMN box_number INT
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_location_code' SQL: ALTER TABLE boxes ADD COLUMN storage_location_code VARCHAR(100)
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'row_number' SQL: ALTER TABLE boxes ADD COLUMN row_number VARCHAR(10) DEFAULT 'R1'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'shelf_number' SQL: ALTER TABLE boxes ADD COLUMN shelf_number VARCHAR(10) DEFAULT 'S1'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'position_number' SQL: ALTER TABLE boxes ADD COLUMN position_number VARCHAR(10)
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity' SQL: ALTER TABLE boxes ADD COLUMN capacity INT DEFAULT 100
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'current_count' SQL: ALTER TABLE boxes ADD COLUMN current_count INT DEFAULT 0
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_value' SQL: ALTER TABLE boxes ADD COLUMN barcode_value VARCHAR(255)
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_generated_at' SQL: ALTER TABLE boxes ADD COLUMN barcode_generated_at TIMESTAMP NULL
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_type' SQL: ALTER TABLE documents ADD COLUMN storage_type VARCHAR(20) DEFAULT 'physical'
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'tags' SQL: ALTER TABLE documents ADD COLUMN tags JSON
[2025-06-09 18:22:34] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'ocr_text' SQL: ALTER TABLE documents ADD COLUMN ocr_text TEXT
[2025-06-09 18:23:06] [info] Database connected successfully
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE bundles ADD COLUMN reference_number VARCHAR(50) UNIQUE
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'bundle_type' SQL: ALTER TABLE bundles ADD COLUMN bundle_type VARCHAR(50) DEFAULT 'custom'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'category' SQL: ALTER TABLE bundles ADD COLUMN category VARCHAR(100) DEFAULT 'general'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'priority' SQL: ALTER TABLE bundles ADD COLUMN priority VARCHAR(20) DEFAULT 'medium'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'retention_period' SQL: ALTER TABLE bundles ADD COLUMN retention_period INT DEFAULT 7
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'access_level' SQL: ALTER TABLE bundles ADD COLUMN access_level VARCHAR(20) DEFAULT 'private'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'document_count' SQL: ALTER TABLE bundles ADD COLUMN document_count INT DEFAULT 0
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'total_size' SQL: ALTER TABLE bundles ADD COLUMN total_size BIGINT DEFAULT 0
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'closed_at' SQL: ALTER TABLE bundles ADD COLUMN closed_at TIMESTAMP NULL
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'closed_by' SQL: ALTER TABLE bundles ADD COLUMN closed_by INT NULL
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'reference_number' SQL: ALTER TABLE document_intake ADD COLUMN reference_number VARCHAR(50) UNIQUE
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'priority' SQL: ALTER TABLE document_intake ADD COLUMN priority VARCHAR(20) DEFAULT 'medium'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'expected_count' SQL: ALTER TABLE document_intake ADD COLUMN expected_count INT DEFAULT 0
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'actual_count' SQL: ALTER TABLE document_intake ADD COLUMN actual_count INT DEFAULT 0
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'sensitivity_level' SQL: ALTER TABLE document_intake ADD COLUMN sensitivity_level VARCHAR(20) DEFAULT 'internal'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'department' SQL: ALTER TABLE document_intake ADD COLUMN department VARCHAR(255)
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'notes' SQL: ALTER TABLE document_intake ADD COLUMN notes TEXT
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'bundle_id' SQL: ALTER TABLE document_intake ADD COLUMN bundle_id INT NULL
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'client_prefix' SQL: ALTER TABLE boxes ADD COLUMN client_prefix VARCHAR(20)
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'box_number' SQL: ALTER TABLE boxes ADD COLUMN box_number INT
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_location_code' SQL: ALTER TABLE boxes ADD COLUMN storage_location_code VARCHAR(100)
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'row_number' SQL: ALTER TABLE boxes ADD COLUMN row_number VARCHAR(10) DEFAULT 'R1'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'shelf_number' SQL: ALTER TABLE boxes ADD COLUMN shelf_number VARCHAR(10) DEFAULT 'S1'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'position_number' SQL: ALTER TABLE boxes ADD COLUMN position_number VARCHAR(10)
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'capacity' SQL: ALTER TABLE boxes ADD COLUMN capacity INT DEFAULT 100
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'current_count' SQL: ALTER TABLE boxes ADD COLUMN current_count INT DEFAULT 0
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_value' SQL: ALTER TABLE boxes ADD COLUMN barcode_value VARCHAR(255)
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'barcode_generated_at' SQL: ALTER TABLE boxes ADD COLUMN barcode_generated_at TIMESTAMP NULL
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'storage_type' SQL: ALTER TABLE documents ADD COLUMN storage_type VARCHAR(20) DEFAULT 'physical'
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'tags' SQL: ALTER TABLE documents ADD COLUMN tags JSON
[2025-06-09 18:23:07] [error] Query failed: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'ocr_text' SQL: ALTER TABLE documents ADD COLUMN ocr_text TEXT
[2025-06-09 18:23:13] [info] Database connected successfully
[2025-06-09 18:23:14] [info] Database connected successfully
[2025-06-09 18:23:18] [info] Database connected successfully
[2025-06-09 18:23:18] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'reference_number' in 'field list' SQL: SELECT reference_number FROM delivery_requests 
             WHERE company_id = ? AND reference_number LIKE ? 
             ORDER BY reference_number DESC LIMIT 1
[2025-06-09 18:25:06] [info] Database connected successfully
[2025-06-09 18:25:06] [info] Database connected successfully
[2025-06-09 18:25:09] [info] Database connected successfully
[2025-06-09 18:25:14] [info] Database connected successfully
[2025-06-09 18:25:14] [info] Database connected successfully
[2025-06-09 18:25:14] [info] Database connected successfully
[2025-06-09 18:25:14] [info] Database connected successfully
[2025-06-09 18:25:14] [info] Database connected successfully
[2025-06-09 18:25:16] [info] Database connected successfully
[2025-06-09 18:25:17] [info] Database connected successfully
[2025-06-09 18:25:46] [info] Database connected successfully
[2025-06-09 23:27:18] [info] Database connected successfully
[2025-06-09 23:27:21] [info] Database connected successfully
[2025-06-09 23:27:32] [info] Database connected successfully
[2025-06-09 23:27:32] [info] Database connected successfully
[2025-06-09 23:27:55] [info] Database connected successfully
[2025-06-09 23:28:07] [info] Database connected successfully
[2025-06-09 23:28:22] [info] Database connected successfully
[2025-06-09 23:28:31] [info] Database connected successfully
[2025-06-09 23:28:51] [info] Database connected successfully
[2025-06-09 23:29:07] [info] Database connected successfully
[2025-06-09 23:29:13] [info] Database connected successfully
[2025-06-09 23:29:21] [info] Database connected successfully
[2025-06-09 23:29:24] [info] Database connected successfully
[2025-06-09 23:29:25] [info] Database connected successfully
[2025-06-09 23:29:28] [info] Database connected successfully
[2025-06-09 23:29:34] [info] Database connected successfully
[2025-06-09 23:29:47] [info] Database connected successfully
[2025-06-09 23:29:51] [info] Database connected successfully
[2025-06-09 23:29:57] [info] Database connected successfully
[2025-06-09 23:29:59] [info] Database connected successfully
[2025-06-09 23:30:08] [info] Database connected successfully
[2025-06-09 23:30:10] [info] Database connected successfully
[2025-06-09 23:30:19] [info] Database connected successfully
[2025-06-10 00:50:12] [info] Database connected successfully
[2025-06-10 00:50:15] [info] Database connected successfully
[2025-06-10 00:50:27] [info] Database connected successfully
[2025-06-10 00:50:27] [info] Database connected successfully
[2025-06-10 00:50:52] [info] Database connected successfully
[2025-06-10 00:54:06] [info] Database connected successfully
[2025-06-10 00:56:29] [info] Database connected successfully
[2025-06-10 00:57:33] [info] Database connected successfully
[2025-06-10 01:03:19] [info] Database connected successfully
[2025-06-10 01:03:22] [info] Database connected successfully
[2025-06-10 01:05:19] [info] Database connected successfully
[2025-06-10 01:05:26] [info] Database connected successfully
[2025-06-10 01:05:28] [info] Database connected successfully
[2025-06-10 01:08:01] [info] Database connected successfully
[2025-06-10 01:08:24] [info] Database connected successfully
[2025-06-10 01:08:25] [info] Database connected successfully
[2025-06-10 01:08:26] [info] Database connected successfully
[2025-06-10 01:08:28] [info] Database connected successfully
[2025-06-10 01:09:28] [info] Database connected successfully
[2025-06-10 01:09:38] [info] Database connected successfully
[2025-06-10 01:09:41] [info] Database connected successfully
[2025-06-10 01:12:45] [info] Database connected successfully
[2025-06-10 01:15:20] [info] Database connected successfully
[2025-06-10 01:15:33] [info] Database connected successfully
[2025-06-10 01:15:46] [info] Database connected successfully
[2025-06-10 01:21:09] [info] Database connected successfully
[2025-06-10 01:25:00] [info] Database connected successfully
[2025-06-10 01:25:38] [info] Database connected successfully
[2025-06-10 01:27:49] [info] Database connected successfully
[2025-06-10 01:28:03] [info] Database connected successfully
[2025-06-10 01:28:07] [info] Database connected successfully
[2025-06-10 01:29:04] [info] Database connected successfully
[2025-06-10 01:29:09] [info] Database connected successfully
[2025-06-10 01:32:23] [info] Database connected successfully
[2025-06-10 01:33:49] [info] Database connected successfully
[2025-06-10 01:34:39] [info] Database connected successfully
[2025-06-10 01:35:23] [info] Database connected successfully
[2025-06-10 01:35:25] [info] Database connected successfully
[2025-06-10 01:37:31] [info] Database connected successfully
[2025-06-10 01:37:33] [info] Database connected successfully
[2025-06-10 01:37:35] [info] Database connected successfully
[2025-06-10 01:38:43] [info] Database connected successfully
[2025-06-10 01:39:11] [info] Database connected successfully
[2025-06-10 01:39:12] [info] Database connected successfully
[2025-06-10 01:39:33] [info] Database connected successfully
[2025-06-10 01:39:45] [info] Database connected successfully
[2025-06-10 01:39:52] [info] Database connected successfully
[2025-06-10 01:39:54] [info] Database connected successfully
[2025-06-10 01:39:57] [info] Database connected successfully
[2025-06-10 01:40:11] [info] Database connected successfully
[2025-06-10 01:40:14] [info] Database connected successfully
[2025-06-10 01:40:16] [info] Database connected successfully
[2025-06-10 01:40:18] [info] Database connected successfully
[2025-06-10 01:40:19] [info] Database connected successfully
[2025-06-10 01:44:42] [info] Database connected successfully
[2025-06-10 01:45:14] [info] Database connected successfully
[2025-06-10 01:45:17] [info] Database connected successfully
[2025-06-10 01:46:30] [info] Database connected successfully
[2025-06-10 01:46:36] [info] Database connected successfully
[2025-06-10 01:46:37] [info] Database connected successfully
[2025-06-10 01:51:43] [info] Database connected successfully
[2025-06-10 01:51:48] [info] Database connected successfully
[2025-06-10 01:51:55] [info] Database connected successfully
[2025-06-10 01:52:04] [info] Database connected successfully
[2025-06-10 01:52:07] [info] Database connected successfully
[2025-06-10 01:52:09] [info] Database connected successfully
[2025-06-10 01:54:32] [info] Database connected successfully
[2025-06-10 01:54:36] [info] Database connected successfully
[2025-06-10 01:54:39] [info] Database connected successfully
[2025-06-10 01:54:41] [info] Database connected successfully
[2025-06-10 01:59:26] [info] Database connected successfully
[2025-06-10 02:03:19] [info] Database connected successfully
[2025-06-10 02:07:04] [info] Database connected successfully
[2025-06-10 02:08:27] [info] Database connected successfully
[2025-06-10 02:08:27] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:08:27] [info] Database connected successfully
[2025-06-10 02:08:46] [info] Database connected successfully
[2025-06-10 02:08:46] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:08:46] [info] Database connected successfully
[2025-06-10 02:09:05] [info] Database connected successfully
[2025-06-10 02:09:05] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:09:05] [info] Database connected successfully
[2025-06-10 02:09:36] [info] Database connected successfully
[2025-06-10 02:09:36] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:09:36] [info] Database connected successfully
[2025-06-10 02:15:55] [info] Database connected successfully
[2025-06-10 02:16:06] [info] Database connected successfully
[2025-06-10 02:16:15] [info] Database connected successfully
[2025-06-10 02:16:15] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:16:15] [info] Database connected successfully
[2025-06-10 02:16:28] [info] Database connected successfully
[2025-06-10 02:16:40] [info] Database connected successfully
[2025-06-10 02:16:44] [info] Database connected successfully
[2025-06-10 02:18:14] [info] Database connected successfully
[2025-06-10 02:18:28] [info] Database connected successfully
[2025-06-10 02:18:28] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:18:28] [info] Database connected successfully
[2025-06-10 02:18:48] [info] Database connected successfully
[2025-06-10 02:18:48] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:18:48] [info] Database connected successfully
[2025-06-10 02:28:07] [info] Database connected successfully
[2025-06-10 02:28:14] [info] Database connected successfully
[2025-06-10 02:28:14] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:28:14] [info] Database connected successfully
[2025-06-10 02:31:59] [info] Database connected successfully
[2025-06-10 02:32:09] [info] Database connected successfully
[2025-06-10 02:32:09] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:32:09] [info] Database connected successfully
[2025-06-10 02:35:58] [info] Database connected successfully
[2025-06-10 02:36:06] [info] Database connected successfully
[2025-06-10 02:36:24] [info] Database connected successfully
[2025-06-10 02:36:24] [error] Query failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'Array' in 'where clause' SQL: DELETE FROM billing_rates WHERE Array
[2025-06-10 02:36:24] [info] Database connected successfully
[2025-06-10 02:36:40] [info] Database connected successfully
[2025-06-10 02:36:50] [info] Database connected successfully
