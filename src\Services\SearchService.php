<?php

namespace App\Services;

use App\Services\BillingEventLogger;

/**
 * Enhanced Search Service
 * 
 * Provides comprehensive search functionality across all entities:
 * - Documents, Bundles, Boxes
 * - Advanced filtering and sorting
 * - Full-text search with relevance scoring
 * - Search analytics and suggestions
 */
class SearchService extends BaseService
{
    /**
     * Perform unified search across all entities
     */
    public function unifiedSearch($query, $filters = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $startTime = microtime(true);
        
        $searchTypes = $filters['search_types'] ?? ['documents', 'bundles', 'boxes'];
        $results = [];

        try {
            // Search each entity type
            if (in_array('documents', $searchTypes)) {
                $documentResults = $this->searchDocuments($query, $filters);
                foreach ($documentResults as $doc) {
                    $doc['result_type'] = 'document';
                    $doc['result_icon'] = '📄';
                    $doc['result_url'] = '/app/documents/' . $doc['id'];
                    $results[] = $doc;
                }
            }

            if (in_array('bundles', $searchTypes)) {
                $bundleResults = $this->searchBundles($query, $filters);
                foreach ($bundleResults as $bundle) {
                    $bundle['result_type'] = 'bundle';
                    $bundle['result_icon'] = '📁';
                    $bundle['result_url'] = '/app/bundles/' . $bundle['id'];
                    $results[] = $bundle;
                }
            }

            if (in_array('boxes', $searchTypes)) {
                $boxResults = $this->searchBoxes($query, $filters);
                foreach ($boxResults as $box) {
                    $box['result_type'] = 'box';
                    $box['result_icon'] = '📦';
                    $box['result_url'] = '/app/boxes/' . $box['id'];
                    $results[] = $box;
                }
            }

            // Sort results by relevance if specified
            if (($filters['sort'] ?? 'relevance') === 'relevance') {
                $results = $this->sortByRelevance($results, $query);
            }

            // Apply pagination
            $page = $filters['page'] ?? 1;
            $perPage = $filters['per_page'] ?? 20;
            $offset = ($page - 1) * $perPage;
            
            $totalResults = count($results);
            $paginatedResults = array_slice($results, $offset, $perPage);

            $searchTime = round((microtime(true) - $startTime) * 1000, 2);

            // Log search activity
            $this->logSearchActivity($query, $filters, $totalResults, $searchTime);

            // Log billing event for search operation
            if (!empty($filters['client_id'])) {
                $searchType = 'unified';
                if (!empty($searchTypes)) {
                    $searchType = $searchTypes[0]; // Use the first search type
                }

                BillingEventLogger::logSearch(
                    $filters['client_id'],
                    $searchType,
                    null,
                    "Search: {$query}"
                );
            }

            return [
                'results' => $paginatedResults,
                'total_count' => $totalResults,
                'search_time' => $searchTime,
                'page' => $page,
                'per_page' => $perPage,
                'total_pages' => ceil($totalResults / $perPage),
                'query' => $query,
                'filters' => $filters
            ];

        } catch (\Exception $e) {
            throw new \Exception('Search failed: ' . $e->getMessage());
        }
    }

    /**
     * Search documents with advanced filters
     */
    public function searchDocuments($query, $filters = [])
    {
        $where = ["d.company_id = ?", "d.status != 'deleted'"];
        $params = [$this->user['company_id']];
        $joins = [];

        // Full-text search
        if (!empty($query)) {
            $searchTerms = explode(' ', trim($query));
            $searchConditions = [];
            
            foreach ($searchTerms as $term) {
                if (!empty($term)) {
                    $searchConditions[] = "(d.title LIKE ? OR d.description LIKE ? OR d.file_name LIKE ? OR d.ocr_text LIKE ?)";
                    $searchTerm = '%' . $term . '%';
                    $params[] = $searchTerm;
                    $params[] = $searchTerm;
                    $params[] = $searchTerm;
                    $params[] = $searchTerm;
                }
            }

            if (!empty($searchConditions)) {
                $where[] = '(' . implode(' AND ', $searchConditions) . ')';
            }
        }

        // Apply filters
        $this->applyDocumentFilters($where, $params, $joins, $filters);

        // Build query
        $joinClause = implode(' ', $joins);
        $whereClause = implode(' AND ', $where);
        $orderBy = $this->getDocumentOrderBy($filters);

        $sql = "SELECT d.*, u.first_name, u.last_name, c.name as category_name,
                       b.name as bundle_name, b.reference_number as bundle_reference
                FROM documents d 
                LEFT JOIN users u ON d.created_by = u.id 
                LEFT JOIN categories c ON d.category_id = c.id 
                LEFT JOIN bundles b ON d.bundle_id = b.id
                {$joinClause}
                WHERE {$whereClause} 
                ORDER BY {$orderBy}";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Search bundles with advanced filters
     */
    public function searchBundles($query, $filters = [])
    {
        $where = ["b.company_id = ?"];
        $params = [$this->user['company_id']];

        // Full-text search
        if (!empty($query)) {
            $where[] = "(b.name LIKE ? OR b.reference_number LIKE ? OR b.description LIKE ?)";
            $searchTerm = '%' . $query . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // Apply filters
        $this->applyBundleFilters($where, $params, $filters);

        $whereClause = implode(' AND ', $where);
        $orderBy = $this->getBundleOrderBy($filters);

        $sql = "SELECT b.*, 
                       COUNT(d.id) as document_count,
                       SUM(d.file_size) as total_size,
                       u.first_name, u.last_name
                FROM bundles b
                LEFT JOIN documents d ON b.id = d.bundle_id AND d.status != 'deleted'
                LEFT JOIN users u ON b.created_by = u.id
                WHERE {$whereClause}
                GROUP BY b.id
                ORDER BY {$orderBy}";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Search boxes with advanced filters
     */
    public function searchBoxes($query, $filters = [])
    {
        $where = ["b.company_id = ?"];
        $params = [$this->user['company_id']];

        // Full-text search
        if (!empty($query)) {
            $where[] = "(b.box_id LIKE ? OR b.name LIKE ? OR b.storage_location_code LIKE ? OR b.barcode_value LIKE ?)";
            $searchTerm = '%' . $query . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // Apply filters
        $this->applyBoxFilters($where, $params, $filters);

        $whereClause = implode(' AND ', $where);
        $orderBy = $this->getBoxOrderBy($filters);

        $sql = "SELECT b.*, w.name as warehouse_name,
                       COUNT(bb.bundle_id) as bundle_count
                FROM boxes b
                LEFT JOIN warehouses w ON b.warehouse_id = w.id
                LEFT JOIN box_bundles bb ON b.id = bb.box_id
                WHERE {$whereClause}
                GROUP BY b.id
                ORDER BY {$orderBy}";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get search suggestions based on query
     */
    public function getSearchSuggestions($query, $limit = 10)
    {
        $this->validateCompanyAccess($this->user['company_id']);

        if (strlen($query) < 2) {
            return [];
        }

        $suggestions = [];

        // Document suggestions
        $documentSuggestions = $this->db->fetchAll(
            "SELECT DISTINCT title as suggestion, 'document' as type, 'title' as field
             FROM documents
             WHERE company_id = ? AND status != 'deleted'
             AND title LIKE ?
             ORDER BY title
             LIMIT ?",
            [$this->user['company_id'], '%' . $query . '%', $limit]
        );

        // Bundle suggestions
        $bundleSuggestions = $this->db->fetchAll(
            "SELECT DISTINCT name as suggestion, 'bundle' as type, 'name' as field
             FROM bundles
             WHERE company_id = ?
             AND (name LIKE ? OR reference_number LIKE ?)
             ORDER BY name
             LIMIT ?",
            [$this->user['company_id'], '%' . $query . '%', '%' . $query . '%', $limit]
        );

        // Box suggestions
        $boxSuggestions = $this->db->fetchAll(
            "SELECT DISTINCT box_id as suggestion, 'box' as type, 'box_id' as field
             FROM boxes
             WHERE company_id = ?
             AND (box_id LIKE ? OR storage_location_code LIKE ?)
             ORDER BY box_id
             LIMIT ?",
            [$this->user['company_id'], '%' . $query . '%', '%' . $query . '%', $limit]
        );

        // Combine and limit suggestions
        $suggestions = array_merge($documentSuggestions, $bundleSuggestions, $boxSuggestions);
        
        // Remove duplicates and limit
        $uniqueSuggestions = [];
        $seen = [];
        
        foreach ($suggestions as $suggestion) {
            $key = $suggestion['suggestion'] . '_' . $suggestion['type'];
            if (!isset($seen[$key]) && count($uniqueSuggestions) < $limit) {
                $seen[$key] = true;
                $uniqueSuggestions[] = $suggestion;
            }
        }

        return $uniqueSuggestions;
    }

    /**
     * Get search analytics
     */
    public function getSearchAnalytics($dateRange = [])
    {
        $this->validateCompanyAccess($this->user['company_id']);

        $where = ["company_id = ?"];
        $params = [$this->user['company_id']];

        if (!empty($dateRange['from'])) {
            $where[] = "DATE(created_at) >= ?";
            $params[] = $dateRange['from'];
        }

        if (!empty($dateRange['to'])) {
            $where[] = "DATE(created_at) <= ?";
            $params[] = $dateRange['to'];
        }

        $whereClause = implode(' AND ', $where);

        // Top search queries
        $topQueries = $this->db->fetchAll(
            "SELECT search_query, COUNT(*) as search_count
             FROM search_logs
             WHERE {$whereClause}
             GROUP BY search_query
             ORDER BY search_count DESC
             LIMIT 10",
            $params
        );

        // Search volume by date
        $searchVolume = $this->db->fetchAll(
            "SELECT DATE(created_at) as date, COUNT(*) as searches
             FROM search_logs
             WHERE {$whereClause}
             GROUP BY DATE(created_at)
             ORDER BY date DESC
             LIMIT 30",
            $params
        );

        // Average search time
        $avgSearchTime = $this->db->fetch(
            "SELECT AVG(search_time_ms) as avg_time
             FROM search_logs
             WHERE {$whereClause}",
            $params
        );

        return [
            'top_queries' => $topQueries,
            'search_volume' => $searchVolume,
            'average_search_time' => $avgSearchTime['avg_time'] ?? 0
        ];
    }

    /**
     * Apply document-specific filters
     */
    private function applyDocumentFilters(&$where, &$params, &$joins, $filters)
    {
        if (!empty($filters['document_type'])) {
            $where[] = "d.document_type = ?";
            $params[] = $filters['document_type'];
        }

        if (!empty($filters['category_id'])) {
            $where[] = "d.category_id = ?";
            $params[] = $filters['category_id'];
        }

        if (!empty($filters['bundle_id'])) {
            $where[] = "d.bundle_id = ?";
            $params[] = $filters['bundle_id'];
        }

        if (!empty($filters['storage_type'])) {
            $where[] = "d.storage_type = ?";
            $params[] = $filters['storage_type'];
        }

        if (!empty($filters['date_from'])) {
            $where[] = "DATE(d.created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where[] = "DATE(d.created_at) <= ?";
            $params[] = $filters['date_to'];
        }

        if (!empty($filters['file_size_min'])) {
            $where[] = "d.file_size >= ?";
            $params[] = $filters['file_size_min'];
        }

        if (!empty($filters['file_size_max'])) {
            $where[] = "d.file_size <= ?";
            $params[] = $filters['file_size_max'];
        }
    }

    /**
     * Apply bundle-specific filters
     */
    private function applyBundleFilters(&$where, &$params, $filters)
    {
        if (!empty($filters['status'])) {
            $where[] = "b.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['bundle_type'])) {
            $where[] = "b.bundle_type = ?";
            $params[] = $filters['bundle_type'];
        }

        if (!empty($filters['category'])) {
            $where[] = "b.category = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['priority'])) {
            $where[] = "b.priority = ?";
            $params[] = $filters['priority'];
        }

        if (!empty($filters['date_from'])) {
            $where[] = "DATE(b.created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where[] = "DATE(b.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
    }

    /**
     * Apply box-specific filters
     */
    private function applyBoxFilters(&$where, &$params, $filters)
    {
        if (!empty($filters['status'])) {
            $where[] = "b.status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['storage_type'])) {
            $where[] = "b.storage_type = ?";
            $params[] = $filters['storage_type'];
        }

        if (!empty($filters['warehouse_id'])) {
            $where[] = "b.warehouse_id = ?";
            $params[] = $filters['warehouse_id'];
        }

        if (!empty($filters['client_prefix'])) {
            $where[] = "b.client_prefix = ?";
            $params[] = $filters['client_prefix'];
        }

        if (!empty($filters['date_from'])) {
            $where[] = "DATE(b.created_at) >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where[] = "DATE(b.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
    }

    /**
     * Get document ordering
     */
    private function getDocumentOrderBy($filters)
    {
        switch ($filters['sort'] ?? 'created_at') {
            case 'title':
                return 'd.title ASC';
            case 'size':
                return 'd.file_size DESC';
            case 'type':
                return 'd.document_type ASC, d.title ASC';
            case 'relevance':
                return 'd.created_at DESC'; // Fallback for relevance
            default:
                return 'd.created_at DESC';
        }
    }

    /**
     * Get bundle ordering
     */
    private function getBundleOrderBy($filters)
    {
        switch ($filters['sort'] ?? 'created_at') {
            case 'name':
                return 'b.name ASC';
            case 'status':
                return 'b.status ASC, b.name ASC';
            case 'document_count':
                return 'document_count DESC';
            case 'size':
                return 'total_size DESC';
            default:
                return 'b.created_at DESC';
        }
    }

    /**
     * Get box ordering
     */
    private function getBoxOrderBy($filters)
    {
        switch ($filters['sort'] ?? 'created_at') {
            case 'box_id':
                return 'b.box_id ASC';
            case 'location':
                return 'b.storage_location_code ASC';
            case 'status':
                return 'b.status ASC, b.box_id ASC';
            case 'capacity':
                return 'b.current_count DESC';
            default:
                return 'b.created_at DESC';
        }
    }

    /**
     * Sort results by relevance
     */
    private function sortByRelevance($results, $query)
    {
        $queryTerms = explode(' ', strtolower(trim($query)));

        foreach ($results as &$result) {
            $score = 0;
            $text = strtolower(($result['title'] ?? $result['name'] ?? '') . ' ' . ($result['description'] ?? ''));

            foreach ($queryTerms as $term) {
                if (!empty($term)) {
                    // Exact match in title/name gets highest score
                    if (strpos(strtolower($result['title'] ?? $result['name'] ?? ''), $term) !== false) {
                        $score += 10;
                    }

                    // Match in description gets medium score
                    if (strpos(strtolower($result['description'] ?? ''), $term) !== false) {
                        $score += 5;
                    }

                    // Partial matches get lower score
                    $score += substr_count($text, $term);
                }
            }

            $result['relevance_score'] = $score;
        }

        // Sort by relevance score descending
        usort($results, function($a, $b) {
            return ($b['relevance_score'] ?? 0) - ($a['relevance_score'] ?? 0);
        });

        return $results;
    }

    /**
     * Log search activity for analytics
     */
    private function logSearchActivity($query, $filters, $resultCount, $searchTime)
    {
        try {
            $this->db->execute(
                "INSERT INTO search_logs (
                    user_id, company_id, search_query, search_filters,
                    result_count, search_time_ms, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())",
                [
                    $this->user['id'],
                    $this->user['company_id'],
                    $query,
                    json_encode($filters),
                    $resultCount,
                    $searchTime
                ]
            );
        } catch (\Exception $e) {
            // Don't fail search if logging fails
            error_log('Failed to log search activity: ' . $e->getMessage());
        }
    }
}
