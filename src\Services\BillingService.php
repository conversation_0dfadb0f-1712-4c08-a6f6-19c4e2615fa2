<?php

namespace App\Services;

use App\Core\Database;
use Exception;

/**
 * Billing Service
 * 
 * Handles all billing operations including event logging, rate management,
 * and invoice generation for the document management system
 */
class BillingService
{
    private $db;
    private $user;
    private $company;

    public function __construct(Database $db, $user = null, $company = null)
    {
        $this->db = $db;
        $this->user = $user;
        $this->company = $company;
    }

    /**
     * Log a billable service event
     */
    public function logServiceEvent($data)
    {
        try {
            // Validate required fields
            $required = ['client_id', 'service_code', 'event_type'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new Exception("Missing required field: {$field}");
                }
            }

            // Get service rate
            $rate = $this->getServiceRate($data['service_code']);
            if (!$rate) {
                throw new Exception("Invalid service code: {$data['service_code']}");
            }

            // Calculate total amount
            $quantity = $data['quantity'] ?? 1.00;
            $unitRate = $data['unit_rate'] ?? $rate['rate'];
            $totalAmount = $quantity * $unitRate;

            // Prepare event data
            $eventData = [
                'company_id' => $this->company['id'] ?? $data['company_id'],
                'client_id' => $data['client_id'],
                'service_code' => $data['service_code'],
                'event_type' => $data['event_type'],
                'quantity' => $quantity,
                'unit_rate' => $unitRate,
                'total_amount' => $totalAmount,
                'entity_type' => $data['entity_type'] ?? null,
                'entity_id' => $data['entity_id'] ?? null,
                'reference_number' => $data['reference_number'] ?? null,
                'performed_by' => $this->user['id'] ?? $data['performed_by'],
                'notes' => $data['notes'] ?? null,
                'metadata' => isset($data['metadata']) ? json_encode($data['metadata']) : null
            ];

            // Insert billing event
            $eventId = $this->db->insert('billing_events', $eventData);

            return [
                'success' => true,
                'event_id' => $eventId,
                'total_amount' => $totalAmount,
                'service_name' => $rate['service_name']
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get service rate by code
     */
    public function getServiceRate($serviceCode)
    {
        return $this->db->fetch(
            "SELECT * FROM billing_rates WHERE service_code = ? AND is_active = 1",
            [$serviceCode]
        );
    }

    /**
     * Get service rate by ID
     */
    public function getServiceRateById($rateId)
    {
        return $this->db->fetch(
            "SELECT * FROM billing_rates WHERE id = ?",
            [$rateId]
        );
    }

    /**
     * Get all active service rates
     */
    public function getAllServiceRates($category = null)
    {
        $sql = "SELECT * FROM billing_rates WHERE is_active = 1";
        $params = [];

        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }

        $sql .= " ORDER BY category, service_name";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get billing events for a client within a date range
     */
    public function getClientBillingEvents($clientId, $startDate, $endDate, $status = null)
    {
        $sql = "SELECT be.*, br.service_name, br.category, br.unit
                FROM billing_events be
                JOIN billing_rates br ON be.service_code = br.service_code
                WHERE be.client_id = ? 
                AND be.performed_at BETWEEN ? AND ?";
        
        $params = [$clientId, $startDate, $endDate];

        if ($status) {
            $sql .= " AND be.billing_status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY be.performed_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Generate monthly invoice for a client
     */
    public function generateMonthlyInvoice($clientId, $year, $month)
    {
        try {
            // Calculate billing period
            $startDate = sprintf('%04d-%02d-01', $year, $month);
            $endDate = date('Y-m-t', strtotime($startDate));

            // Get unbilled events for the period
            $events = $this->getClientBillingEvents($clientId, $startDate, $endDate, 'pending');

            if (empty($events)) {
                return [
                    'success' => false,
                    'error' => 'No billable events found for the specified period'
                ];
            }

            // Group events by service
            $lineItems = [];
            $subtotal = 0;

            foreach ($events as $event) {
                $serviceCode = $event['service_code'];
                
                if (!isset($lineItems[$serviceCode])) {
                    $lineItems[$serviceCode] = [
                        'service_code' => $serviceCode,
                        'service_name' => $event['service_name'],
                        'unit' => $event['unit'],
                        'quantity' => 0,
                        'unit_rate' => $event['unit_rate'],
                        'total_amount' => 0
                    ];
                }

                $lineItems[$serviceCode]['quantity'] += $event['quantity'];
                $lineItems[$serviceCode]['total_amount'] += $event['total_amount'];
                $subtotal += $event['total_amount'];
            }

            // Get client information
            $client = $this->db->fetch("SELECT * FROM companies WHERE id = ?", [$clientId]);
            if (!$client) {
                throw new Exception("Client not found");
            }

            // Calculate tax (default 0% - can be configured per client)
            $taxRate = 0.0000; // Can be made configurable
            $taxAmount = $subtotal * $taxRate;
            $totalAmount = $subtotal + $taxAmount;

            // Generate invoice number
            $invoiceNumber = $this->generateInvoiceNumber($year, $month);

            // Create invoice
            $invoiceData = [
                'company_id' => $this->company['id'],
                'client_id' => $clientId,
                'invoice_number' => $invoiceNumber,
                'invoice_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+30 days')),
                'billing_period_start' => $startDate,
                'billing_period_end' => $endDate,
                'subtotal' => $subtotal,
                'tax_rate' => $taxRate,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'line_items' => json_encode(array_values($lineItems)),
                'created_by' => $this->user['id']
            ];

            $invoiceId = $this->db->insert('billing_invoices', $invoiceData);

            // Update billing events to mark as invoiced
            $eventIds = array_column($events, 'id');
            $this->markEventsAsInvoiced($eventIds, $invoiceId);

            return [
                'success' => true,
                'invoice_id' => $invoiceId,
                'invoice_number' => $invoiceNumber,
                'total_amount' => $totalAmount,
                'line_items_count' => count($lineItems)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate unique invoice number
     */
    private function generateInvoiceNumber($year, $month)
    {
        $prefix = sprintf('INV-%04d%02d-', $year, $month);
        
        // Get the next sequence number for this month
        $lastInvoice = $this->db->fetch(
            "SELECT invoice_number FROM billing_invoices 
             WHERE invoice_number LIKE ? 
             ORDER BY invoice_number DESC LIMIT 1",
            [$prefix . '%']
        );

        $sequence = 1;
        if ($lastInvoice) {
            $lastSequence = (int)substr($lastInvoice['invoice_number'], -4);
            $sequence = $lastSequence + 1;
        }

        return $prefix . sprintf('%04d', $sequence);
    }

    /**
     * Mark billing events as invoiced
     */
    private function markEventsAsInvoiced($eventIds, $invoiceId)
    {
        if (empty($eventIds)) {
            return;
        }

        $placeholders = str_repeat('?,', count($eventIds) - 1) . '?';
        $params = array_merge($eventIds, [$invoiceId]);

        $this->db->execute(
            "UPDATE billing_events 
             SET billing_status = 'invoiced', invoice_id = ? 
             WHERE id IN ({$placeholders})",
            array_merge([$invoiceId], $eventIds)
        );
    }

    /**
     * Get client's current month running total
     */
    public function getClientRunningTotal($clientId, $year = null, $month = null)
    {
        $year = $year ?? date('Y');
        $month = $month ?? date('n');
        
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));

        $result = $this->db->fetch(
            "SELECT 
                COUNT(*) as event_count,
                SUM(total_amount) as total_amount
             FROM billing_events 
             WHERE client_id = ? 
             AND performed_at BETWEEN ? AND ?
             AND billing_status = 'pending'",
            [$clientId, $startDate, $endDate]
        );

        return [
            'period' => sprintf('%04d-%02d', $year, $month),
            'event_count' => (int)$result['event_count'],
            'total_amount' => (float)$result['total_amount']
        ];
    }

    /**
     * Create a new service rate
     */
    public function createServiceRate($data)
    {
        try {
            // Check if service code already exists
            $existing = $this->db->fetch(
                "SELECT id FROM billing_rates WHERE service_code = ?",
                [$data['service_code']]
            );

            if ($existing) {
                throw new Exception("Service code '{$data['service_code']}' already exists");
            }

            // Insert new rate
            $rateData = [
                'service_code' => $data['service_code'],
                'service_name' => $data['service_name'],
                'service_description' => $data['service_description'] ?? '',
                'rate' => (float)$data['rate'],
                'unit' => $data['unit'],
                'category' => $data['category'],
                'is_active' => $data['is_active'] ?? 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $rateId = $this->db->insert('billing_rates', $rateData);
            return $rateId;

        } catch (Exception $e) {
            throw new Exception("Failed to create service rate: " . $e->getMessage());
        }
    }

    /**
     * Update an existing service rate
     */
    public function updateServiceRate($rateId, $data)
    {
        try {
            // Check if rate exists
            $existing = $this->db->fetch(
                "SELECT * FROM billing_rates WHERE id = ?",
                [$rateId]
            );

            if (!$existing) {
                throw new Exception("Service rate not found");
            }

            // Check if service code is being changed and if it conflicts
            if ($data['service_code'] !== $existing['service_code']) {
                $codeExists = $this->db->fetch(
                    "SELECT id FROM billing_rates WHERE service_code = ? AND id != ?",
                    [$data['service_code'], $rateId]
                );

                if ($codeExists) {
                    throw new Exception("Service code '{$data['service_code']}' already exists");
                }
            }

            // Update rate
            $updateData = [
                'service_code' => $data['service_code'],
                'service_name' => $data['service_name'],
                'service_description' => $data['service_description'] ?? '',
                'rate' => (float)$data['rate'],
                'unit' => $data['unit'],
                'category' => $data['category'],
                'is_active' => $data['is_active'] ?? 1,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = $this->db->update('billing_rates', $updateData, ['id' => $rateId]);
            return $result;

        } catch (Exception $e) {
            throw new Exception("Failed to update service rate: " . $e->getMessage());
        }
    }

    /**
     * Toggle service rate active status
     */
    public function toggleServiceRateStatus($rateId, $isActive)
    {
        try {
            $result = $this->db->update(
                'billing_rates',
                [
                    'is_active' => $isActive ? 1 : 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                ['id' => $rateId]
            );

            return $result;

        } catch (Exception $e) {
            throw new Exception("Failed to toggle service rate status: " . $e->getMessage());
        }
    }

    /**
     * Get all service rates (including inactive ones for admin)
     */
    public function getAllServiceRatesForAdmin($category = null)
    {
        $sql = "SELECT * FROM billing_rates WHERE 1=1";
        $params = [];

        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }

        $sql .= " ORDER BY category, service_name";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Delete a service rate
     */
    public function deleteServiceRate($rateId)
    {
        try {
            $result = $this->db->delete('billing_rates', ['id' => $rateId]);
            return $result;

        } catch (Exception $e) {
            throw new Exception("Failed to delete service rate: " . $e->getMessage());
        }
    }

    /**
     * Check if service rate is being used in billing events
     */
    public function isServiceRateInUse($rateId)
    {
        try {
            // Get the service code for this rate
            $rate = $this->db->fetch("SELECT service_code FROM billing_rates WHERE id = ?", [$rateId]);

            if (!$rate) {
                return false;
            }

            // Check if any billing events use this service code
            $usage = $this->db->fetch(
                "SELECT COUNT(*) as count FROM billing_events WHERE service_code = ?",
                [$rate['service_code']]
            );

            return (int)$usage['count'] > 0;

        } catch (Exception $e) {
            // If there's an error checking usage, assume it's in use to be safe
            return true;
        }
    }

    /**
     * Update user context
     */
    public function updateUserContext($user, $company = null)
    {
        $this->user = $user;
        $this->company = $company;
    }
}
