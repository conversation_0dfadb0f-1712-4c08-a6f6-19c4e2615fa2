<?php
/**
 * Debug Billing System Integration with Application
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

// Create a mock user session for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['company_id'] = 1;
    $_SESSION['user_role'] = 'super_admin';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['logged_in'] = true;
}

echo "<h1>🔗 Billing System Integration Analysis</h1>";

try {
    $db = App\Core\Database::getInstance();
    
    // Step 1: Check if BillingEventLogger exists and is accessible
    echo "<h2>Step 1: Billing Event Logger Analysis</h2>";
    
    if (class_exists('App\Services\BillingEventLogger')) {
        echo "<p>✅ BillingEventLogger class exists</p>";
        
        // Test if we can call static methods
        try {
            $reflection = new ReflectionClass('App\Services\BillingEventLogger');
            $methods = $reflection->getMethods(ReflectionMethod::IS_STATIC | ReflectionMethod::IS_PUBLIC);
            
            echo "<p>✅ Available logging methods:</p>";
            echo "<ul>";
            foreach ($methods as $method) {
                if (strpos($method->getName(), 'log') === 0) {
                    echo "<li><code>{$method->getName()}</code></li>";
                }
            }
            echo "</ul>";
            
        } catch (Exception $e) {
            echo "<p>❌ Error analyzing BillingEventLogger: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ BillingEventLogger class not found</p>";
    }
    
    // Step 2: Check which controllers/services are using billing integration
    echo "<h2>Step 2: Integration Status by Service</h2>";
    
    $integrationStatus = [
        'BoxHandlingService' => [
            'file' => 'src/Services/BoxHandlingService.php',
            'expected_events' => ['box.handling', 'box.barcode_generated', 'box.registration'],
            'integrated' => false
        ],
        'BundleHandlingService' => [
            'file' => 'src/Services/BundleHandlingService.php', 
            'expected_events' => ['bundle.handling', 'bundle.created'],
            'integrated' => false
        ],
        'DocumentController' => [
            'file' => 'src/Controllers/DocumentController.php',
            'expected_events' => ['document.upload', 'document.processing'],
            'integrated' => false
        ],
        'IntakeController' => [
            'file' => 'src/Controllers/IntakeController.php',
            'expected_events' => ['intake.new', 'intake.processing'],
            'integrated' => false
        ],
        'SearchService' => [
            'file' => 'src/Services/SearchService.php',
            'expected_events' => ['search.box', 'search.bundle', 'search.document'],
            'integrated' => false
        ],
        'BarcodeManagementController' => [
            'file' => 'src/Controllers/BarcodeManagementController.php',
            'expected_events' => ['barcode.generated'],
            'integrated' => false
        ]
    ];
    
    // Check each service for billing integration
    foreach ($integrationStatus as $serviceName => &$status) {
        $filePath = APP_ROOT . '/' . $status['file'];
        
        if (file_exists($filePath)) {
            $fileContent = file_get_contents($filePath);
            
            // Check if BillingEventLogger is imported
            $hasImport = strpos($fileContent, 'use App\Services\BillingEventLogger') !== false;
            
            // Check if any billing methods are called
            $hasCalls = strpos($fileContent, 'BillingEventLogger::') !== false;
            
            $status['integrated'] = $hasImport && $hasCalls;
            $status['has_import'] = $hasImport;
            $status['has_calls'] = $hasCalls;
            $status['exists'] = true;
        } else {
            $status['exists'] = false;
        }
    }
    
    // Display integration status
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Service/Controller</th><th>File Exists</th><th>Has Import</th><th>Has Calls</th><th>Status</th><th>Expected Events</th></tr>";
    
    foreach ($integrationStatus as $serviceName => $status) {
        $statusIcon = $status['integrated'] ? '✅ Integrated' : '❌ Not Integrated';
        $statusColor = $status['integrated'] ? '#d4edda' : '#f8d7da';
        
        echo "<tr style='background: {$statusColor};'>";
        echo "<td><strong>{$serviceName}</strong></td>";
        echo "<td>" . ($status['exists'] ? '✅' : '❌') . "</td>";
        echo "<td>" . ($status['has_import'] ?? false ? '✅' : '❌') . "</td>";
        echo "<td>" . ($status['has_calls'] ?? false ? '✅' : '❌') . "</td>";
        echo "<td>{$statusIcon}</td>";
        echo "<td>" . implode(', ', $status['expected_events']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 3: Check actual billing events in database
    echo "<h2>Step 3: Actual Billing Events Analysis</h2>";
    
    $eventTypes = $db->fetchAll("
        SELECT 
            service_code,
            event_type,
            COUNT(*) as event_count,
            MIN(performed_at) as first_event,
            MAX(performed_at) as last_event,
            SUM(total_amount) as total_revenue
        FROM billing_events 
        GROUP BY service_code, event_type 
        ORDER BY event_count DESC
    ");
    
    if (empty($eventTypes)) {
        echo "<p>❌ <strong>No billing events found in database!</strong></p>";
        echo "<p>This indicates that the billing integration is not working or no services have been used yet.</p>";
    } else {
        echo "<p>✅ Found " . count($eventTypes) . " different event types</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>Service Code</th><th>Event Type</th><th>Count</th><th>First Event</th><th>Last Event</th><th>Revenue</th></tr>";
        
        foreach ($eventTypes as $event) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($event['service_code']) . "</td>";
            echo "<td>" . htmlspecialchars($event['event_type']) . "</td>";
            echo "<td>" . $event['event_count'] . "</td>";
            echo "<td>" . date('M j, Y', strtotime($event['first_event'])) . "</td>";
            echo "<td>" . date('M j, Y', strtotime($event['last_event'])) . "</td>";
            echo "<td>$" . number_format($event['total_revenue'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 4: Test billing integration manually
    echo "<h2>Step 4: Manual Integration Test</h2>";
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_integration'])) {
        try {
            // Get a test client
            $client = $db->fetch("SELECT id, name FROM companies WHERE type = 'client' LIMIT 1");
            
            if (!$client) {
                throw new Exception("No client found for testing");
            }
            
            $testType = $_POST['test_type'];
            $result = false;
            
            switch ($testType) {
                case 'intake':
                    $result = App\Services\BillingEventLogger::logIntakeCreated(
                        $client['id'], 
                        999, 
                        'TEST-INTAKE-' . date('Ymd-His')
                    );
                    break;
                    
                case 'box_handling':
                    $result = App\Services\BillingEventLogger::logBoxHandling(
                        $client['id'], 
                        999, 
                        'test', 
                        'TEST-BOX-' . date('Ymd-His')
                    );
                    break;
                    
                case 'search':
                    $result = App\Services\BillingEventLogger::logSearch(
                        $client['id'], 
                        'box', 
                        999, 
                        'TEST-SEARCH-' . date('Ymd-His')
                    );
                    break;
                    
                case 'bundle_handling':
                    $result = App\Services\BillingEventLogger::logBundleHandling(
                        $client['id'], 
                        999, 
                        'test', 
                        'TEST-BUNDLE-' . date('Ymd-His')
                    );
                    break;
            }
            
            if ($result) {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "✅ <strong>Integration test successful!</strong> {$testType} event logged for client: {$client['name']}";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "❌ <strong>Integration test failed!</strong> Could not log {$testType} event.";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ <strong>Integration test error:</strong> " . $e->getMessage();
            echo "</div>";
        }
    }
    
    // Test form
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🧪 Test Billing Integration</h3>";
    echo "<form method='POST'>";
    echo "<div style='margin: 10px 0;'>";
    echo "<label>Test Type: <select name='test_type' style='margin-left: 10px; padding: 5px;'>";
    echo "<option value='intake'>New Intake</option>";
    echo "<option value='box_handling'>Box Handling</option>";
    echo "<option value='search'>Search Operation</option>";
    echo "<option value='bundle_handling'>Bundle Handling</option>";
    echo "</select></label>";
    echo "</div>";
    echo "<button type='submit' name='test_integration' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>🧪 Test Integration</button>";
    echo "</form>";
    echo "</div>";
    
    // Step 5: Integration recommendations
    echo "<h2>Step 5: Integration Recommendations</h2>";
    
    $notIntegrated = array_filter($integrationStatus, function($status) {
        return !$status['integrated'] && $status['exists'];
    });
    
    if (!empty($notIntegrated)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ Services Missing Billing Integration</h3>";
        echo "<p>The following services should be integrated with billing but are not:</p>";
        echo "<ul>";
        foreach ($notIntegrated as $serviceName => $status) {
            echo "<li><strong>{$serviceName}</strong> - Expected events: " . implode(', ', $status['expected_events']) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Summary
    $integratedCount = count(array_filter($integrationStatus, function($s) { return $s['integrated']; }));
    $totalCount = count($integrationStatus);
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📊 Integration Summary</h3>";
    echo "<p><strong>Integrated Services:</strong> {$integratedCount} / {$totalCount}</p>";
    echo "<p><strong>Integration Status:</strong> " . round(($integratedCount / $totalCount) * 100) . "%</p>";
    
    if ($integratedCount == $totalCount) {
        echo "<p>✅ <strong>All services are properly integrated with billing!</strong></p>";
    } else {
        echo "<p>⚠️ <strong>Some services need billing integration.</strong></p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}

?>

<h2>🎯 Next Steps</h2>
<div style="background: #cce5ff; border: 1px solid #99ccff; padding: 15px; border-radius: 5px;">
    <h3>To Complete Billing Integration:</h3>
    <ol>
        <li><strong>Add missing imports</strong> - Add <code>use App\Services\BillingEventLogger;</code> to controllers/services</li>
        <li><strong>Add billing calls</strong> - Call appropriate logging methods when services are used</li>
        <li><strong>Test integration</strong> - Use the test form above to verify billing events are logged</li>
        <li><strong>Monitor billing events</strong> - Check that real usage creates billing events</li>
    </ol>
</div>

<hr>
<p><a href="<?= url('/app/billing') ?>">← Back to Billing Dashboard</a></p>
<p><a href="debug-billing-rates-connection.php">🔍 Check Billing Rates Connection</a></p>
