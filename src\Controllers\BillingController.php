<?php

namespace App\Controllers;

use App\Services\ServiceFactory;

/**
 * Billing Controller
 * 
 * Handles billing operations, invoice generation, and client billing dashboard
 */
class BillingController extends BaseController
{
    private $billingService;

    public function __construct()
    {
        parent::__construct();
        
        // Initialize service factory with current user context
        ServiceFactory::initialize($this->db, $this->user, $this->company);
        $this->billingService = ServiceFactory::getBillingService();
    }

    /**
     * Billing dashboard - shows overview of billing status
     */
    public function index()
    {
        $this->requireAuth();
        
        try {
            // Get current month stats
            $currentYear = date('Y');
            $currentMonth = date('n');
            
            // Get all clients for this company
            $clients = $this->db->fetchAll(
                "SELECT id, name FROM companies WHERE id != ? ORDER BY name",
                [$this->company['id']]
            );

            $clientStats = [];
            foreach ($clients as $client) {
                $runningTotal = $this->billingService->getClientRunningTotal($client['id'], $currentYear, $currentMonth);
                $clientStats[] = [
                    'client' => $client,
                    'running_total' => $runningTotal
                ];
            }

            // Get recent invoices
            $recentInvoices = $this->db->fetchAll(
                "SELECT bi.*, c.name as client_name 
                 FROM billing_invoices bi
                 JOIN companies c ON bi.client_id = c.id
                 WHERE bi.company_id = ?
                 ORDER BY bi.created_at DESC
                 LIMIT 10",
                [$this->company['id']]
            );

            $this->view('billing/index', [
                'client_stats' => $clientStats,
                'recent_invoices' => $recentInvoices,
                'current_period' => sprintf('%04d-%02d', $currentYear, $currentMonth)
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error loading billing dashboard: ' . $e->getMessage());
            $this->redirect('/app/dashboard');
        }
    }

    /**
     * Show client billing details
     */
    public function clientBilling($clientId)
    {
        $this->requireAuth();
        
        try {
            // Get client information
            $client = $this->db->fetch("SELECT * FROM companies WHERE id = ?", [$clientId]);
            if (!$client) {
                throw new \Exception("Client not found");
            }

            // Get current month running total
            $runningTotal = $this->billingService->getClientRunningTotal($clientId);

            // Get recent billing events
            $startDate = date('Y-m-01');
            $endDate = date('Y-m-t');
            $recentEvents = $this->billingService->getClientBillingEvents($clientId, $startDate, $endDate);

            // Get client invoices
            $invoices = $this->db->fetchAll(
                "SELECT * FROM billing_invoices 
                 WHERE client_id = ? AND company_id = ?
                 ORDER BY invoice_date DESC
                 LIMIT 20",
                [$clientId, $this->company['id']]
            );

            $this->view('billing/client', [
                'client' => $client,
                'running_total' => $runningTotal,
                'recent_events' => $recentEvents,
                'invoices' => $invoices
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error loading client billing: ' . $e->getMessage());
            $this->redirect('/app/billing');
        }
    }

    /**
     * Generate monthly invoice for a client
     */
    public function generateInvoice()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/app/billing');
            return;
        }

        try {
            $data = $this->validate($_POST, [
                'client_id' => 'required|integer',
                'year' => 'required|integer|min:2020|max:2030',
                'month' => 'required|integer|min:1|max:12'
            ]);

            $result = $this->billingService->generateMonthlyInvoice(
                $data['client_id'],
                $data['year'],
                $data['month']
            );

            if ($result['success']) {
                $this->setFlashMessage('success', 
                    "Invoice {$result['invoice_number']} generated successfully. Total: $" . 
                    number_format($result['total_amount'], 2)
                );
                $this->redirect('/app/billing/invoice/' . $result['invoice_id']);
            } else {
                throw new \Exception($result['error']);
            }

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error generating invoice: ' . $e->getMessage());
            $this->redirect('/app/billing');
        }
    }

    /**
     * View invoice details
     */
    public function viewInvoice($invoiceId)
    {
        $this->requireAuth();
        
        try {
            // Get invoice with client information
            $invoice = $this->db->fetch(
                "SELECT bi.*, c.name as client_name, c.address as client_address,
                        c.email as client_email, c.phone as client_phone
                 FROM billing_invoices bi
                 JOIN companies c ON bi.client_id = c.id
                 WHERE bi.id = ? AND bi.company_id = ?",
                [$invoiceId, $this->company['id']]
            );

            if (!$invoice) {
                throw new \Exception("Invoice not found");
            }

            // Decode line items
            $invoice['line_items'] = json_decode($invoice['line_items'], true);

            // Get related billing events
            $events = $this->db->fetchAll(
                "SELECT be.*, br.service_name
                 FROM billing_events be
                 JOIN billing_rates br ON be.service_code = br.service_code
                 WHERE be.invoice_id = ?
                 ORDER BY be.performed_at",
                [$invoiceId]
            );

            $this->view('billing/invoice', [
                'invoice' => $invoice,
                'events' => $events
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error loading invoice: ' . $e->getMessage());
            $this->redirect('/app/billing');
        }
    }

    /**
     * Mark invoice as paid
     */
    public function markPaid($invoiceId)
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/app/billing');
            return;
        }

        try {
            $data = $this->validate($_POST, [
                'payment_date' => 'required|date',
                'payment_method' => 'required|max:50',
                'payment_reference' => 'max:100',
                'notes' => 'max:1000'
            ]);

            // Update invoice status
            $this->db->execute(
                "UPDATE billing_invoices 
                 SET payment_status = 'paid', 
                     payment_date = ?, 
                     payment_method = ?, 
                     payment_reference = ?
                 WHERE id = ? AND company_id = ?",
                [
                    $data['payment_date'],
                    $data['payment_method'],
                    $data['payment_reference'] ?? null,
                    $invoiceId,
                    $this->company['id']
                ]
            );

            // Record payment in payments table
            $invoice = $this->db->fetch(
                "SELECT * FROM billing_invoices WHERE id = ?", 
                [$invoiceId]
            );

            if ($invoice) {
                $this->db->insert('billing_payments', [
                    'invoice_id' => $invoiceId,
                    'company_id' => $this->company['id'],
                    'client_id' => $invoice['client_id'],
                    'payment_amount' => $invoice['total_amount'],
                    'payment_date' => $data['payment_date'],
                    'payment_method' => $data['payment_method'],
                    'payment_reference' => $data['payment_reference'] ?? null,
                    'notes' => $data['notes'] ?? null,
                    'recorded_by' => $this->user['id']
                ]);

                // Update billing events status
                $this->db->execute(
                    "UPDATE billing_events 
                     SET billing_status = 'paid' 
                     WHERE invoice_id = ?",
                    [$invoiceId]
                );
            }

            $this->setFlashMessage('success', 'Invoice marked as paid successfully');
            $this->redirect('/app/billing/invoice/' . $invoiceId);

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error marking invoice as paid: ' . $e->getMessage());
            $this->redirect('/app/billing/invoice/' . $invoiceId);
        }
    }

    /**
     * Service rates management
     */
    public function serviceRates()
    {
        $this->requireAuth();

        try {
            // Get all rates (including inactive ones) for admin interface
            $rates = $this->billingService->getAllServiceRatesForAdmin();

            // Group rates by category
            $ratesByCategory = [];
            foreach ($rates as $rate) {
                $ratesByCategory[$rate['category']][] = $rate;
            }

            $this->view('billing/rates', [
                'rates_by_category' => $ratesByCategory
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error loading service rates: ' . $e->getMessage());
            $this->redirect('/app/billing');
        }
    }

    /**
     * API endpoint to log a billing event
     */
    public function logEvent()
    {
        $this->requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        try {
            $data = $this->validate($_POST, [
                'client_id' => 'required|integer',
                'service_code' => 'required|max:50',
                'event_type' => 'required|max:100',
                'quantity' => 'numeric|min:0.01',
                'entity_type' => 'max:50',
                'entity_id' => 'integer',
                'reference_number' => 'max:100',
                'notes' => 'max:1000'
            ]);

            $result = $this->billingService->logServiceEvent($data);

            header('Content-Type: application/json');
            echo json_encode($result);

        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Save service rate (create or update)
     */
    public function saveServiceRate()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/app/billing/rates');
            return;
        }

        try {
            $data = $this->validate($_POST, [
                'service_code' => 'required|max:50',
                'service_name' => 'required|max:255',
                'service_description' => 'max:1000',
                'rate' => 'required|numeric|min:0',
                'unit' => 'required|max:50',
                'category' => 'required|max:50',
                'is_active' => 'boolean'
            ]);

            // Convert checkbox value
            $data['is_active'] = isset($data['is_active']) ? 1 : 0;

            if (!empty($_POST['rate_id'])) {
                // Update existing rate
                $rateId = (int)$_POST['rate_id'];
                $result = $this->billingService->updateServiceRate($rateId, $data);
                $message = 'Service rate updated successfully';
            } else {
                // Create new rate
                $result = $this->billingService->createServiceRate($data);
                $message = 'Service rate created successfully';
            }

            if ($result) {
                $this->setFlashMessage('success', $message);
            } else {
                $this->setFlashMessage('error', 'Failed to save service rate');
            }

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error saving service rate: ' . $e->getMessage());
        }

        $this->redirect('/app/billing/rates');
    }

    /**
     * Toggle service rate active status
     */
    public function toggleServiceRate($rateId)
    {
        $this->requireAuth();

        try {
            $isActive = isset($_GET['active']) ? (bool)$_GET['active'] : false;

            $result = $this->billingService->toggleServiceRateStatus($rateId, $isActive);

            if ($result) {
                $status = $isActive ? 'activated' : 'deactivated';
                $this->setFlashMessage('success', "Service rate {$status} successfully");
            } else {
                $this->setFlashMessage('error', 'Failed to update service rate status');
            }

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error updating service rate: ' . $e->getMessage());
        }

        $this->redirect('/app/billing/rates');
    }

    /**
     * Delete service rate
     */
    public function deleteServiceRate($rateId)
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/app/billing/rates');
            return;
        }

        try {
            // Check if rate is being used in any billing events
            $isUsed = $this->billingService->isServiceRateInUse($rateId);

            if ($isUsed) {
                $this->setFlashMessage('error', 'Cannot delete service rate: it is being used in billing events. You can deactivate it instead.');
            } else {
                $result = $this->billingService->deleteServiceRate($rateId);

                if ($result) {
                    $this->setFlashMessage('success', 'Service rate deleted successfully');
                } else {
                    $this->setFlashMessage('error', 'Failed to delete service rate');
                }
            }

        } catch (\Exception $e) {
            $this->setFlashMessage('error', 'Error deleting service rate: ' . $e->getMessage());
        }

        $this->redirect('/app/billing/rates');
    }
}
