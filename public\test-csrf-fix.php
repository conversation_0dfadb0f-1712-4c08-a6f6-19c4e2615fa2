<?php
/**
 * Test CSRF Token Fix
 */

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = generateToken(32);
}

echo "<h1>🔒 CSRF Token Fix Test</h1>";

echo "<h2>Step 1: CSRF Token Generation</h2>";
if (isset($_SESSION['csrf_token']) && !empty($_SESSION['csrf_token'])) {
    echo "<p>✅ <strong>CSRF Token Generated:</strong> " . substr($_SESSION['csrf_token'], 0, 16) . "...</p>";
} else {
    echo "<p>❌ <strong>CSRF Token Missing</strong></p>";
}

echo "<h2>Step 2: Meta Tag Test</h2>";
echo "<meta name='csrf-token' content='" . ($_SESSION['csrf_token'] ?? '') . "'>";
echo "<p>✅ Meta tag added to page</p>";

echo "<h2>Step 3: JavaScript Token Detection</h2>";
echo "<div id='js-token-test'></div>";

echo "<h2>Step 4: Test Delete Form</h2>";
echo "<p>This form simulates the delete operation:</p>";

echo "<form method='POST' action='" . url('/app/billing/rates/delete/1') . "' style='background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0;'>";
echo "<input type='hidden' name='_token' value='" . ($_SESSION['csrf_token'] ?? '') . "'>";
echo "<input type='hidden' name='rate_id' value='1'>";
echo "<p><strong>Form Action:</strong> " . url('/app/billing/rates/delete/1') . "</p>";
echo "<p><strong>CSRF Token:</strong> " . substr($_SESSION['csrf_token'] ?? '', 0, 16) . "...</p>";
echo "<button type='submit' style='background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"Test the fixed delete functionality?\")'>🧪 Test Fixed Delete</button>";
echo "</form>";

echo "<h2>Step 5: Comparison</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Test</th><th>Before Fix</th><th>After Fix</th></tr>";
echo "<tr><td>CSRF Token in Session</td><td>❌ Missing</td><td>✅ " . (isset($_SESSION['csrf_token']) ? 'Present' : 'Missing') . "</td></tr>";
echo "<tr><td>Meta Tag Content</td><td>❌ Empty</td><td>✅ " . (!empty($_SESSION['csrf_token']) ? 'Populated' : 'Empty') . "</td></tr>";
echo "<tr><td>JavaScript Detection</td><td>❌ Not Found</td><td><span id='js-result'>Testing...</span></td></tr>";
echo "</table>";

?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test JavaScript token detection
    const tokenTest = document.getElementById('js-token-test');
    const jsResult = document.getElementById('js-result');
    
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    
    if (csrfToken && csrfToken.getAttribute('content')) {
        const tokenValue = csrfToken.getAttribute('content');
        tokenTest.innerHTML = `
            <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px;">
                ✅ <strong>JavaScript can access CSRF token:</strong> ${tokenValue.substring(0, 16)}...
            </div>
        `;
        jsResult.innerHTML = '✅ Found';
        jsResult.style.color = 'green';
    } else {
        tokenTest.innerHTML = `
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px;">
                ❌ <strong>JavaScript cannot access CSRF token</strong>
            </div>
        `;
        jsResult.innerHTML = '❌ Not Found';
        jsResult.style.color = 'red';
    }
    
    console.log('CSRF Token Test:', csrfToken ? csrfToken.getAttribute('content') : 'Not found');
});
</script>

<h2>Step 6: Instructions</h2>
<ol>
    <li><strong>Verify all tests show ✅</strong> - This means CSRF tokens are working</li>
    <li><strong>Try the "Test Fixed Delete" button</strong> - This should work without the "error" message</li>
    <li><strong>Go back to the rates page</strong> and try deleting a rate normally</li>
    <li><strong>Check browser console</strong> for any remaining errors</li>
</ol>

<div style="background: #cce5ff; border: 1px solid #99ccff; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3>🎯 Expected Results:</h3>
    <ul>
        <li>✅ CSRF token should be generated and available</li>
        <li>✅ JavaScript should detect the token</li>
        <li>✅ Delete operations should work without "error" message</li>
        <li>✅ You should see specific success/error messages instead of generic "error"</li>
    </ul>
</div>

<hr>
<p><a href="<?= url('/app/billing/rates') ?>">← Back to Service Rates</a></p>
