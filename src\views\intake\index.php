<?php
$title = 'Document Intake';
ob_start();
?>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.bg-gray-25 {
    background-color: #fafafa;
}

/* Table hover effects */
tbody tr:hover {
    background-color: #f8fafc !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.15s ease-in-out;
}

/* Priority badges animation */
.inline-flex.items-center.px-3.py-1.rounded-full {
    transition: all 0.2s ease-in-out;
}

.inline-flex.items-center.px-3.py-1.rounded-full:hover {
    transform: scale(1.05);
}

/* Action buttons hover effects */
.inline-flex.items-center.px-3.py-1\.5 {
    transition: all 0.2s ease-in-out;
}

.inline-flex.items-center.px-3.py-1\.5:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>

<!-- Document Intake Dashboard -->
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Page Header -->
        <div class="mb-10">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">
                        Document Intake
                    </h1>
                    <p class="text-lg text-gray-600">Streamline your document processing workflow</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>Process documents efficiently</span>
                    </div>
                    <a href="<?= url('/app/intake/create') ?>" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Intake Entry
                    </a>
                </div>
            </div>
        </div>

        <!-- Intake Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
            <!-- Pending Intakes -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-orange-400 to-orange-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-orange-200 text-sm font-medium">Pending</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-orange-100 mb-1">Pending Intake</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['pending']) ?></p>
                    <p class="text-xs text-orange-100 mt-1">awaiting processing</p>
                </div>
            </div>

            <!-- Processing Intakes -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-blue-200 text-sm font-medium">Active</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-blue-100 mb-1">Processing</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['processing']) ?></p>
                    <p class="text-xs text-blue-100 mt-1">being organized</p>
                </div>
            </div>

            <!-- Completed Today -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-green-400 to-green-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-green-200 text-sm font-medium">Today</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-green-100 mb-1">Completed Today</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['completed_today']) ?></p>
                    <p class="text-xs text-green-100 mt-1">processed items</p>
                </div>
            </div>

            <!-- Total This Month -->
            <div class="group relative overflow-hidden bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="relative">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="flex items-center space-x-1">
                            <span class="text-purple-200 text-sm font-medium">Month</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-purple-100 mb-1">Total This Month</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format($stats['total_this_month']) ?></p>
                    <p class="text-xs text-purple-100 mt-1">intake entries</p>
                </div>
            </div>
        </div>



        <!-- Main Content -->
        <div class="space-y-8">
            <!-- Pending Intakes Table -->
            <div class="bg-white rounded-xl border border-gray-200 shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-900">Pending Intakes</h2>
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                                <?= count($pendingIntakes) ?> items
                            </span>
                            <div class="flex items-center space-x-2">
                                <button onclick="refreshTable()" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors" title="Refresh">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </button>
                                <button onclick="toggleView()" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors" title="Toggle View">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (empty($pendingIntakes)): ?>
                    <div class="text-center py-16">
                        <div class="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">All caught up!</h3>
                        <p class="text-gray-600 mb-6">No pending intake items at the moment.</p>
                        <a href="<?= url('/app/intake/create') ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create New Intake
                        </a>
                    </div>
                <?php else: ?>
                    <!-- Table View -->
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50 border-b border-gray-200">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div class="flex items-center space-x-1">
                                            <span>Reference</span>
                                            <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client & Description</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source & Type</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Documents</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($pendingIntakes as $index => $intake): ?>
                                    <tr class="hover:bg-gray-50 transition-colors duration-150 <?= $index % 2 === 0 ? 'bg-white' : 'bg-gray-25' ?>">
                                        <!-- Reference Number -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-semibold text-gray-900">
                                                        <a href="<?= url('/app/intake/' . $intake['id']) ?>" class="hover:text-blue-600 transition-colors">
                                                            <?= e($intake['reference_number']) ?>
                                                        </a>
                                                    </div>
                                                    <div class="text-xs text-gray-500">ID: <?= $intake['id'] ?></div>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Client & Description -->
                                        <td class="px-6 py-4">
                                            <div class="max-w-xs">
                                                <div class="text-sm font-medium text-gray-900 mb-1">
                                                    <?= e($intake['client_name']) ?>
                                                    <?php if (!empty($intake['client_id'])): ?>
                                                        <span class="text-xs text-gray-500">(<?= e($intake['client_id']) ?>)</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-sm text-gray-600 line-clamp-2" title="<?= e($intake['description']) ?>">
                                                    <?= e(strlen($intake['description']) > 60 ? substr($intake['description'], 0, 60) . '...' : $intake['description']) ?>
                                                </div>
                                                <?php if (!empty($intake['department'])): ?>
                                                    <div class="text-xs text-gray-500 mt-1">Dept: <?= e($intake['department']) ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </td>

                                        <!-- Source & Type -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                <div class="font-medium"><?= ucfirst(str_replace('_', ' ', $intake['source'])) ?></div>
                                                <div class="text-gray-600"><?= ucfirst($intake['document_type']) ?></div>
                                                <?php if (!empty($intake['sensitivity_level'])): ?>
                                                    <div class="text-xs text-gray-500 mt-1">
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                                            <?php
                                                            switch ($intake['sensitivity_level']) {
                                                                case 'restricted': echo 'bg-red-100 text-red-800'; break;
                                                                case 'confidential': echo 'bg-orange-100 text-orange-800'; break;
                                                                case 'internal': echo 'bg-yellow-100 text-yellow-800'; break;
                                                                default: echo 'bg-green-100 text-green-800';
                                                            }
                                                            ?>">
                                                            <?= ucfirst($intake['sensitivity_level']) ?>
                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>

                                        <!-- Priority -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                                <?php
                                                switch ($intake['priority']) {
                                                    case 'urgent': echo 'bg-red-100 text-red-800'; break;
                                                    case 'high': echo 'bg-orange-100 text-orange-800'; break;
                                                    case 'medium': echo 'bg-yellow-100 text-yellow-800'; break;
                                                    case 'low': echo 'bg-green-100 text-green-800'; break;
                                                }
                                                ?>">
                                                <?php
                                                switch ($intake['priority']) {
                                                    case 'urgent': echo '🔴 Urgent'; break;
                                                    case 'high': echo '🟠 High'; break;
                                                    case 'medium': echo '🟡 Medium'; break;
                                                    case 'low': echo '🟢 Low'; break;
                                                }
                                                ?>
                                            </span>
                                        </td>

                                        <!-- Status -->
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                                <?= $intake['status'] === 'pending' ? 'bg-orange-100 text-orange-800' : 'bg-blue-100 text-blue-800' ?>">
                                                <?php
                                                switch ($intake['status']) {
                                                    case 'pending': echo '⏳ Pending'; break;
                                                    case 'processing': echo '⚙️ Processing'; break;
                                                    case 'completed': echo '✅ Completed'; break;
                                                    default: echo ucfirst($intake['status']);
                                                }
                                                ?>
                                            </span>
                                        </td>

                                        <!-- Documents -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div class="flex items-center space-x-2">
                                                <div class="text-center">
                                                    <div class="text-lg font-semibold text-blue-600"><?= $intake['expected_count'] ?></div>
                                                    <div class="text-xs text-gray-500">Expected</div>
                                                </div>
                                                <?php if (isset($intake['document_count']) && $intake['document_count'] > 0): ?>
                                                    <div class="text-gray-400">→</div>
                                                    <div class="text-center">
                                                        <div class="text-lg font-semibold text-green-600"><?= $intake['document_count'] ?></div>
                                                        <div class="text-xs text-gray-500">Uploaded</div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>

                                        <!-- Created -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div>
                                                <div class="font-medium"><?= date('M j, Y', strtotime($intake['created_at'])) ?></div>
                                                <div class="text-gray-500"><?= date('g:i A', strtotime($intake['created_at'])) ?></div>
                                                <?php if (!empty($intake['first_name']) || !empty($intake['last_name'])): ?>
                                                    <div class="text-xs text-gray-500 mt-1">
                                                        by <?= e($intake['first_name'] . ' ' . $intake['last_name']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>

                                        <!-- Actions -->
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                <a href="<?= url('/app/intake/' . $intake['id']) ?>"
                                                   class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 text-xs font-medium rounded-md hover:bg-blue-200 transition-colors"
                                                   title="View Details">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    View
                                                </a>
                                                <?php if ($intake['status'] === 'pending'): ?>
                                                    <button onclick="processIntake(<?= $intake['id'] ?>, '<?= addslashes($intake['reference_number']) ?>')"
                                                            class="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 text-xs font-medium rounded-md hover:bg-green-200 transition-colors"
                                                            title="Process Intake">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        Process
                                                    </button>
                                                    <button onclick="deleteIntake(<?= $intake['id'] ?>, '<?= addslashes($intake['reference_number']) ?>')"
                                                            class="inline-flex items-center px-3 py-1.5 bg-red-100 text-red-700 text-xs font-medium rounded-md hover:bg-red-200 transition-colors"
                                                            title="Delete Intake">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                        Delete
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Table Footer -->
                    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                Showing <span class="font-medium"><?= count($pendingIntakes) ?></span> pending intake<?= count($pendingIntakes) !== 1 ? 's' : '' ?>
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="<?= url('/app/intake?status=all') ?>" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                    View all intakes →
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-xl border border-gray-200 shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-900">Recent Activity</h2>
                        <span class="text-sm text-gray-500">Last 10 items</span>
                    </div>
                </div>
                <div class="p-6">
                    <?php if (empty($recentActivity)): ?>
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No activity yet</h3>
                            <p class="text-gray-600">Intake activity will appear here.</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($recentActivity as $activity): ?>
                                <div class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-200">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <?php
                                            switch ($activity['status']) {
                                                case 'pending':
                                                    echo '<svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                                                    break;
                                                case 'processing':
                                                    echo '<svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>';
                                                    break;
                                                case 'completed':
                                                    echo '<svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                                                    break;
                                                default:
                                                    echo '<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-900">
                                            <span class="font-semibold"><?= e($activity['reference_number']) ?></span>
                                            <span class="text-gray-600">- <?= e($activity['description']) ?></span>
                                        </p>
                                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                                            <span>Status: <?= ucfirst($activity['status']) ?></span>
                                            <span>Priority: <?= ucfirst($activity['priority']) ?></span>
                                            <span><?= date('M j, Y g:i A', strtotime($activity['updated_at'])) ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Refresh table function
function refreshTable() {
    window.location.reload();
}

// Toggle view function (placeholder for future card/table toggle)
function toggleView() {
    showNotification('View toggle feature coming soon!', 'info');
}

// Process intake function
function processIntake(intakeId, referenceNumber) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Process Intake</h3>
                        <p class="text-sm text-gray-600">Change status to processing</p>
                    </div>
                </div>

                <div class="mb-6">
                    <p class="text-gray-700 mb-2">Process intake entry:</p>
                    <p class="font-semibold text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">${referenceNumber}</p>
                    <p class="text-sm text-green-600 mt-2">✅ This will mark the intake as "Processing" and move it to the active queue.</p>
                </div>

                <div class="flex space-x-3">
                    <button onclick="confirmProcess(${intakeId})"
                            class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
                        Start Processing
                    </button>
                    <button onclick="closeProcessModal()"
                            class="flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors font-medium">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// Confirm process action
function confirmProcess(intakeId) {
    const modal = document.querySelector('.fixed.inset-0');
    const processButton = modal.querySelector('button[onclick*="confirmProcess"]');

    // Show loading state
    processButton.innerHTML = `
        <svg class="w-4 h-4 animate-spin inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Processing...
    `;
    processButton.disabled = true;

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `<?= url('/app/intake') ?>/${intakeId}/process`;

    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'PATCH';
    form.appendChild(methodInput);

    // Add CSRF token if available
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = csrfToken.getAttribute('content');
        form.appendChild(tokenInput);
    }

    document.body.appendChild(form);
    form.submit();
}

// Close process modal
function closeProcessModal() {
    const modal = document.querySelector('.fixed.inset-0');
    if (modal) {
        modal.querySelector('div > div').classList.add('scale-95', 'opacity-0');
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 200);
    }
}

// Delete intake function with enhanced confirmation
function deleteIntake(intakeId, referenceNumber) {
    // Create custom confirmation modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Delete Intake Entry</h3>
                        <p class="text-sm text-gray-600">This action cannot be undone</p>
                    </div>
                </div>

                <div class="mb-6">
                    <p class="text-gray-700 mb-2">Are you sure you want to delete intake:</p>
                    <p class="font-semibold text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">${referenceNumber}</p>
                    <p class="text-sm text-red-600 mt-2">⚠️ This will permanently remove the intake entry and any associated documents.</p>
                </div>

                <div class="flex space-x-3">
                    <button onclick="confirmDelete(${intakeId})"
                            class="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium">
                        Delete Permanently
                    </button>
                    <button onclick="closeDeleteModal()"
                            class="flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors font-medium">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Animate in
    setTimeout(() => {
        modal.querySelector('div > div').classList.add('scale-100');
    }, 10);
}

// Confirm delete action
function confirmDelete(intakeId) {
    const modal = document.querySelector('.fixed.inset-0');
    const deleteButton = modal.querySelector('button[onclick*="confirmDelete"]');

    // Show loading state
    deleteButton.innerHTML = `
        <svg class="w-4 h-4 animate-spin inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Deleting...
    `;
    deleteButton.disabled = true;

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `<?= url('/app/intake') ?>/${intakeId}`;

    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'DELETE';
    form.appendChild(methodInput);

    // Add CSRF token if available
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = csrfToken.getAttribute('content');
        form.appendChild(tokenInput);
    }

    document.body.appendChild(form);
    form.submit();
}

// Close delete modal
function closeDeleteModal() {
    const modal = document.querySelector('.fixed.inset-0');
    if (modal) {
        modal.querySelector('div > div').classList.add('scale-95', 'opacity-0');
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 200);
    }
}

// Show notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
